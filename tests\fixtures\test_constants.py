"""
Константы для тестирования PM Searcher Bot
"""

# =======================================
# ТЕСТОВЫЕ ДАННЫЕ ДЛЯ СТОП-СЛОВ
# =======================================

VALID_STOPWORDS = [
    "спам", "реклама", "продажа", "купить", "скидка",
    "test_word", "тест", "проверка",
    "a", "очень_длинное_стоп_слово_для_тестирования"
]

INVALID_STOPWORDS = [
    "", "   ", "\n\t", "слово с пробелами",
    "слово\nс\nпереносами", "слово/с/слешами",
    "слово<с>тегами", "слово\"с\"кавычками",
    "слово;с;точкой;запятой", "слово|с|пайпом",
    "слово\\с\\бэкслешами", "слово:с:двоеточием"
]

# =======================================
# ТЕСТОВЫЕ ФАЙЛЫ ИМПОРТА
# =======================================

VALID_IMPORT_FILES = {
    "simple": "# Комментарий\nспам\nреклама\n",
    "with_comments": "# Только комментарии\n# Еще комментарий\n",
    "mixed": "слово1\nслово2\n# комментарий\nслово3\n",
    "empty_lines": "слово1\n\n\nслово2\n\n",
    "unicode": "тест\nпроверка\nрусский\n"
}

INVALID_IMPORT_FILES = {
    "empty": "",
    "only_spaces": "   \n\t\n   ",
    "invalid_chars": "слово с пробелами\nнормальное_слово\n",
    "too_large": "слово\n" * 10000,
    "binary_data": b"\x00\x01\x02\x03\x04\x05"
}

# =======================================
# ВРЕДОНОСНЫЕ ВХОДНЫЕ ДАННЫЕ
# =======================================

MALICIOUS_INPUTS = [
    # SQL injection
    "'; DROP TABLE users; --",
    "' OR '1'='1",
    "admin'--",
    "1' UNION SELECT * FROM users--",
    
    # XSS
    "<script>alert('xss')</script>",
    "<img src=x onerror=alert('xss')>",
    "javascript:alert('xss')",
    "<svg onload=alert('xss')>",
    
    # Path traversal
    "../../../etc/passwd",
    "..\\..\\..\\windows\\system32\\config\\sam",
    "....//....//....//etc//passwd",
    "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
    
    # Command injection
    "; rm -rf /",
    "| cat /etc/passwd",
    "$(whoami)",
    "`id`",
    "&& echo vulnerable",
    
    # LDAP injection
    "*)(uid=*",
    "admin)(&(password=*))",
    
    # XML injection
    "<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>"
]

# =======================================
# ТЕСТОВЫЕ ПОЛЬЗОВАТЕЛИ
# =======================================

TEST_ADMIN_USER_ID = 987654321
TEST_REGULAR_USER_ID = 123456789
TEST_ANOTHER_USER_ID = 555666777

# =======================================
# ТЕСТОВЫЕ ТЕЛЕФОНЫ
# =======================================

VALID_PHONE_NUMBERS = [
    "+79991234567",
    "+1234567890",
    "+************",
    "+77771234567"
]

INVALID_PHONE_NUMBERS = [
    "79991234567",  # без +
    "+7999123456",  # слишком короткий
    "+799912345678901",  # слишком длинный
    "+7abc1234567",  # содержит буквы
    "",  # пустой
    "   ",  # только пробелы
    "****** 123 45 67"  # с пробелами
]

# =======================================
# ТЕСТОВЫЕ КОДЫ ПОДТВЕРЖДЕНИЯ
# =======================================

VALID_CONFIRMATION_CODES = [
    "12345",
    "67890",
    "00000",
    "99999"
]

INVALID_CONFIRMATION_CODES = [
    "1234",  # слишком короткий
    "123456",  # слишком длинный
    "abcde",  # содержит буквы
    "",  # пустой
    "   ",  # только пробелы
    "12 34"  # с пробелом
]

# =======================================
# ТЕСТОВЫЕ ТЕКСТЫ ДЛЯ ПОИСКА СТОП-СЛОВ
# =======================================

TEXTS_WITH_STOPWORDS = [
    ("Это СПАМ сообщение", ["спам"]),
    ("Продажа товаров со скидкой", ["продажа", "скидка"]),
    ("Хочу купить что-то", ["купить"]),
    ("Реклама нового продукта", ["реклама"]),
    ("спамить рекламировать", ["спам", "реклама"])  # частичные совпадения
]

TEXTS_WITHOUT_STOPWORDS = [
    "Обычное сообщение без стоп-слов",
    "Привет, как дела?",
    "Встретимся завтра в кафе",
    "Отличная погода сегодня",
    "Спасибо за помощь!"
]

# =======================================
# ГРАНИЧНЫЕ СЛУЧАИ
# =======================================

EDGE_CASES = {
    "empty_text": "",
    "only_spaces": "   \n\t\n   ",
    "very_long_text": "слово " * 10000,
    "unicode_text": "🚀 Тест с эмодзи 🎉",
    "mixed_case": "СПАМ спам Спам СпАм",
    "special_chars": "!@#$%^&*()_+-=[]{}|;':\",./<>?",
    "numbers_only": "1234567890",
    "single_char": "a"
}

# =======================================
# НАСТРОЙКИ ПРОИЗВОДИТЕЛЬНОСТИ
# =======================================

PERFORMANCE_LIMITS = {
    "max_stopwords_count": 10000,
    "max_text_length": 100000,
    "max_file_size": 1024 * 1024,  # 1MB
    "max_import_words": 1000
}

# =======================================
# НАСТРОЙКИ БЕЗОПАСНОСТИ
# =======================================

SECURITY_PATTERNS = {
    "min_word_length": 1,
    "max_word_length": 50,
    "allowed_file_extensions": [".txt"],
    "forbidden_chars": ['<', '>', ':', '"', '|', '?', '*', '/', '\\'],
    "dangerous_patterns": [
        r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>',
        r'(union\s+select|insert\s+into|delete\s+from|drop\s+table)',
        r'[;&|`$(){}\[\]<>]',
        r'\.\.[\\/]'
    ]
}

# =======================================
# КОНСТАНТЫ ДЛЯ ТЕСТОВ БЕЗОПАСНОСТИ
# =======================================

SQL_INJECTION_PAYLOADS = [
    "'; DROP TABLE users; --",
    "' OR '1'='1",
    "admin'--",
    "1' UNION SELECT * FROM users--",
    "'; INSERT INTO users VALUES ('hacker', 'password'); --",
    "' OR 1=1 --",
    "' UNION SELECT username, password FROM users --",
    "'; DELETE FROM users WHERE '1'='1'; --"
]

XSS_PAYLOADS = [
    "<script>alert('xss')</script>",
    "<img src=x onerror=alert('xss')>",
    "javascript:alert('xss')",
    "<svg onload=alert('xss')>",
    "<iframe src=javascript:alert('xss')></iframe>",
    "<body onload=alert('xss')>",
    "<input onfocus=alert('xss') autofocus>",
    "<select onfocus=alert('xss') autofocus>"
]

PATH_TRAVERSAL_PAYLOADS = [
    "../../../etc/passwd",
    "..\\..\\..\\windows\\system32\\config\\sam",
    "....//....//....//etc//passwd",
    "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
    "..%2F..%2F..%2Fetc%2Fpasswd",
    "..%5c..%5c..%5cwindows%5csystem32%5cconfig%5csam",
    "....\\\\....\\\\....\\\\windows\\\\system32\\\\config\\\\sam",
    "%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows%5csystem32%5cconfig%5csam"
]

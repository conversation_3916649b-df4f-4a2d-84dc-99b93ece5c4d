"""
Менеджер ролей пользователей
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Literal
from .settings import DATA_DIR

logger = logging.getLogger(__name__)

# Путь к файлу конфигурации ролей
ROLES_CONFIG_FILE = DATA_DIR / "roles_config.json"

# Типы ролей
UserRole = Literal["admin", "teamlead", "none"]

class RolesManager:
    """Менеджер ролей пользователей"""
    
    def __init__(self):
        self._config = self._load_config()
    
    def _load_config(self) -> Dict:
        """Загрузка конфигурации ролей из файла"""
        try:
            if ROLES_CONFIG_FILE.exists():
                with open(ROLES_CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"Конфигурация ролей загружена из {ROLES_CONFIG_FILE}")
                return config
            else:
                # Конфигурация по умолчанию
                default_config = {
                    "users": {},  # user_id: {"role": "admin|teamlead|none", "granted_by": admin_id, "granted_at": timestamp}
                    "last_updated": None,
                    "updated_by": None
                }
                self._save_config(default_config)
                logger.info("Создана конфигурация ролей по умолчанию")
                return default_config
        except Exception as e:
            logger.error(f"Ошибка загрузки конфигурации ролей: {e}")
            # Возвращаем конфигурацию по умолчанию при ошибке
            return {
                "users": {},
                "last_updated": None,
                "updated_by": None
            }
    
    def _save_config(self, config: Dict = None) -> bool:
        """Сохранение конфигурации ролей в файл"""
        try:
            if config is None:
                config = self._config
            
            # Обновляем метаданные
            config["last_updated"] = datetime.now().isoformat()
            
            # Создаем директорию если не существует
            ROLES_CONFIG_FILE.parent.mkdir(parents=True, exist_ok=True)
            
            with open(ROLES_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Конфигурация ролей сохранена в {ROLES_CONFIG_FILE}")
            return True
        except Exception as e:
            logger.error(f"Ошибка сохранения конфигурации ролей: {e}")
            return False
    
    def get_user_role(self, user_id: int) -> UserRole:
        """
        Получение роли пользователя
        
        Args:
            user_id (int): ID пользователя
            
        Returns:
            UserRole: Роль пользователя ("admin", "teamlead", "none")
        """
        try:
            # Сначала проверяем, является ли пользователь администратором из .env
            from .environment import env_config
            if env_config.is_admin(user_id):
                return "admin"
            
            # Затем проверяем роли из конфигурации
            user_data = self._config.get("users", {}).get(str(user_id))
            if user_data:
                return user_data.get("role", "none")
            
            return "none"
        except Exception as e:
            logger.error(f"Ошибка получения роли пользователя {user_id}: {e}")
            return "none"
    
    def set_user_role(self, user_id: int, role: UserRole, granted_by: int) -> bool:
        """
        Установка роли пользователя
        
        Args:
            user_id (int): ID пользователя
            role (UserRole): Роль ("admin", "teamlead", "none")
            granted_by (int): ID администратора, который выдал роль
            
        Returns:
            bool: True если роль успешно установлена
        """
        try:
            # Проверяем, что роль валидна
            if role not in ["admin", "teamlead", "none"]:
                logger.error(f"Некорректная роль: {role}")
                return False
            
            # Проверяем, что выдающий роль является администратором
            if self.get_user_role(granted_by) != "admin":
                logger.error(f"Пользователь {granted_by} не является администратором")
                return False
            
            if "users" not in self._config:
                self._config["users"] = {}
            
            # Если роль "none", удаляем пользователя из конфигурации
            if role == "none":
                if str(user_id) in self._config["users"]:
                    del self._config["users"][str(user_id)]
            else:
                # Устанавливаем роль
                self._config["users"][str(user_id)] = {
                    "role": role,
                    "granted_by": granted_by,
                    "granted_at": datetime.now().isoformat()
                }
            
            self._config["updated_by"] = granted_by
            
            success = self._save_config()
            if success:
                logger.info(f"Роль {role} установлена для пользователя {user_id} администратором {granted_by}")
            
            return success
        except Exception as e:
            logger.error(f"Ошибка установки роли {role} для пользователя {user_id}: {e}")
            return False
    
    def is_admin(self, user_id: int) -> bool:
        """Проверка является ли пользователь администратором"""
        return self.get_user_role(user_id) == "admin"
    
    def is_teamlead(self, user_id: int) -> bool:
        """Проверка является ли пользователь тимлидом"""
        return self.get_user_role(user_id) == "teamlead"
    
    def has_access(self, user_id: int) -> bool:
        """Проверка имеет ли пользователь доступ к боту (админ или тимлид)"""
        role = self.get_user_role(user_id)
        return role in ["admin", "teamlead"]
    
    def get_all_users_with_roles(self) -> Dict[str, Dict]:
        """
        Получение всех пользователей с ролями
        
        Returns:
            Dict: Словарь пользователей с их ролями
        """
        try:
            # Добавляем администраторов из .env
            from .environment import env_config
            result = {}
            
            # Сначала добавляем администраторов из .env
            for admin_id in env_config.ADMIN_CHAT_IDS:
                result[str(admin_id)] = {
                    "role": "admin",
                    "granted_by": "system",
                    "granted_at": "system",
                    "source": "env"
                }
            
            # Затем добавляем пользователей из конфигурации
            for user_id, user_data in self._config.get("users", {}).items():
                result[user_id] = user_data.copy()
                result[user_id]["source"] = "config"
            
            return result
        except Exception as e:
            logger.error(f"Ошибка получения пользователей с ролями: {e}")
            return {}
    
    def get_users_by_role(self, role: UserRole) -> List[int]:
        """
        Получение списка пользователей с определенной ролью
        
        Args:
            role (UserRole): Роль для поиска
            
        Returns:
            List[int]: Список ID пользователей с указанной ролью
        """
        try:
            users = []
            
            if role == "admin":
                # Добавляем администраторов из .env
                from .environment import env_config
                users.extend(env_config.ADMIN_CHAT_IDS)
            
            # Добавляем пользователей из конфигурации
            for user_id, user_data in self._config.get("users", {}).items():
                if user_data.get("role") == role:
                    users.append(int(user_id))
            
            return list(set(users))  # Убираем дубликаты
        except Exception as e:
            logger.error(f"Ошибка получения пользователей с ролью {role}: {e}")
            return []
    
    def remove_user_role(self, user_id: int, removed_by: int) -> bool:
        """
        Удаление роли пользователя (установка роли "none")
        
        Args:
            user_id (int): ID пользователя
            removed_by (int): ID администратора, который удаляет роль
            
        Returns:
            bool: True если роль успешно удалена
        """
        return self.set_user_role(user_id, "none", removed_by)
    
    def get_statistics(self) -> Dict:
        """
        Получение статистики по ролям
        
        Returns:
            Dict: Статистика ролей
        """
        try:
            from .environment import env_config
            
            admins_count = len(env_config.ADMIN_CHAT_IDS)
            teamleads_count = len(self.get_users_by_role("teamlead"))
            total_users_with_access = admins_count + teamleads_count
            
            return {
                "total_admins": admins_count,
                "total_teamleads": teamleads_count,
                "total_users_with_access": total_users_with_access,
                "last_updated": self._config.get("last_updated"),
                "updated_by": self._config.get("updated_by")
            }
        except Exception as e:
            logger.error(f"Ошибка получения статистики ролей: {e}")
            return {}

# Глобальный экземпляр менеджера ролей
roles_manager = RolesManager()

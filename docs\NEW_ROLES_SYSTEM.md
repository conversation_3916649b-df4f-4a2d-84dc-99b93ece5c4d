# 👤 Новая система ролей пользователей

## 🎯 Описание

Полностью переработанная система ролей, которая обеспечивает контролируемый доступ к боту с тремя уровнями прав:
- **Администраторы** - полный доступ ко всем функциям
- **Тимлиды** - ограниченный доступ только к назначенным аккаунтам
- **Обычные пользователи** - нет доступа к боту

## 🔄 Изменения в системе

### ❌ **Что было убрано:**
- Автоматический доступ для всех пользователей
- Возможность тимлидов добавлять каналы
- Неконтролируемое использование бота

### ✅ **Что добавлено:**
- Система ролей с контролем доступа
- Ограниченный интерфейс для тимлидов
- Управление ролями через админку
- Автоматические уведомления при назначении/удалении ролей

## 🎭 Роли пользователей

### 👑 **Администраторы (admin)**
**Источник:** `ADMIN_CHAT_ID` в `.env` файле

**Права доступа:**
- ✅ Подключение и управление аккаунтами
- ✅ Управление стоп-словами
- ✅ Настройки мониторинга
- ✅ Управление тимлидами
- ✅ Настройка времени ответа
- ✅ Управление ролями пользователей
- ✅ Просмотр статистики
- ✅ Все административные функции

**Клавиатура:**
```
[Подключить аккаунт]
[Мои аккаунты]
[Управление стоп-словами]
[⚙️ Настройки мониторинга]
[👥 Управление тимлидами]
[⏰ Время ответа]
[👤 Управление ролями]
[Статистика]
```

### 👤 **Тимлиды (teamlead)**
**Источник:** Назначаются администраторами через бота

**Права доступа:**
- ✅ Просмотр только назначенных им аккаунтов
- ✅ Просмотр статистики
- ❌ Подключение новых аккаунтов
- ❌ Управление настройками
- ❌ Административные функции

**Клавиатура:**
```
[Мои аккаунты]
[Статистика]
```

### 🚫 **Обычные пользователи (none)**
**Источник:** Все новые пользователи по умолчанию

**Права доступа:**
- ❌ Нет доступа к боту
- ❌ Получают сообщение "У вас нет прав использовать этого бота"

## 🔧 Управление ролями

### 📱 **Доступ к управлению:**
1. Только администраторы могут управлять ролями
2. Кнопка **"👤 Управление ролями"** в главном меню
3. Интуитивный интерфейс с подтверждениями

### 👤 **Назначение роли тимлида:**

1. **Нажмите "👤 Управление ролями"**
2. **Выберите "👤 Назначить тимлида"**
3. **Введите Telegram ID пользователя**
4. **Подтвердите назначение**

**Результат:**
- Пользователь получает роль тимлида
- Автоматическое уведомление пользователю
- Доступ к ограниченному интерфейсу бота

### 🗑️ **Удаление роли:**

1. **Нажмите "👤 Управление ролями"**
2. **Выберите "🗑️ Удалить роль"**
3. **Введите Telegram ID пользователя**
4. **Подтвердите удаление**

**Результат:**
- Пользователь теряет доступ к боту
- Автоматическое уведомление пользователю
- Назначения аккаунтов остаются (через систему тимлидов)

### 👥 **Просмотр всех ролей:**

Показывает полный список пользователей с ролями:
- Администраторы (из .env)
- Тимлиды (из конфигурации)
- Информация о том, кто и когда назначил роль

## 🔄 Интеграция с системой тимлидов

### 📱 **Назначение аккаунтов тимлидам:**
1. **Сначала назначьте роль тимлида** через "👤 Управление ролями"
2. **Затем назначьте аккаунты** через "👥 Управление тимлидами"

### 🔔 **Система уведомлений:**
- **Алерты времени ответа** → назначенным тимлидам
- **Фото алерты** → назначенным тимлидам
- **Fallback на админов** если тимлиды не назначены

### 👀 **Просмотр аккаунтов тимлидами:**
- Тимлиды видят только назначенные им аккаунты
- Нет возможности подключать новые аккаунты
- Только просмотр информации и статистики

## 🏗️ Техническая реализация

### 📁 **Новые файлы:**
- `src/config/roles_manager.py` - менеджер ролей
- `src/bot/handlers/roles_management.py` - обработчики управления ролями
- `data/roles_config.json` - конфигурация ролей

### 🔄 **Обновленные файлы:**
- `src/bot/keyboards.py` - новые клавиатуры для ролей
- `src/bot/handlers/basic.py` - обновленный /start
- `src/bot/handlers/account.py` - ограничения для тимлидов
- `src/bot/states.py` - новые FSM состояния

### 📊 **Структура конфигурации ролей:**
```json
{
  "users": {
    "*********": {
      "role": "teamlead",
      "granted_by": **********,
      "granted_at": "2025-07-29T21:50:46.132537"
    }
  },
  "last_updated": "2025-07-29T21:50:46.132537",
  "updated_by": **********
}
```

## 🎮 Сценарии использования

### 📋 **Сценарий 1: Новый пользователь**
1. Пользователь нажимает /start
2. Получает сообщение "У вас нет прав использовать этого бота"
3. Обращается к администратору
4. Администратор назначает роль тимлида
5. Пользователь получает доступ к ограниченному интерфейсу

### 📋 **Сценарий 2: Назначение тимлида**
1. Администратор открывает "👤 Управление ролями"
2. Выбирает "👤 Назначить тимлида"
3. Вводит ID пользователя
4. Система автоматически уведомляет пользователя
5. Администратор назначает аккаунты через "👥 Управление тимлидами"

### 📋 **Сценарий 3: Работа тимлида**
1. Тимлид нажимает /start
2. Видит приветствие для тимлида
3. Может просматривать только назначенные аккаунты
4. Получает алерты от назначенных аккаунтов
5. Не может подключать новые аккаунты или изменять настройки

## 🔒 Безопасность

### 🛡️ **Контроль доступа:**
- Проверка ролей на каждом уровне
- Валидация прав перед выполнением действий
- Логирование всех изменений ролей

### 📝 **Аудит:**
- Все назначения ролей логируются
- Сохраняется информация о том, кто назначил роль
- Временные метки всех изменений

### 🔐 **Защита от злоупотреблений:**
- Только администраторы могут назначать роли
- Нельзя изменить роль администратора
- Нельзя изменить свою собственную роль

## 🧪 Тестирование

### 🔬 **Автоматические тесты:**
```bash
# Полное тестирование системы ролей
python scripts/test_roles_system.py
```

### ✅ **Покрытие тестами:**
- ✅ Проверка администраторов из .env
- ✅ Назначение и удаление ролей
- ✅ Валидация прав доступа
- ✅ Получение пользователей по ролям
- ✅ Статистика ролей
- ✅ Защита от некорректных операций

## 📊 Статистика

### 📈 **Доступная информация:**
- Общее количество администраторов
- Общее количество тимлидов
- Общее количество пользователей с доступом
- Время последнего обновления
- ID администратора, выполнившего последнее изменение

## 🎉 Заключение

Новая система ролей обеспечивает:

- ✅ **Полный контроль доступа** - только авторизованные пользователи
- ✅ **Гибкое управление** - простое назначение и удаление ролей
- ✅ **Безопасность** - защита от несанкционированного доступа
- ✅ **Интеграция** - совместимость с существующей системой тимлидов
- ✅ **Удобство** - интуитивный интерфейс для администраторов
- ✅ **Ограниченный доступ тимлидов** - только к назначенным аккаунтам

Система готова к продуктивному использованию и полностью соответствует требованиям!

#!/usr/bin/env python3
"""
Тестовый скрипт для проверки системы мониторинга времени ответа
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.response_time_manager import response_time_manager
from src.monitoring.response_tracker import ResponseTracker

async def test_response_time_system():
    """Тестирование системы мониторинга времени ответа"""
    print("🧪 Тестирование системы мониторинга времени ответа")
    print("=" * 60)
    
    # Тест 1: Проверка ResponseTimeManager
    print("\n1️⃣ Тестирование ResponseTimeManager...")
    
    # Проверяем начальные настройки
    print(f"   📊 Включен: {response_time_manager.enabled}")
    print(f"   ⏰ Время ответа по умолчанию: {response_time_manager.default_response_time_minutes} мин")
    print(f"   🔔 Предупреждение за: {response_time_manager.alert_before_deadline_minutes} мин")
    print(f"   🔄 Интервал проверки: {response_time_manager.check_interval_seconds} сек")
    
    # Тестируем изменение настроек
    print("\n   🔧 Тестирование изменения настроек...")
    test_user_id = 123456789
    
    # Включаем систему
    success = response_time_manager.set_enabled(True, test_user_id)
    print(f"   ✅ Включение системы: {'успешно' if success else 'ошибка'}")
    
    # Устанавливаем время ответа
    success = response_time_manager.set_default_response_time(15, test_user_id)
    print(f"   ✅ Установка времени ответа: {'успешно' if success else 'ошибка'}")
    
    # Устанавливаем индивидуальные настройки для аккаунта
    test_phone = "+************"
    success = response_time_manager.set_account_response_time(test_phone, 5, test_user_id)
    print(f"   ✅ Индивидуальные настройки: {'успешно' if success else 'ошибка'}")
    
    # Проверяем получение времени ответа
    account_time = response_time_manager.get_response_time_for_account(test_phone)
    default_time = response_time_manager.get_response_time_for_account("+***********")
    print(f"   📱 Время для {test_phone}: {account_time} мин")
    print(f"   📱 Время для неизвестного номера: {default_time} мин")
    
    # Тест 2: Проверка ResponseTracker
    print("\n2️⃣ Тестирование ResponseTracker...")
    
    # Создаем mock callback для алертов
    alerts_received = []
    
    async def mock_alert_callback(phone: str, alert_text: str):
        alerts_received.append({
            'phone': phone,
            'alert': alert_text,
            'time': datetime.now()
        })
        print(f"   🚨 Получен алерт для {phone}: {alert_text[:50]}...")
    
    # Создаем трекер
    tracker = ResponseTracker(mock_alert_callback)
    
    # Запускаем мониторинг
    await tracker.start_monitoring()
    print(f"   🔄 Трекер запущен: {tracker._running}")
    
    # Тестируем отслеживание входящего сообщения
    print("\n   📥 Тестирование входящего сообщения...")
    incoming_message = {
        'account_phone': test_phone,
        'message_id': 12345,
        'sender_id': *********,
        'sender_name': 'Тестовый Лид',
        'chat_id': *********,
        'chat_title': 'Тестовый чат',
        'text': 'Привет! У меня есть вопрос по вашему продукту.',
        'is_from_external': True
    }
    
    success = tracker.track_incoming_message(incoming_message)
    print(f"   ✅ Отслеживание входящего сообщения: {'успешно' if success else 'ошибка'}")
    
    # Проверяем статус
    status = tracker.get_status()
    print(f"   📊 Статус трекера: {status}")
    
    # Ждем немного для демонстрации
    print("\n   ⏳ Ожидание 3 секунды для демонстрации...")
    await asyncio.sleep(3)
    
    # Тестируем исходящее сообщение (ответ)
    print("\n   📤 Тестирование исходящего сообщения (ответ)...")
    outgoing_message = {
        'account_phone': test_phone,
        'chat_id': *********,
        'message_id': 12346,
        'text': 'Здравствуйте! Спасибо за ваш вопрос.'
    }
    
    success = tracker.track_outgoing_message(outgoing_message)
    print(f"   ✅ Отслеживание исходящего сообщения: {'успешно' if success else 'ошибка'}")
    
    # Проверяем обновленный статус
    status = tracker.get_status()
    print(f"   📊 Обновленный статус: {status}")
    
    # Останавливаем трекер
    await tracker.stop_monitoring()
    print(f"   🛑 Трекер остановлен: {not tracker._running}")
    
    # Тест 3: Проверка статистики
    print("\n3️⃣ Проверка статистики...")
    statistics = response_time_manager.get_statistics()
    print(f"   📈 Статистика: {statistics}")
    
    # Тест 4: Проверка всех настроек
    print("\n4️⃣ Проверка всех настроек...")
    all_settings = response_time_manager.get_all_settings()
    print(f"   ⚙️ Все настройки:")
    for key, value in all_settings.items():
        if key != 'account_specific_settings':  # Не показываем детали аккаунтов
            print(f"      {key}: {value}")
        else:
            print(f"      {key}: {len(value)} аккаунтов с индивидуальными настройками")
    
    # Показываем полученные алерты
    print(f"\n📨 Получено алертов: {len(alerts_received)}")
    for i, alert in enumerate(alerts_received, 1):
        print(f"   {i}. {alert['phone']} в {alert['time'].strftime('%H:%M:%S')}")
    
    print("\n" + "=" * 60)
    print("✅ Тестирование завершено!")
    print("\n💡 Рекомендации:")
    print("1. Запустите бота и проверьте кнопку '⏰ Время ответа'")
    print("2. Настройте время ответа через интерфейс")
    print("3. Подключите аккаунты и протестируйте реальные сообщения")
    print("4. Проверьте алерты в Telegram")

if __name__ == "__main__":
    asyncio.run(test_response_time_system())

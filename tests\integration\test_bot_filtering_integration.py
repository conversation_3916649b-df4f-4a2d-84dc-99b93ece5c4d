"""
Интеграционные тесты для функциональности фильтрации ботов

Проверяет взаимодействие между MonitoringSettings и TelethonManager
для корректной фильтрации алертов от ботов.
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

from src.config.monitoring_settings import MonitoringSettings
from src.monitoring.telethon_manager import TelethonManager


class TestBotFilteringIntegration:
    """Интеграционные тесты фильтрации ботов"""
    
    def test_bot_filtering_enabled_by_default(self):
        """Тест что фильтрация ботов включена по умолчанию"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                manager = TelethonManager()
                
                # Проверяем что фильтрация включена по умолчанию
                assert settings.bot_filtering_enabled is True
                
                # Проверяем что функция определения ботов работает
                bot_sender = Mock()
                bot_sender.bot = True
                bot_sender.username = "test_bot"
                
                assert manager._is_bot_sender(bot_sender) is True
    
    def test_bot_filtering_can_be_disabled(self):
        """Тест отключения фильтрации ботов"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                # Отключаем фильтрацию ботов
                result = settings.set_bot_filtering(False, updated_by=123456789)
                assert result is True
                assert settings.bot_filtering_enabled is False
                
                # Проверяем что настройка сохранилась
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    assert saved_settings["bot_filtering_enabled"] is False
    
    def test_monitoring_settings_integration(self):
        """Тест интеграции с настройками мониторинга"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                # Проверяем полный статус мониторинга
                status = settings.get_monitoring_status()
                
                assert "bot_filtering" in status
                assert status["bot_filtering"] is True
                
                # Изменяем настройку и проверяем обновление
                settings.set_bot_filtering(False, updated_by=987654321)
                
                updated_status = settings.get_monitoring_status()
                assert updated_status["bot_filtering"] is False
                assert updated_status["updated_by"] == 987654321
    
    def test_bot_detection_patterns(self):
        """Тест различных паттернов определения ботов"""
        manager = TelethonManager()
        
        # Тестовые случаи: (username, bot_flag, expected_result)
        test_cases = [
            # Боты с флагом
            ("regular_user", True, True),
            ("test_bot", True, True),
            
            # Боты по username
            ("PriemkaLitva_bot", False, True),
            ("support_bot", False, True),
            ("notification_bot", False, True),
            ("helper_bot", False, True),
            
            # Обычные пользователи
            ("regular_user", False, False),
            ("john_doe", False, False),
            ("user123", False, False),
            
            # Граничные случаи
            ("botuser", False, False),  # Содержит "bot", но не заканчивается на "_bot"
            ("user_support", False, False),  # Содержит "support", но не заканчивается на "_bot"
        ]
        
        for username, bot_flag, expected in test_cases:
            mock_sender = Mock()
            mock_sender.bot = bot_flag
            mock_sender.username = username
            
            result = manager._is_bot_sender(mock_sender)
            assert result == expected, f"Failed for username: {username}, bot_flag: {bot_flag}"
    
    def test_settings_persistence(self):
        """Тест сохранения настроек между сессиями"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                # Первая сессия - изменяем настройки
                settings1 = MonitoringSettings()
                settings1.set_bot_filtering(False, updated_by=111)
                settings1.set_group_chat_monitoring(False, updated_by=111)
                
                # Вторая сессия - загружаем настройки
                settings2 = MonitoringSettings()
                
                assert settings2.bot_filtering_enabled is False
                assert settings2.group_chat_monitoring_enabled is False
                
                status = settings2.get_monitoring_status()
                assert status["updated_by"] == 111
    
    def test_error_recovery(self):
        """Тест восстановления после ошибок"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            # Создаем файл с невалидным JSON
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write("invalid json content")
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                # Должны получить настройки по умолчанию
                settings = MonitoringSettings()
                
                assert settings.bot_filtering_enabled is True
                assert settings.group_chat_monitoring_enabled is True
                assert settings.private_chat_monitoring_enabled is True
                
                # После исправления должны работать нормально
                result = settings.set_bot_filtering(False)
                assert result is True
                assert settings.bot_filtering_enabled is False
    
    def test_concurrent_settings_access(self):
        """Тест одновременного доступа к настройкам"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                # Создаем несколько экземпляров настроек
                settings1 = MonitoringSettings()
                settings2 = MonitoringSettings()
                
                # Изменяем настройки в первом экземпляре
                settings1.set_bot_filtering(False, updated_by=123)
                
                # Перезагружаем настройки во втором экземпляре
                settings2.reload_settings()
                
                # Проверяем что изменения видны
                assert settings2.bot_filtering_enabled is False
                
                status = settings2.get_monitoring_status()
                assert status["updated_by"] == 123

"""
Утилиты для настройки логирования
"""

import logging
import sys
from pathlib import Path
from src.config.settings import LOGS_DIR, LOG_FORMAT, LOG_LEVEL

def setup_logging(name: str = None, log_file: str = None) -> logging.Logger:
    """
    Настройка логирования для модуля
    
    Args:
        name: Имя логгера (по умолчанию __name__)
        log_file: Имя файла лога (по умолчанию 'app.log')
    
    Returns:
        Настроенный логгер
    """
    if name is None:
        name = __name__
    
    if log_file is None:
        log_file = 'app.log'
    
    logger = logging.getLogger(name)
    
    # Если логгер уже настроен, возвращаем его
    if logger.handlers:
        return logger
    
    logger.setLevel(getattr(logging, LOG_LEVEL))
    
    # Создание форматтера
    formatter = logging.Formatter(LOG_FORMAT)
    
    # Обработчик для файла
    file_handler = logging.FileHandler(
        LOGS_DIR / log_file, 
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    
    # Обработчик для консоли
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # Добавление обработчиков
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """Получение логгера по имени"""
    return logging.getLogger(name)

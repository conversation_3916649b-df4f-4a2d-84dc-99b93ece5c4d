#!/usr/bin/env python3
"""
Тест получения списка аккаунтов из бота
"""

import asyncio
import sys
from pathlib import Path

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.monitoring.telethon_manager import TelethonManager
from src.bot.alerts import AlertManager
from aiogram import Bot

async def test_bot_accounts():
    """Тест получения списка аккаунтов"""
    print("🔍 Тест получения списка аккаунтов...")
    print("=" * 50)
    
    # Создаем менеджер с AlertManager (как в реальном боте)
    from src.config.environment import env_config
    bot = Bot(token=env_config.BOT_TOKEN)
    alert_manager = AlertManager(bot)
    manager = TelethonManager(alert_manager)
    
    # Инициализируем
    print("🚀 Инициализация TelethonManager...")
    await manager.initialize()
    print("✅ Инициализация завершена")
    
    # Получаем список аккаунтов
    print("\n👥 Получение списка подключенных аккаунтов...")
    accounts = manager.get_connected_accounts()
    print(f"✅ Найдено {len(accounts)} аккаунтов:")
    
    for account in accounts:
        print(f"   - User ID: {account['user_id']}")
        print(f"     Телефон: {account['phone']}")
        print(f"     Мониторинг: {account['monitoring']}")
        print()
    
    # Закрываем бота
    await bot.session.close()

if __name__ == "__main__":
    # Исправление для Windows
    import sys
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(test_bot_accounts())

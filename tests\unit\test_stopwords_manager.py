"""
Юнит-тесты для StopWordsManager

Покрывает все методы класса StopWordsManager с граничными случаями,
обработкой ошибок и тестами безопасности.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, mock_open, Mock

from src.utils.stopwords import StopWordsManager
from tests.fixtures.test_constants import (
    VALID_STOPWORDS, INVALID_STOPWORDS, MALICIOUS_INPUTS,
    TEXTS_WITH_STOPWORDS, TEXTS_WITHOUT_STOPWORDS, EDGE_CASES
)
from tests.utils.test_helpers import (
    create_temp_file, create_stopwords_file_content, simulate_file_error
)


class TestStopWordsManagerInit:
    """Тесты инициализации StopWordsManager"""
    
    def test_init_loads_stopwords_on_creation(self, temp_stopwords_file):
        """Тест загрузки стоп-слов при создании экземпляра"""
        with patch('src.utils.stopwords.STOPWORDS_FILE', temp_stopwords_file):
            manager = StopWordsManager()
            
            assert len(manager.stopwords) > 0
            assert "спам" in manager.stopwords
            assert "реклама" in manager.stopwords
    
    def test_init_with_missing_file(self, temp_dir):
        """Тест инициализации с отсутствующим файлом"""
        missing_file = temp_dir / "missing_stopwords.txt"
        
        with patch('src.utils.stopwords.STOPWORDS_FILE', missing_file):
            manager = StopWordsManager()
            
            assert manager.stopwords == []
    
    def test_init_with_permission_error(self, temp_stopwords_file):
        """Тест инициализации с ошибкой доступа к файлу"""
        with patch('src.utils.stopwords.STOPWORDS_FILE', temp_stopwords_file):
            with patch('builtins.open', side_effect=PermissionError("Access denied")):
                manager = StopWordsManager()
                
                assert manager.stopwords == []


class TestLoadStopwords:
    """Тесты метода load_stopwords()"""
    
    def test_load_stopwords_basic_functionality(self, temp_dir):
        """Тест базовой функциональности загрузки стоп-слов"""
        content = create_stopwords_file_content(["спам", "реклама", "продажа"])
        stopwords_file = create_temp_file(content)
        
        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            words = manager.load_stopwords()
            
            assert len(words) == 3
            assert "спам" in words
            assert "реклама" in words
            assert "продажа" in words
    
    def test_load_stopwords_ignores_comments(self, temp_dir):
        """Тест игнорирования комментариев"""
        content = """# Это комментарий
спам
# Еще комментарий
реклама
# Последний комментарий"""
        
        stopwords_file = create_temp_file(content)
        
        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            words = manager.load_stopwords()
            
            assert len(words) == 2
            assert "спам" in words
            assert "реклама" in words
            assert "# Это комментарий" not in words
    
    def test_load_stopwords_ignores_empty_lines(self, temp_dir):
        """Тест игнорирования пустых строк"""
        content = """спам

реклама


продажа
"""
        
        stopwords_file = create_temp_file(content)
        
        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            words = manager.load_stopwords()
            
            assert len(words) == 3
            assert "" not in words
    
    def test_load_stopwords_converts_to_lowercase(self, temp_dir):
        """Тест преобразования в нижний регистр"""
        content = "СПАМ\nРеклама\nПРОДАЖА"
        stopwords_file = create_temp_file(content)
        
        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            words = manager.load_stopwords()
            
            assert "спам" in words
            assert "реклама" in words
            assert "продажа" in words
            assert "СПАМ" not in words
    
    def test_load_stopwords_file_not_found(self, temp_dir):
        """Тест обработки отсутствующего файла"""
        missing_file = temp_dir / "missing.txt"
        
        with patch('src.utils.stopwords.STOPWORDS_FILE', missing_file):
            manager = StopWordsManager()
            words = manager.load_stopwords()
            
            assert words == []
    
    def test_load_stopwords_permission_error(self, temp_stopwords_file):
        """Тест обработки ошибки доступа"""
        with patch('src.utils.stopwords.STOPWORDS_FILE', temp_stopwords_file):
            with patch('builtins.open', side_effect=PermissionError("Access denied")):
                manager = StopWordsManager()
                words = manager.load_stopwords()
                
                assert words == []
    
    def test_load_stopwords_unicode_decode_error(self, temp_dir):
        """Тест обработки ошибки кодировки"""
        # Создаем файл с неверной кодировкой
        binary_file = temp_dir / "bad_encoding.txt"
        binary_file.write_bytes(b'\xff\xfe\x00\x00invalid')
        
        with patch('src.utils.stopwords.STOPWORDS_FILE', binary_file):
            manager = StopWordsManager()
            words = manager.load_stopwords()
            
            assert words == []
    
    def test_load_stopwords_io_error(self, temp_stopwords_file):
        """Тест обработки ошибки ввода-вывода"""
        with patch('src.utils.stopwords.STOPWORDS_FILE', temp_stopwords_file):
            with patch('builtins.open', side_effect=IOError("I/O error")):
                manager = StopWordsManager()
                words = manager.load_stopwords()
                
                assert words == []
    
    def test_load_stopwords_strips_whitespace(self, temp_dir):
        """Тест удаления пробелов в начале и конце"""
        content = "  спам  \n\t реклама \t\n   продажа   "
        stopwords_file = create_temp_file(content)
        
        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            words = manager.load_stopwords()
            
            assert "спам" in words
            assert "реклама" in words
            assert "продажа" in words
            assert "  спам  " not in words


class TestReloadStopwords:
    """Тесты метода reload_stopwords()"""
    
    def test_reload_stopwords_returns_count(self, temp_stopwords_file):
        """Тест возврата количества загруженных слов"""
        with patch('src.utils.stopwords.STOPWORDS_FILE', temp_stopwords_file):
            manager = StopWordsManager()
            initial_count = len(manager.stopwords)
            
            count = manager.reload_stopwords()
            
            assert count == initial_count
            assert count > 0
    
    def test_reload_stopwords_updates_list(self, temp_dir):
        """Тест обновления списка стоп-слов"""
        # Создаем файл с начальными словами
        content1 = "спам\nреклама"
        stopwords_file = create_temp_file(content1)
        
        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            assert len(manager.stopwords) == 2
            
            # Обновляем файл
            content2 = "спам\nреклама\nпродажа\nкупить"
            stopwords_file.write_text(content2, encoding='utf-8')
            
            count = manager.reload_stopwords()
            
            assert count == 4
            assert len(manager.stopwords) == 4
            assert "продажа" in manager.stopwords
            assert "купить" in manager.stopwords
    
    def test_reload_stopwords_with_empty_file(self, temp_dir):
        """Тест перезагрузки с пустым файлом"""
        empty_file = create_temp_file("")

        with patch('src.utils.stopwords.STOPWORDS_FILE', empty_file):
            manager = StopWordsManager()
            count = manager.reload_stopwords()

            assert count == 0
            assert manager.stopwords == []


class TestContainsStopwords:
    """Тесты метода contains_stopwords()"""

    def test_contains_stopwords_finds_single_word(self, stopwords_manager):
        """Тест поиска одного стоп-слова"""
        stopwords_manager.stopwords = ["спам", "реклама"]

        found, words = stopwords_manager.contains_stopwords("Это спам сообщение")

        assert found is True
        assert "спам" in words
        assert len(words) == 1

    def test_contains_stopwords_finds_multiple_words(self, stopwords_manager):
        """Тест поиска нескольких стоп-слов"""
        stopwords_manager.stopwords = ["спам", "реклама", "продажа"]

        found, words = stopwords_manager.contains_stopwords("Спам и реклама продажа")

        assert found is True
        assert "спам" in words
        assert "реклама" in words
        assert "продажа" in words
        assert len(words) == 3

    def test_contains_stopwords_case_insensitive(self, stopwords_manager):
        """Тест поиска без учета регистра"""
        stopwords_manager.stopwords = ["спам"]

        test_cases = [
            "СПАМ",
            "Спам",
            "спАм",
            "СпАМ"
        ]

        for text in test_cases:
            found, words = stopwords_manager.contains_stopwords(text)
            assert found is True
            assert "спам" in words

    def test_contains_stopwords_partial_match(self, stopwords_manager):
        """Тест частичного совпадения"""
        stopwords_manager.stopwords = ["спам", "реклама"]

        found, words = stopwords_manager.contains_stopwords("спамить рекламировать")

        assert found is True
        assert "спам" in words
        assert "реклама" in words

    def test_contains_stopwords_no_match(self, stopwords_manager):
        """Тест отсутствия совпадений"""
        stopwords_manager.stopwords = ["спам", "реклама"]

        found, words = stopwords_manager.contains_stopwords("Обычное сообщение")

        assert found is False
        assert words == []

    def test_contains_stopwords_empty_text(self, stopwords_manager):
        """Тест с пустым текстом"""
        stopwords_manager.stopwords = ["спам"]

        found, words = stopwords_manager.contains_stopwords("")

        assert found is False
        assert words == []

    def test_contains_stopwords_none_text(self, stopwords_manager):
        """Тест с None текстом"""
        stopwords_manager.stopwords = ["спам"]

        found, words = stopwords_manager.contains_stopwords(None)

        assert found is False
        assert words == []

    def test_contains_stopwords_empty_stopwords_list(self, stopwords_manager):
        """Тест с пустым списком стоп-слов"""
        stopwords_manager.stopwords = []

        found, words = stopwords_manager.contains_stopwords("Любой текст")

        assert found is False
        assert words == []

    def test_contains_stopwords_whitespace_text(self, stopwords_manager):
        """Тест с текстом из пробелов"""
        stopwords_manager.stopwords = ["спам"]

        found, words = stopwords_manager.contains_stopwords("   \n\t   ")

        assert found is False
        assert words == []

    @pytest.mark.parametrize("text,expected_words", TEXTS_WITH_STOPWORDS)
    def test_contains_stopwords_parametrized_positive(self, stopwords_manager, text, expected_words):
        """Параметризованный тест поиска стоп-слов"""
        stopwords_manager.stopwords = VALID_STOPWORDS

        found, words = stopwords_manager.contains_stopwords(text)

        assert found is True
        for expected_word in expected_words:
            assert expected_word in words

    @pytest.mark.parametrize("text", TEXTS_WITHOUT_STOPWORDS)
    def test_contains_stopwords_parametrized_negative(self, stopwords_manager, text):
        """Параметризованный тест отсутствия стоп-слов"""
        stopwords_manager.stopwords = VALID_STOPWORDS

        found, words = stopwords_manager.contains_stopwords(text)

        assert found is False
        assert words == []

    def test_contains_stopwords_duplicate_matches(self, stopwords_manager):
        """Тест дублирования найденных слов"""
        stopwords_manager.stopwords = ["спам"]

        found, words = stopwords_manager.contains_stopwords("спам спам спам")

        assert found is True
        # Проверяем что спам найден, но не дублируется в результате
        assert "спам" in words
        assert words.count("спам") == 1  # Должно быть только одно вхождение

    def test_contains_stopwords_unicode_text(self, stopwords_manager):
        """Тест с Unicode символами"""
        stopwords_manager.stopwords = ["тест"]

        found, words = stopwords_manager.contains_stopwords("🚀 тест с эмодзи 🎉")

        assert found is True
        assert "тест" in words


class TestAddStopword:
    """Тесты метода add_stopword()"""

    def test_add_stopword_success(self, stopwords_manager):
        """Тест успешного добавления стоп-слова"""
        stopwords_manager.stopwords = ["спам"]

        with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
            result = stopwords_manager.add_stopword("реклама")

            assert result is True
            assert "реклама" in stopwords_manager.stopwords
            assert len(stopwords_manager.stopwords) == 2

    def test_add_stopword_duplicate(self, stopwords_manager):
        """Тест добавления дублирующегося стоп-слова"""
        stopwords_manager.stopwords = ["спам"]

        result = stopwords_manager.add_stopword("спам")

        assert result is False
        assert stopwords_manager.stopwords.count("спам") == 1

    def test_add_stopword_case_insensitive_duplicate(self, stopwords_manager):
        """Тест добавления дублирующегося стоп-слова в разном регистре"""
        stopwords_manager.stopwords = ["спам"]

        result = stopwords_manager.add_stopword("СПАМ")

        assert result is False
        assert "СПАМ" not in stopwords_manager.stopwords
        assert stopwords_manager.stopwords.count("спам") == 1

    def test_add_stopword_strips_whitespace(self, stopwords_manager):
        """Тест удаления пробелов при добавлении"""
        stopwords_manager.stopwords = []

        with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
            result = stopwords_manager.add_stopword("  реклама  ")

            assert result is True
            assert "реклама" in stopwords_manager.stopwords
            assert "  реклама  " not in stopwords_manager.stopwords

    def test_add_stopword_converts_to_lowercase(self, stopwords_manager):
        """Тест преобразования в нижний регистр"""
        stopwords_manager.stopwords = []

        with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
            result = stopwords_manager.add_stopword("РЕКЛАМА")

            assert result is True
            assert "реклама" in stopwords_manager.stopwords
            assert "РЕКЛАМА" not in stopwords_manager.stopwords

    def test_add_stopword_empty_string(self, stopwords_manager):
        """Тест добавления пустой строки"""
        stopwords_manager.stopwords = []

        result = stopwords_manager.add_stopword("")

        assert result is False
        assert len(stopwords_manager.stopwords) == 0

    def test_add_stopword_whitespace_only(self, stopwords_manager):
        """Тест добавления строки из пробелов"""
        stopwords_manager.stopwords = []

        result = stopwords_manager.add_stopword("   \n\t   ")

        assert result is False
        assert len(stopwords_manager.stopwords) == 0

    def test_add_stopword_save_failure(self, stopwords_manager):
        """Тест неудачного сохранения"""
        stopwords_manager.stopwords = []

        with patch.object(stopwords_manager, '_save_stopwords', return_value=False):
            result = stopwords_manager.add_stopword("реклама")

            assert result is False
            # Слово должно быть добавлено в память, но сохранение не удалось
            assert "реклама" in stopwords_manager.stopwords

    @pytest.mark.parametrize("word", VALID_STOPWORDS)
    def test_add_stopword_valid_words(self, stopwords_manager, word):
        """Параметризованный тест добавления валидных слов"""
        stopwords_manager.stopwords = []

        with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
            result = stopwords_manager.add_stopword(word)

            assert result is True
            assert word.lower() in stopwords_manager.stopwords


class TestRemoveStopword:
    """Тесты метода remove_stopword()"""

    def test_remove_stopword_success(self, stopwords_manager):
        """Тест успешного удаления стоп-слова"""
        stopwords_manager.stopwords = ["спам", "реклама"]

        with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
            result = stopwords_manager.remove_stopword("спам")

            assert result is True
            assert "спам" not in stopwords_manager.stopwords
            assert "реклама" in stopwords_manager.stopwords
            assert len(stopwords_manager.stopwords) == 1

    def test_remove_stopword_not_found(self, stopwords_manager):
        """Тест удаления несуществующего стоп-слова"""
        stopwords_manager.stopwords = ["спам"]

        result = stopwords_manager.remove_stopword("реклама")

        assert result is False
        assert "спам" in stopwords_manager.stopwords
        assert len(stopwords_manager.stopwords) == 1

    def test_remove_stopword_case_insensitive(self, stopwords_manager):
        """Тест удаления без учета регистра"""
        stopwords_manager.stopwords = ["спам"]

        with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
            result = stopwords_manager.remove_stopword("СПАМ")

            assert result is True
            assert "спам" not in stopwords_manager.stopwords
            assert len(stopwords_manager.stopwords) == 0

    def test_remove_stopword_strips_whitespace(self, stopwords_manager):
        """Тест удаления пробелов при поиске"""
        stopwords_manager.stopwords = ["спам"]

        with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
            result = stopwords_manager.remove_stopword("  спам  ")

            assert result is True
            assert "спам" not in stopwords_manager.stopwords

    def test_remove_stopword_empty_string(self, stopwords_manager):
        """Тест удаления пустой строки"""
        stopwords_manager.stopwords = ["спам"]

        result = stopwords_manager.remove_stopword("")

        assert result is False
        assert len(stopwords_manager.stopwords) == 1

    def test_remove_stopword_save_failure(self, stopwords_manager):
        """Тест неудачного сохранения при удалении"""
        stopwords_manager.stopwords = ["спам", "реклама"]

        with patch.object(stopwords_manager, '_save_stopwords', return_value=False):
            result = stopwords_manager.remove_stopword("спам")

            assert result is False
            # Слово должно быть удалено из памяти, но сохранение не удалось
            assert "спам" not in stopwords_manager.stopwords


class TestSaveStopwords:
    """Тесты метода _save_stopwords()"""

    def test_save_stopwords_success(self, temp_dir):
        """Тест успешного сохранения стоп-слов"""
        stopwords_file = temp_dir / "test_stopwords.txt"

        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            manager.stopwords = ["спам", "реклама", "продажа"]

            result = manager._save_stopwords()

            assert result is True
            assert stopwords_file.exists()

            # Проверяем содержимое файла
            content = stopwords_file.read_text(encoding='utf-8')
            assert "спам" in content
            assert "реклама" in content
            assert "продажа" in content
            assert "# Список стоп-слов для мониторинга" in content

    def test_save_stopwords_sorted_output(self, temp_dir):
        """Тест сортировки при сохранении"""
        stopwords_file = temp_dir / "test_stopwords.txt"

        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            manager.stopwords = ["яблоко", "банан", "апельсин"]

            result = manager._save_stopwords()

            assert result is True

            content = stopwords_file.read_text(encoding='utf-8')
            lines = [line.strip() for line in content.split('\n') if line.strip() and not line.startswith('#')]

            assert lines == ["апельсин", "банан", "яблоко"]

    def test_save_stopwords_empty_list(self, temp_dir):
        """Тест сохранения пустого списка"""
        stopwords_file = temp_dir / "test_stopwords.txt"

        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            manager.stopwords = []

            result = manager._save_stopwords()

            assert result is True

            content = stopwords_file.read_text(encoding='utf-8')
            # Должны быть только комментарии
            lines = [line.strip() for line in content.split('\n') if line.strip() and not line.startswith('#')]
            assert len(lines) == 0

    def test_save_stopwords_permission_error(self, temp_dir):
        """Тест ошибки доступа при сохранении"""
        stopwords_file = temp_dir / "test_stopwords.txt"

        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            manager.stopwords = ["спам"]

            with patch('builtins.open', side_effect=PermissionError("Access denied")):
                result = manager._save_stopwords()

                assert result is False

    def test_save_stopwords_io_error(self, temp_dir):
        """Тест ошибки ввода-вывода при сохранении"""
        stopwords_file = temp_dir / "test_stopwords.txt"

        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            manager.stopwords = ["спам"]

            with patch('builtins.open', side_effect=IOError("I/O error")):
                result = manager._save_stopwords()

                assert result is False

    def test_save_stopwords_unicode_content(self, temp_dir):
        """Тест сохранения Unicode содержимого"""
        stopwords_file = temp_dir / "test_stopwords.txt"

        with patch('src.utils.stopwords.STOPWORDS_FILE', stopwords_file):
            manager = StopWordsManager()
            manager.stopwords = ["тест", "проверка", "русский"]

            result = manager._save_stopwords()

            assert result is True

            content = stopwords_file.read_text(encoding='utf-8')
            assert "тест" in content
            assert "проверка" in content
            assert "русский" in content


class TestGetStats:
    """Тесты метода get_stats()"""

    def test_get_stats_basic_functionality(self, stopwords_manager):
        """Тест базовой функциональности статистики"""
        stopwords_manager.stopwords = ["спам", "реклама", "продажа"]

        stats = stopwords_manager.get_stats()

        assert isinstance(stats, dict)
        assert 'total_count' in stats
        assert 'words' in stats
        assert stats['total_count'] == 3
        assert len(stats['words']) == 3

    def test_get_stats_empty_list(self, stopwords_manager):
        """Тест статистики для пустого списка"""
        stopwords_manager.stopwords = []

        stats = stopwords_manager.get_stats()

        assert stats['total_count'] == 0
        assert stats['words'] == []

    def test_get_stats_large_list_truncation(self, stopwords_manager):
        """Тест усечения большого списка в статистике"""
        # Создаем список из 15 слов
        large_list = [f"слово{i}" for i in range(15)]
        stopwords_manager.stopwords = large_list

        stats = stopwords_manager.get_stats()

        assert stats['total_count'] == 15
        assert len(stats['words']) == 10  # Должно быть усечено до 10
        assert stats['words'] == large_list[:10]

    def test_get_stats_exactly_ten_words(self, stopwords_manager):
        """Тест статистики для списка из ровно 10 слов"""
        ten_words = [f"слово{i}" for i in range(10)]
        stopwords_manager.stopwords = ten_words

        stats = stopwords_manager.get_stats()

        assert stats['total_count'] == 10
        assert len(stats['words']) == 10
        assert stats['words'] == ten_words

    def test_get_stats_less_than_ten_words(self, stopwords_manager):
        """Тест статистики для списка менее 10 слов"""
        five_words = [f"слово{i}" for i in range(5)]
        stopwords_manager.stopwords = five_words

        stats = stopwords_manager.get_stats()

        assert stats['total_count'] == 5
        assert len(stats['words']) == 5
        assert stats['words'] == five_words


class TestStopWordsManagerEdgeCases:
    """Тесты граничных случаев и безопасности"""

    @pytest.mark.parametrize("malicious_input", MALICIOUS_INPUTS)
    def test_security_malicious_inputs(self, stopwords_manager, malicious_input):
        """Тест безопасности с вредоносными входными данными"""
        stopwords_manager.stopwords = []

        # Тестируем добавление
        with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
            result = stopwords_manager.add_stopword(malicious_input)
            # Вредоносные данные должны быть отклонены или безопасно обработаны
            if result:
                # Если добавление прошло, проверяем что данные безопасно сохранены
                assert malicious_input.lower().strip() in stopwords_manager.stopwords

        # Тестируем поиск
        found, words = stopwords_manager.contains_stopwords(malicious_input)
        # Поиск должен работать без ошибок
        assert isinstance(found, bool)
        assert isinstance(words, list)

    @pytest.mark.parametrize("edge_case_name,edge_case_text", EDGE_CASES.items())
    def test_edge_cases(self, stopwords_manager, edge_case_name, edge_case_text):
        """Тест граничных случаев"""
        stopwords_manager.stopwords = ["тест", "спам"]

        # Тестируем поиск
        found, words = stopwords_manager.contains_stopwords(edge_case_text)
        assert isinstance(found, bool)
        assert isinstance(words, list)

        # Тестируем добавление (если это строка)
        if isinstance(edge_case_text, str):
            with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
                result = stopwords_manager.add_stopword(edge_case_text)
                assert isinstance(result, bool)

    def test_concurrent_access_simulation(self, stopwords_manager):
        """Симуляция конкурентного доступа"""
        stopwords_manager.stopwords = ["спам"]

        # Симулируем одновременное добавление и удаление
        with patch.object(stopwords_manager, '_save_stopwords', return_value=True):
            # Добавляем слово
            add_result = stopwords_manager.add_stopword("реклама")
            assert add_result is True

            # Удаляем другое слово
            remove_result = stopwords_manager.remove_stopword("спам")
            assert remove_result is True

            # Проверяем финальное состояние
            assert "реклама" in stopwords_manager.stopwords
            assert "спам" not in stopwords_manager.stopwords

    def test_memory_usage_large_dataset(self, stopwords_manager):
        """Тест использования памяти с большим набором данных"""
        # Создаем большой список стоп-слов
        large_list = [f"слово{i}" for i in range(1000)]
        stopwords_manager.stopwords = large_list

        # Тестируем поиск в большом тексте
        large_text = " ".join([f"текст{i}" for i in range(1000)])
        found, words = stopwords_manager.contains_stopwords(large_text)

        assert isinstance(found, bool)
        assert isinstance(words, list)

        # Тестируем статистику
        stats = stopwords_manager.get_stats()
        assert stats['total_count'] == 1000
        assert len(stats['words']) == 10  # Должно быть усечено

#!/usr/bin/env python3
"""
Комплексный тест системы мониторинга времени ответа
Проверяет все сценарии и нюансы работы системы
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.response_time_manager import response_time_manager
from src.config.monitoring_settings import monitoring_settings
from src.monitoring.response_tracker import ResponseTracker

class ResponseTimeTestSuite:
    """Комплексный набор тестов для системы времени ответа"""
    
    def __init__(self):
        self.alerts_received = []
        self.test_results = []
        
    async def alert_callback(self, phone: str, alert_text: str):
        """Mock callback для получения алертов"""
        alert = {
            'phone': phone,
            'alert': alert_text,
            'timestamp': datetime.now()
        }
        self.alerts_received.append(alert)
        print(f"   🚨 АЛЕРТ: {phone} - {alert_text[:50]}...")
    
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Логирование результата теста"""
        status = "✅ ПРОЙДЕН" if success else "❌ ПРОВАЛЕН"
        result = {
            'test': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now()
        }
        self.test_results.append(result)
        print(f"   {status}: {test_name}")
        if details:
            print(f"      {details}")
    
    async def test_chat_type_filtering(self):
        """Тест фильтрации по типам чатов"""
        print("\n🔍 ТЕСТ 1: Фильтрация по типам чатов")
        print("-" * 50)
        
        tracker = ResponseTracker(self.alert_callback)
        await tracker.start_monitoring()
        
        # Включаем систему
        response_time_manager.set_enabled(True, *********)
        
        test_cases = [
            {
                'name': 'Личный чат (должен отслеживаться)',
                'chat_type': 'private',
                'should_track': True
            },
            {
                'name': 'Групповой чат (НЕ должен отслеживаться)',
                'chat_type': 'group',
                'should_track': False
            },
            {
                'name': 'Супергруппа (НЕ должна отслеживаться)',
                'chat_type': 'supergroup',
                'should_track': False
            },
            {
                'name': 'Канал (НЕ должен отслеживаться)',
                'chat_type': 'channel',
                'should_track': False
            }
        ]
        
        for i, case in enumerate(test_cases):
            # Проверяем настройки мониторинга
            should_monitor = monitoring_settings.should_monitor_chat(case['chat_type'])
            settings_correct = should_monitor == case['should_track']
            
            self.log_test_result(
                f"Настройки для {case['chat_type']}",
                settings_correct,
                f"should_monitor_chat('{case['chat_type']}') = {should_monitor}"
            )
            
            # Тестируем реальное отслеживание
            message = {
                'account_phone': '+************',
                'message_id': 10000 + i,
                'sender_id': 900000 + i,
                'sender_name': f'Тестер {i+1}',
                'chat_id': 800000 + i,
                'chat_title': f'Тест чат {case["chat_type"]}',
                'text': f'Сообщение в {case["chat_type"]} чате',
                'is_from_external': True,
                'chat_type': case['chat_type'],
                'is_bot': False
            }
            
            success = tracker.track_incoming_message(message)
            tracking_correct = success == case['should_track']
            
            self.log_test_result(
                case['name'],
                tracking_correct,
                f"track_incoming_message() = {success}, ожидалось: {case['should_track']}"
            )
        
        await tracker.stop_monitoring()
    
    async def test_bot_filtering(self):
        """Тест фильтрации ботов"""
        print("\n🤖 ТЕСТ 2: Фильтрация ботов")
        print("-" * 50)
        
        tracker = ResponseTracker(self.alert_callback)
        await tracker.start_monitoring()
        
        test_cases = [
            {
                'name': 'Обычный пользователь (должен отслеживаться)',
                'is_bot': False,
                'sender_name': 'Обычный Пользователь',
                'should_track': True
            },
            {
                'name': 'Бот (НЕ должен отслеживаться при включенной фильтрации)',
                'is_bot': True,
                'sender_name': 'TestBot',
                'should_track': False
            }
        ]
        
        # Проверяем, что фильтрация ботов включена
        bot_filtering = monitoring_settings.bot_filtering_enabled
        self.log_test_result(
            "Фильтрация ботов включена",
            bot_filtering,
            f"bot_filtering_enabled = {bot_filtering}"
        )
        
        for i, case in enumerate(test_cases):
            message = {
                'account_phone': '+************',
                'message_id': 20000 + i,
                'sender_id': 700000 + i,
                'sender_name': case['sender_name'],
                'chat_id': 600000 + i,
                'chat_title': 'Личный чат',
                'text': f'Сообщение от {"бота" if case["is_bot"] else "пользователя"}',
                'is_from_external': True,
                'chat_type': 'private',
                'is_bot': case['is_bot']
            }
            
            success = tracker.track_incoming_message(message)
            expected = case['should_track'] if bot_filtering else True
            tracking_correct = success == expected
            
            self.log_test_result(
                case['name'],
                tracking_correct,
                f"track_incoming_message() = {success}, ожидалось: {expected}"
            )
        
        await tracker.stop_monitoring()
    
    async def test_self_message_filtering(self):
        """Тест фильтрации собственных сообщений"""
        print("\n👤 ТЕСТ 3: Фильтрация собственных сообщений")
        print("-" * 50)
        
        tracker = ResponseTracker(self.alert_callback)
        await tracker.start_monitoring()
        
        test_cases = [
            {
                'name': 'Внешнее сообщение (должно отслеживаться)',
                'is_from_external': True,
                'should_track': True
            },
            {
                'name': 'Собственное сообщение (НЕ должно отслеживаться)',
                'is_from_external': False,
                'should_track': False
            }
        ]
        
        for i, case in enumerate(test_cases):
            message = {
                'account_phone': '+************',
                'message_id': 30000 + i,
                'sender_id': 500000 + i,
                'sender_name': 'Отправитель',
                'chat_id': 400000 + i,
                'chat_title': 'Личный чат',
                'text': f'{"Внешнее" if case["is_from_external"] else "Собственное"} сообщение',
                'is_from_external': case['is_from_external'],
                'chat_type': 'private',
                'is_bot': False
            }
            
            success = tracker.track_incoming_message(message)
            tracking_correct = success == case['should_track']
            
            self.log_test_result(
                case['name'],
                tracking_correct,
                f"track_incoming_message() = {success}, ожидалось: {case['should_track']}"
            )
        
        await tracker.stop_monitoring()
    
    async def test_system_enabled_disabled(self):
        """Тест включения/выключения системы"""
        print("\n⚙️ ТЕСТ 4: Включение/выключение системы")
        print("-" * 50)
        
        tracker = ResponseTracker(self.alert_callback)
        await tracker.start_monitoring()
        
        test_message = {
            'account_phone': '+************',
            'message_id': 40000,
            'sender_id': 300000,
            'sender_name': 'Тестовый Лид',
            'chat_id': 200000,
            'chat_title': 'Личный чат',
            'text': 'Тестовое сообщение',
            'is_from_external': True,
            'chat_type': 'private',
            'is_bot': False
        }
        
        # Тест с выключенной системой
        response_time_manager.set_enabled(False, *********)
        success_disabled = tracker.track_incoming_message(test_message)
        
        self.log_test_result(
            "Система выключена (НЕ должна отслеживать)",
            not success_disabled,
            f"При выключенной системе: track_incoming_message() = {success_disabled}"
        )
        
        # Тест с включенной системой
        response_time_manager.set_enabled(True, *********)
        test_message['message_id'] = 40001  # Новый ID
        success_enabled = tracker.track_incoming_message(test_message)
        
        self.log_test_result(
            "Система включена (должна отслеживать)",
            success_enabled,
            f"При включенной системе: track_incoming_message() = {success_enabled}"
        )
        
        await tracker.stop_monitoring()
    
    async def test_response_workflow(self):
        """Тест полного цикла отслеживания ответа"""
        print("\n🔄 ТЕСТ 5: Полный цикл отслеживания ответа")
        print("-" * 50)
        
        tracker = ResponseTracker(self.alert_callback)
        await tracker.start_monitoring()
        
        # Убеждаемся, что система включена
        response_time_manager.set_enabled(True, *********)
        
        # 1. Входящее сообщение
        incoming_message = {
            'account_phone': '+************',
            'message_id': 50000,
            'sender_id': 100000,
            'sender_name': 'Важный Клиент',
            'chat_id': 50000,
            'chat_title': 'VIP чат',
            'text': 'Срочный вопрос!',
            'is_from_external': True,
            'chat_type': 'private',
            'is_bot': False
        }
        
        success_incoming = tracker.track_incoming_message(incoming_message)
        self.log_test_result(
            "Отслеживание входящего сообщения",
            success_incoming,
            f"track_incoming_message() = {success_incoming}"
        )
        
        # Проверяем статус
        status_before = tracker.get_status()
        pending_before = status_before['pending_messages_count']
        
        self.log_test_result(
            "Сообщение добавлено в ожидающие",
            pending_before == 1,
            f"pending_messages_count = {pending_before}"
        )
        
        # 2. Исходящий ответ
        outgoing_response = {
            'account_phone': '+************',
            'chat_id': 50000,
            'message_id': 50001,
            'text': 'Отвечаю на ваш вопрос...'
        }
        
        response_found = tracker.track_outgoing_message(outgoing_response)
        self.log_test_result(
            "Отслеживание исходящего ответа",
            response_found,
            f"track_outgoing_message() = {response_found}"
        )
        
        # Проверяем, что сообщение удалено из ожидающих
        status_after = tracker.get_status()
        pending_after = status_after['pending_messages_count']
        
        self.log_test_result(
            "Сообщение удалено из ожидающих",
            pending_after == 0,
            f"pending_messages_count = {pending_after}"
        )
        
        await tracker.stop_monitoring()
    
    async def test_deadline_alerts(self):
        """Тест системы алертов о дедлайнах"""
        print("\n⏰ ТЕСТ 6: Система алертов о дедлайнах")
        print("-" * 50)
        
        # Устанавливаем очень короткое время для теста (в секундах через настройки)
        original_time = response_time_manager.default_response_time_minutes

        # Временно изменяем настройки для быстрого теста
        response_time_manager._config["global_settings"]["default_response_time_minutes"] = 0.05  # 3 секунды
        response_time_manager._config["global_settings"]["alert_before_deadline_minutes"] = 0.01  # 0.6 секунды
        response_time_manager.set_enabled(True, *********)
        
        tracker = ResponseTracker(self.alert_callback)
        await tracker.start_monitoring()
        
        # Очищаем предыдущие алерты
        self.alerts_received.clear()
        
        # Добавляем сообщение
        message = {
            'account_phone': '+************',
            'message_id': 60000,
            'sender_id': 60000,
            'sender_name': 'Нетерпеливый Клиент',
            'chat_id': 60000,
            'chat_title': 'Срочный чат',
            'text': 'Мне нужен ответ СЕЙЧАС!',
            'is_from_external': True,
            'chat_type': 'private',
            'is_bot': False
        }
        
        success = tracker.track_incoming_message(message)
        self.log_test_result(
            "Сообщение добавлено для отслеживания дедлайна",
            success,
            f"track_incoming_message() = {success}"
        )
        
        # Ждем срабатывания алерта (дольше чем время ответа)
        print("   ⏳ Ожидание срабатывания алерта (4 секунды)...")
        await asyncio.sleep(4)
        
        # Проверяем алерты
        alerts_count = len(self.alerts_received)
        self.log_test_result(
            "Алерт о просрочке отправлен",
            alerts_count > 0,
            f"Получено алертов: {alerts_count}"
        )
        
        if alerts_count > 0:
            alert = self.alerts_received[-1]
            contains_overdue = 'ПРОСРОЧЕН' in alert['alert']
            self.log_test_result(
                "Алерт содержит информацию о просрочке",
                contains_overdue,
                f"Текст алерта содержит 'ПРОСРОЧЕН': {contains_overdue}"
            )
        
        # Восстанавливаем исходные настройки
        response_time_manager._config["global_settings"]["default_response_time_minutes"] = original_time
        response_time_manager._config["global_settings"]["alert_before_deadline_minutes"] = 2
        response_time_manager._save_config()
        
        await tracker.stop_monitoring()
    
    async def run_all_tests(self):
        """Запуск всех тестов"""
        print("🧪 КОМПЛЕКСНОЕ ТЕСТИРОВАНИЕ СИСТЕМЫ ВРЕМЕНИ ОТВЕТА")
        print("=" * 70)
        
        print(f"\n📊 Текущие настройки:")
        print(f"   • Мониторинг личных чатов: {monitoring_settings.private_chat_monitoring_enabled}")
        print(f"   • Мониторинг групповых чатов: {monitoring_settings.group_chat_monitoring_enabled}")
        print(f"   • Фильтрация ботов: {monitoring_settings.bot_filtering_enabled}")
        print(f"   • Система времени ответа: {response_time_manager.enabled}")
        print(f"   • Время ответа по умолчанию: {response_time_manager.default_response_time_minutes} мин")
        
        # Запускаем все тесты
        await self.test_chat_type_filtering()
        await self.test_bot_filtering()
        await self.test_self_message_filtering()
        await self.test_system_enabled_disabled()
        await self.test_response_workflow()
        await self.test_deadline_alerts()
        
        # Подводим итоги
        print("\n" + "=" * 70)
        print("📋 ИТОГИ ТЕСТИРОВАНИЯ")
        print("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 Всего тестов: {total_tests}")
        print(f"✅ Пройдено: {passed_tests}")
        print(f"❌ Провалено: {failed_tests}")
        print(f"📈 Успешность: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ ПРОВАЛИВШИЕСЯ ТЕСТЫ:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • {result['test']}: {result['details']}")
        
        print(f"\n🚨 Получено алертов: {len(self.alerts_received)}")
        
        success_rate = passed_tests / total_tests
        if success_rate == 1.0:
            print("\n🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Система работает корректно.")
        elif success_rate >= 0.8:
            print("\n⚠️ Большинство тестов пройдено, но есть проблемы.")
        else:
            print("\n🚨 КРИТИЧЕСКИЕ ПРОБЛЕМЫ! Система требует исправления.")
        
        return success_rate


async def main():
    """Главная функция"""
    test_suite = ResponseTimeTestSuite()
    success_rate = await test_suite.run_all_tests()
    
    # Возвращаем код выхода
    exit_code = 0 if success_rate == 1.0 else 1
    sys.exit(exit_code)


if __name__ == "__main__":
    asyncio.run(main())

"""
FSM обработчики для управления тимлидами
"""

import logging
from datetime import datetime
from aiogram import Dispatcher, types
from aiogram.fsm.context import FSMContext

from ..states import TeamleadManagement
from ..keyboards import get_keyboard, teamlead_management_keyboard
from src.config.environment import env_config
from src.config.teamleads_manager import teamleads_manager
from src.config.users_manager import users_manager
from src.utils.logging import setup_logging

logger = setup_logging(__name__, 'teamlead_fsm.log')


def register_teamlead_fsm_handlers(dp: Dispatcher):
    """Регистрация FSM обработчиков для управления тимлидами"""
    
    @dp.message(TeamleadManagement.waiting_for_teamlead_username)
    async def process_teamlead_username(message: types.Message, state: FSMContext):
        """Обработка ввода никнейма тимлида с автоматическим получением ID"""
        username_input = message.text.strip().lstrip('@')
        user_id = message.from_user.id

        if not username_input:
            await message.answer("❌ Никнейм не может быть пустым. Попробуйте еще раз:")
            return

        username = username_input.lower()

        # Проверяем, не существует ли уже такой тимлид
        if teamleads_manager.teamlead_exists(username):
            await message.answer(f"❌ Тимлид с никнеймом '{username}' уже существует. Введите другой никнейм:")
            return

        # Пытаемся найти пользователя по никнейму
        await message.answer(f"🔍 Поиск пользователя @{username_input}...")

        try:
            telegram_id = None
            search_method = None

            # Шаг 1: Поиск в локальной базе пользователей
            local_user = users_manager.find_user_by_username(username_input)
            if local_user:
                telegram_id = local_user["user_id"]
                search_method = "локальная база"
                logger.info(f"Пользователь @{username_input} найден в локальной базе: ID {telegram_id}")

            # Шаг 2: Если не найден в локальной базе, пробуем API Telegram
            if not telegram_id:
                try:
                    bot = message.bot
                    user_info = await bot.get_chat(f"@{username_input}")
                    telegram_id = user_info.id
                    search_method = "Telegram API"
                    logger.info(f"Пользователь @{username_input} найден через API: ID {telegram_id}")

                    # Сохраняем найденного пользователя в локальную базу
                    users_manager.add_or_update_user(
                        user_id=telegram_id,
                        username=username_input,
                        first_name=getattr(user_info, 'first_name', None),
                        last_name=getattr(user_info, 'last_name', None)
                    )

                except Exception as api_error:
                    logger.warning(f"API поиск не удался для @{username_input}: {api_error}")

            # Если пользователь найден любым способом
            if telegram_id:
                # Добавляем тимлида
                success = teamleads_manager.add_teamlead(username, telegram_id, user_id)

                if success:
                    text = (
                        f"✅ Тимлид успешно добавлен!\n\n"
                        f"👤 Никнейм: @{username_input}\n"
                        f"🆔 Telegram ID: {telegram_id}\n"
                        f"🔍 Найден через: {search_method}\n"
                        f"📅 Добавлен: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                        f"Теперь вы можете назначить ему номера телефонов через кнопку \"📱 Назначить номера\""
                    )

                    await message.answer(
                        text,
                        reply_markup=teamlead_management_keyboard()
                    )
                    logger.info(f"Администратор {user_id} добавил тимлида {username} (ID: {telegram_id}) через {search_method}")
                else:
                    await message.answer(
                        "❌ Ошибка при добавлении тимлида. Попробуйте еще раз.",
                        reply_markup=teamlead_management_keyboard()
                    )

                await state.clear()
            else:
                # Пользователь не найден ни одним способом - fallback на ручной ввод ID
                await message.answer(
                    f"❌ Не удалось найти пользователя @{username_input}\n\n"
                    f"Проверено:\n"
                    f"• Локальная база пользователей бота\n"
                    f"• Telegram API\n\n"
                    f"Возможные причины:\n"
                    f"• Пользователь никогда не взаимодействовал с ботом\n"
                    f"• Никнейм написан неверно\n"
                    f"• У пользователя закрытый профиль\n"
                    f"• Пользователь не существует\n\n"
                    f"Введите Telegram ID пользователя вручную:\n"
                    f"💡 ID можно получить у @userinfobot"
                )

                await state.update_data(username=username, original_username=username_input)
                await state.set_state(TeamleadManagement.waiting_for_teamlead_user_id)

        except Exception as e:
            logger.error(f"Критическая ошибка при поиске пользователя: {e}")
            await message.answer(
                "❌ Произошла ошибка при поиске пользователя.\n"
                "Попробуйте еще раз или введите ID вручную.",
                reply_markup=teamlead_management_keyboard()
            )
            await state.clear()
    
    @dp.message(TeamleadManagement.waiting_for_teamlead_user_id)
    async def process_teamlead_user_id(message: types.Message, state: FSMContext):
        """Обработка ввода Telegram ID тимлида (fallback)"""
        try:
            telegram_id = int(message.text.strip())
            user_id = message.from_user.id

            if telegram_id <= 0:
                await message.answer("❌ ID должен быть положительным числом. Попробуйте еще раз:")
                return

            data = await state.get_data()
            username = data['username']
            original_username = data.get('original_username', username)

            # Добавляем тимлида
            success = teamleads_manager.add_teamlead(username, telegram_id, user_id)

            if success:
                text = (
                    f"✅ Тимлид успешно добавлен!\n\n"
                    f"👤 Никнейм: @{original_username}\n"
                    f"🆔 Telegram ID: {telegram_id}\n"
                    f"📅 Добавлен: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                    f"Теперь вы можете назначить ему номера телефонов через кнопку \"📱 Назначить номера\""
                )

                await message.answer(
                    text,
                    reply_markup=teamlead_management_keyboard()
                )
                logger.info(f"Администратор {user_id} добавил тимлида {username} (ID: {telegram_id}) через fallback")
            else:
                await message.answer(
                    "❌ Ошибка при добавлении тимлида. Попробуйте еще раз.",
                    reply_markup=teamlead_management_keyboard()
                )

            await state.clear()

        except ValueError:
            await message.answer("❌ ID должен быть числом. Попробуйте еще раз:")
    
    @dp.message(TeamleadManagement.managing_phone_assignments)
    async def process_phone_unassignment(message: types.Message, state: FSMContext):
        """Обработка отмены назначения номера"""
        user_id = message.from_user.id
        phone = message.text.strip()
        
        data = await state.get_data()
        assignments = data.get('assignments', {})
        
        if phone not in assignments:
            await message.answer("❌ Номер не найден в назначениях. Попробуйте еще раз:")
            return
        
        teamleads = assignments[phone]
        
        # Отменяем все назначения для этого номера
        success_count = 0
        failed_count = 0
        
        for username in teamleads:
            if teamleads_manager.unassign_phone_from_teamlead(phone, username, user_id):
                success_count += 1
            else:
                failed_count += 1
        
        result_text = f"📊 Результат отмены назначений для номера {phone}:\n\n"
        
        if success_count > 0:
            result_text += f"✅ Успешно отменено: {success_count} назначений\n"
        
        if failed_count > 0:
            result_text += f"❌ Ошибок: {failed_count}\n"
        
        result_text += f"\n📱 Номер {phone} больше не назначен тимлидам"
        
        await message.answer(
            result_text, 
            reply_markup=teamlead_management_keyboard()
        )
        
        logger.info(f"Администратор {user_id} отменил {success_count} назначений для номера {phone}")
        await state.clear()
    
    @dp.message(TeamleadManagement.viewing_assignments)
    async def process_clear_all_assignments(message: types.Message, state: FSMContext):
        """Обработка подтверждения очистки всех назначений"""
        user_id = message.from_user.id
        confirmation = message.text.strip()
        
        if confirmation == "ОЧИСТИТЬ ВСЕ НАЗНАЧЕНИЯ":
            # Получаем все назначения
            all_assignments = teamleads_manager.get_all_assignments()
            
            success_count = 0
            failed_count = 0
            
            # Очищаем все назначения
            for phone, teamleads in all_assignments.items():
                for username in teamleads:
                    if teamleads_manager.unassign_phone_from_teamlead(phone, username, user_id):
                        success_count += 1
                    else:
                        failed_count += 1
            
            result_text = (
                f"✅ Очистка назначений завершена!\n\n"
                f"📊 Результат:\n"
                f"• Успешно отменено: {success_count} назначений\n"
                f"• Ошибок: {failed_count}\n"
                f"📅 Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"Все тимлиды остались в системе, но больше не получают алерты."
            )
            
            logger.info(f"Администратор {user_id} очистил все назначения: {success_count} успешно, {failed_count} ошибок")
        else:
            result_text = "❌ Очистка назначений отменена."
        
        await message.answer(
            result_text, 
            reply_markup=teamlead_management_keyboard()
        )
        await state.clear()
    
    @dp.message(TeamleadManagement.confirming_teamlead_deletion)
    async def process_teamlead_deletion_fsm(message: types.Message, state: FSMContext):
        """Обработка подтверждения удаления тимлида через FSM"""
        user_id = message.from_user.id
        text = message.text.strip()
        
        data = await state.get_data()
        
        # Проверяем, это выбор тимлида или подтверждение удаления
        if 'selected_username' not in data:
            # Это выбор тимлида
            try:
                selection = int(text)
                teamleads_list = data['teamleads_list']
                
                if selection < 1 or selection > len(teamleads_list):
                    await message.answer(f"❌ Выберите номер от 1 до {len(teamleads_list)}:")
                    return
                
                selected_username = teamleads_list[selection - 1]
                
                # Получаем информацию о тимлиде
                teamlead_data = teamleads_manager.get_all_teamleads()[selected_username]
                assigned_phones = teamlead_data.get('assigned_phones', [])
                
                confirmation_text = (
                    f"⚠️ Подтверждение удаления\n\n"
                    f"👤 Тимлид: {selected_username}\n"
                    f"🆔 ID: {teamlead_data.get('user_id', 'Неизвестно')}\n"
                    f"📱 Назначенных номеров: {len(assigned_phones)}\n"
                )
                
                if assigned_phones:
                    confirmation_text += f"📞 Номера: {', '.join(assigned_phones)}\n"
                
                confirmation_text += (
                    f"\n❗ Все назначения этого тимлида будут отменены!\n\n"
                    f"Для подтверждения введите: УДАЛИТЬ {selected_username.upper()}\n"
                    f"Для отмены введите любой другой текст."
                )
                
                await message.answer(confirmation_text)
                await state.update_data(selected_username=selected_username)
                
            except ValueError:
                await message.answer("❌ Введите номер тимлида:")
        else:
            # Это подтверждение удаления
            selected_username = data['selected_username']
            expected_confirmation = f"УДАЛИТЬ {selected_username.upper()}"
            
            if text == expected_confirmation:
                # Удаляем тимлида
                success = teamleads_manager.remove_teamlead(selected_username, user_id)
                
                if success:
                    result_text = (
                        f"✅ Тимлид удален!\n\n"
                        f"👤 Тимлид: {selected_username}\n"
                        f"📅 Удален: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                        f"🔄 Все назначения отменены"
                    )
                    logger.info(f"Администратор {user_id} удалил тимлида {selected_username}")
                else:
                    result_text = "❌ Ошибка при удалении тимлида."
            else:
                result_text = "❌ Удаление отменено."
            
            await message.answer(
                result_text,
                reply_markup=teamlead_management_keyboard()
            )
            await state.clear()

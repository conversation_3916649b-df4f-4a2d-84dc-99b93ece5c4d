"""
Модуль валидации стоп-слов для PM Searcher Bot

Содержит класс StopWordValidator для комплексной валидации:
- Пользовательского ввода стоп-слов
- Файлов импорта/экспорта
- Поисковых запросов
- Проверки безопасности данных

Автор: PM Searcher Bot System
Дата создания: 26.07.2025
"""

import re
import os
import logging
from dataclasses import dataclass
from typing import List, Any, Optional, Tuple, Set
from pathlib import Path

from ..utils.logging import setup_logging

# Инициализация логгера для модуля валидации
logger = setup_logging(__name__, 'bot.log')


@dataclass
class ValidationResult:
    """
    Результат валидации данных
    
    Attributes:
        is_valid (bool): Флаг успешности валидации
        errors (List[str]): Список ошибок валидации
        warnings (List[str]): Список предупреждений
        normalized_data (Any): Нормализованные данные при успешной валидации
    """
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    normalized_data: Any = None


class StopWordValidator:
    """
    Валидатор стоп-слов для системы управления стоп-словами
    
    Предоставляет комплексную валидацию пользовательского ввода,
    файлов импорта/экспорта, поисковых запросов и проверку безопасности.
    
    Константы и ограничения:
        - MIN_WORD_LENGTH: Минимальная длина стоп-слова (2 символа)
        - MAX_WORD_LENGTH: Максимальная длина стоп-слова (50 символов)
        - MAX_IMPORT_FILE_SIZE: Максимальный размер файла импорта (1MB)
        - MAX_IMPORT_WORDS: Максимальное количество слов для импорта (1000)
        - ALLOWED_CHARACTERS: Регекс разрешенных символов
    """
    
    # Константы валидации
    MIN_WORD_LENGTH = 2
    MAX_WORD_LENGTH = 50
    MAX_IMPORT_FILE_SIZE = 1024 * 1024  # 1MB в байтах
    MAX_IMPORT_WORDS = 1000
    
    # Регулярное выражение для разрешенных символов
    # Расширенная поддержка: кириллица, латиница, цифры, дефисы, подчеркивания, точки, звездочки и другие безопасные символы
    ALLOWED_CHARACTERS = re.compile(r'^[а-яёА-ЯЁa-zA-Z0-9\-_.*+?!@#%^~,]+$', re.UNICODE)

    # Строго опасные паттерны для проверки безопасности (только критически опасные)
    DANGEROUS_PATTERNS = [
        re.compile(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', re.IGNORECASE),  # XSS скрипты
        re.compile(r'(union\s+select|insert\s+into|delete\s+from|drop\s+table)', re.IGNORECASE),  # SQL injection
        re.compile(r'[;&|`$(){}]'),  # Критически опасные символы для shell injection (убрали [] и <> для большей гибкости)
        re.compile(r'\.\.[\\/]'),  # Path traversal
        re.compile(r'(javascript:|data:|vbscript:)', re.IGNORECASE),  # Опасные протоколы
    ]
    
    # Зарезервированные имена файлов (Windows)
    RESERVED_FILENAMES = {
        'con', 'prn', 'aux', 'nul', 'com1', 'com2', 'com3', 'com4', 'com5',
        'com6', 'com7', 'com8', 'com9', 'lpt1', 'lpt2', 'lpt3', 'lpt4',
        'lpt5', 'lpt6', 'lpt7', 'lpt8', 'lpt9'
    }
    
    def __init__(self, existing_stopwords: Optional[Set[str]] = None):
        """
        Инициализация валидатора
        
        Args:
            existing_stopwords: Набор существующих стоп-слов для проверки дубликатов
        """
        self.existing_stopwords = existing_stopwords or set()
        logger.info("Инициализирован StopWordValidator")
    
    def validate_user_input(self, text: str) -> ValidationResult:
        """
        Валидация пользовательского ввода стоп-слова
        
        Проверяет:
        - Пустой ввод
        - Длину слова
        - Разрешенные символы
        - Дубликаты
        - Безопасность содержимого
        
        Args:
            text: Введенный пользователем текст
            
        Returns:
            ValidationResult: Результат валидации с нормализованным словом
        """
        errors = []
        warnings = []
        
        logger.debug(f"Валидация пользовательского ввода: '{text}'")
        
        # Проверка на пустой ввод
        if not text or not text.strip():
            errors.append("Стоп-слово не может быть пустым")
            return ValidationResult(False, errors, warnings)
        
        # Нормализация слова
        normalized_word = self.normalize_stopword(text)
        
        # Проверка длины (с исключением для символа "@")
        if len(normalized_word) < self.MIN_WORD_LENGTH and normalized_word != "@":
            errors.append(f"Стоп-слово слишком короткое (минимум {self.MIN_WORD_LENGTH} символа)")

        if len(normalized_word) > self.MAX_WORD_LENGTH:
            errors.append(f"Стоп-слово слишком длинное (максимум {self.MAX_WORD_LENGTH} символов)")
        
        # Проверка допустимых символов
        if not self.ALLOWED_CHARACTERS.match(normalized_word):
            errors.append("Стоп-слово содержит недопустимые символы. Разрешены: буквы, цифры, дефисы, подчеркивания, точки, звездочки, символ @ и другие безопасные символы")
        
        # Проверка на дубликаты
        if normalized_word.lower() in self.existing_stopwords:
            errors.append(f"Стоп-слово '{normalized_word}' уже существует")
        
        # Проверка безопасности
        safety_result = self.check_word_safety(normalized_word)
        if not safety_result:
            errors.append("Стоп-слово содержит потенциально опасное содержимое")
        
        # Предупреждения
        if len(normalized_word) <= 3:
            warnings.append("Короткие стоп-слова могут блокировать много легитимного контента")
        
        if normalized_word.isdigit():
            warnings.append("Числовые стоп-слова могут быть проблематичными")
        
        is_valid = len(errors) == 0
        
        logger.info(f"Валидация завершена: valid={is_valid}, word='{normalized_word}'")
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            normalized_data=normalized_word if is_valid else None
        )

    def validate_stopword(self, text: str) -> ValidationResult:
        """
        Алиас для validate_user_input для совместимости с тестами

        Args:
            text: Введенный пользователем текст

        Returns:
            ValidationResult: Результат валидации с нормализованным словом
        """
        return self.validate_user_input(text)
    
    def validate_import_file(self, file_content: str, filename: str = "импорт") -> ValidationResult:
        """
        Валидация содержимого файла для импорта стоп-слов
        
        Проверяет:
        - Размер содержимого
        - Количество слов
        - Валидность каждого слова
        - Дубликаты в файле
        
        Args:
            file_content: Содержимое файла в виде строки
            filename: Имя файла для логирования
            
        Returns:
            ValidationResult: Результат с валидными словами и статистикой
        """
        errors = []
        warnings = []
        
        logger.debug(f"Валидация файла импорта: {filename}")
        
        # Проверка размера файла
        file_size = len(file_content.encode('utf-8'))
        if file_size > self.MAX_IMPORT_FILE_SIZE:
            errors.append(f"Файл слишком большой ({file_size/1024/1024:.1f}MB). Максимум: 1MB")
            return ValidationResult(False, errors, warnings)
        
        # Извлечение слов из содержимого
        words = self.extract_words_from_text(file_content)
        
        # Проверка количества слов
        if len(words) == 0:
            errors.append("Файл не содержит валидных стоп-слов")
            return ValidationResult(False, errors, warnings)
        
        if len(words) > self.MAX_IMPORT_WORDS:
            errors.append(f"Слишком много слов для импорта ({len(words)}). Максимум: {self.MAX_IMPORT_WORDS}")
            return ValidationResult(False, errors, warnings)
        
        # Валидация каждого слова и сбор статистики
        valid_words = []
        invalid_words = []
        duplicate_words = set()
        seen_in_file = set()
        
        for word in words:
            normalized = self.normalize_stopword(word)
            
            # Проверка дубликатов внутри файла
            if normalized.lower() in seen_in_file:
                duplicate_words.add(normalized)
                continue
            
            seen_in_file.add(normalized.lower())
            
            # Валидация отдельного слова (без проверки существующих дубликатов)
            temp_validator = StopWordValidator(set())  # Пустой набор для проверки
            word_result = temp_validator.validate_user_input(word)
            
            if word_result.is_valid:
                # Дополнительная проверка на существующие стоп-слова
                if normalized.lower() in self.existing_stopwords:
                    warnings.append(f"Слово '{normalized}' уже существует (будет пропущено)")
                else:
                    valid_words.append(normalized)
            else:
                invalid_words.append((word, word_result.errors))
        
        # Формирование предупреждений и ошибок
        if duplicate_words:
            warnings.append(f"Найдено {len(duplicate_words)} дубликатов в файле: {', '.join(list(duplicate_words)[:5])}")
        
        if invalid_words:
            invalid_count = len(invalid_words)
            warnings.append(f"Найдено {invalid_count} невалидных слов (будут пропущены)")
            
        if len(valid_words) == 0:
            errors.append("Нет валидных слов для импорта")
        
        # Подготовка результата
        import_stats = {
            'valid_words': valid_words,
            'invalid_words': invalid_words,
            'duplicate_words': list(duplicate_words),
            'invalid_count': len(invalid_words),
            'duplicate_count': len(duplicate_words),
            'total_processed': len(words),
            'file_size': file_size
        }
        
        is_valid = len(errors) == 0 and len(valid_words) > 0
        
        logger.info(f"Валидация импорта завершена: valid={is_valid}, слов для импорта={len(valid_words)}")
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            normalized_data=import_stats if is_valid else None
        )
    
    def validate_export_filename(self, filename: str) -> ValidationResult:
        """
        Валидация имени файла для экспорта
        
        Проверяет:
        - Пустое имя
        - Недопустимые символы
        - Зарезервированные имена
        - Длину имени файла
        - Безопасность пути
        
        Args:
            filename: Имя файла для экспорта
            
        Returns:
            ValidationResult: Результат с нормализованным именем файла
        """
        errors = []
        warnings = []
        
        logger.debug(f"Валидация имени файла экспорта: '{filename}'")
        
        # Проверка на пустое имя
        if not filename or not filename.strip():
            errors.append("Имя файла не может быть пустым")
            return ValidationResult(False, errors, warnings)
        
        # Нормализация имени файла
        normalized_name = filename.strip()
        
        # Удаление небезопасных символов
        if re.search(r'[<>:"/\\|?*]', normalized_name):
            errors.append("Имя файла содержит недопустимые символы: < > : \" / \\ | ? *")
        
        # Проверка длины (Windows ограничение - 255 символов)
        if len(normalized_name) > 255:
            errors.append("Имя файла слишком длинное (максимум 255 символов)")
        
        # Проверка зарезервированных имен Windows
        name_without_ext = normalized_name.split('.')[0].lower()
        if name_without_ext in self.RESERVED_FILENAMES:
            errors.append(f"'{name_without_ext}' - зарезервированное имя файла")
        
        # Проверка на path traversal
        if '..' in normalized_name or '/' in normalized_name or '\\' in normalized_name:
            errors.append("Имя файла не должно содержать пути к каталогам")
        
        # Автоматическое добавление расширения .txt если отсутствует
        if not normalized_name.endswith('.txt'):
            normalized_name += '.txt'
            warnings.append("Автоматически добавлено расширение .txt")
        
        # Проверка безопасности
        if not self.check_word_safety(normalized_name):
            errors.append("Имя файла содержит потенциально опасное содержимое")
        
        is_valid = len(errors) == 0
        
        logger.info(f"Валидация имени файла завершена: valid={is_valid}, filename='{normalized_name}'")
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            normalized_data=normalized_name if is_valid else None
        )
    
    def validate_search_query(self, query: str) -> ValidationResult:
        """
        Валидация поискового запроса по стоп-словам
        
        Проверяет:
        - Пустой запрос
        - Минимальную длину
        - Разрешенные символы для поиска
        - Безопасность запроса
        
        Args:
            query: Поисковый запрос
            
        Returns:
            ValidationResult: Результат с нормализованным запросом
        """
        errors = []
        warnings = []
        
        logger.debug(f"Валидация поискового запроса: '{query}'")
        
        # Проверка на пустой запрос
        if not query or not query.strip():
            errors.append("Поисковый запрос не может быть пустым")
            return ValidationResult(False, errors, warnings)
        
        # Нормализация запроса
        normalized_query = query.strip().lower()
        
        # Проверка минимальной длины для эффективного поиска
        if len(normalized_query) < 1:
            errors.append("Поисковый запрос слишком короткий")
        
        # Проверка максимальной длины
        if len(normalized_query) > 100:
            errors.append("Поисковый запрос слишком длинный (максимум 100 символов)")
        
        # Более мягкая проверка символов для поиска (разрешаем пробелы и некоторые спецсимволы)
        search_pattern = re.compile(r'^[а-яёА-ЯЁa-zA-Z0-9\-_\s\*\?\.]+$', re.UNICODE)
        if not search_pattern.match(normalized_query):
            errors.append("Поисковый запрос содержит недопустимые символы")
        
        # Проверка безопасности
        if not self.check_word_safety(normalized_query):
            errors.append("Поисковый запрос содержит потенциально опасное содержимое")
        
        # Предупреждения
        if len(normalized_query) == 1:
            warnings.append("Поиск по одному символу может вернуть много результатов")
        
        if '*' in normalized_query or '?' in normalized_query:
            warnings.append("Использование wildcards (*,?) может замедлить поиск")
        
        is_valid = len(errors) == 0
        
        logger.info(f"Валидация поискового запроса завершена: valid={is_valid}, query='{normalized_query}'")
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            normalized_data=normalized_query if is_valid else None
        )
    
    def normalize_stopword(self, word: str) -> str:
        """
        Нормализация стоп-слова к стандартному виду
        
        Выполняет:
        - Удаление пробелов в начале и конце
        - Приведение к нижнему регистру
        - Удаление повторяющихся символов
        - Очистку от недопустимых символов на краях
        
        Args:
            word: Исходное слово
            
        Returns:
            str: Нормализованное слово
        """
        if not word:
            return ""
        
        # Удаление пробелов и приведение к нижнему регистру
        normalized = word.strip().lower()
        
        # Удаление недопустимых символов с краев (но не из середины)
        normalized = re.sub(r'^[^а-яёa-z0-9]+|[^а-яёa-z0-9]+$', '', normalized, flags=re.UNICODE)
        
        # Замена множественных дефисов на один
        normalized = re.sub(r'-+', '-', normalized)
        
        # Удаление дефисов с краев
        normalized = normalized.strip('-')
        
        logger.debug(f"Нормализация: '{word}' -> '{normalized}'")
        
        return normalized
    
    def extract_words_from_text(self, text: str) -> List[str]:
        """
        Извлечение отдельных слов из текста файла импорта
        
        Поддерживает различные форматы:
        - Построчное разделение
        - Разделение запятыми
        - Разделение пробелами
        - Игнорирование комментариев (#)
        
        Args:
            text: Текст для обработки
            
        Returns:
            List[str]: Список извлеченных слов
        """
        if not text:
            return []
        
        words = []
        
        # Обработка построчно
        for line in text.split('\n'):
            line = line.strip()
            
            # Игнорируем пустые строки и комментарии
            if not line or line.startswith('#'):
                continue
            
            # Удаляем комментарии в конце строки
            if '#' in line:
                line = line.split('#')[0].strip()
            
            # Разделение по различным разделителям
            # Сначала по запятым, затем по пробелам
            if ',' in line:
                line_words = [w.strip() for w in line.split(',')]
            else:
                line_words = line.split()
            
            # Добавляем непустые слова
            for word in line_words:
                word = word.strip()
                if word:
                    words.append(word)
        
        logger.debug(f"Извлечено {len(words)} слов из текста")
        
        return words
    
    def check_word_safety(self, word: str) -> bool:
        """
        Проверка безопасности слова (отсутствие вредного контента)
        
        Проверяет на:
        - XSS-атаки
        - SQL-инъекции
        - Shell-инъекции
        - Path traversal
        
        Args:
            word: Слово для проверки
            
        Returns:
            bool: True если слово безопасно
        """
        if not word:
            return True
        
        # Проверка каждого опасного паттерна
        for pattern in self.DANGEROUS_PATTERNS:
            if pattern.search(word):
                logger.warning(f"Обнаружен опасный паттерн в слове: '{word}'")
                return False
        
        return True
    
    def format_validation_errors(self, errors: List[str]) -> str:
        """
        Форматирование ошибок валидации для пользователя
        
        Создает удобочитаемое сообщение об ошибках с нумерацией
        и соответствующими эмодзи для улучшения UX.
        
        Args:
            errors: Список ошибок валидации
            
        Returns:
            str: Отформатированное сообщение об ошибках
        """
        if not errors:
            return "✅ Валидация прошла успешно"
        
        if len(errors) == 1:
            return f"❌ Ошибка валидации:\n{errors[0]}"
        
        # Множественные ошибки с нумерацией
        formatted_errors = [f"{i+1}. {error}" for i, error in enumerate(errors)]
        
        return f"❌ Найдено {len(errors)} ошибок валидации:\n" + "\n".join(formatted_errors)
    
    def update_existing_stopwords(self, stopwords: Set[str]) -> None:
        """
        Обновление набора существующих стоп-слов для проверки дубликатов
        
        Args:
            stopwords: Новый набор существующих стоп-слов
        """
        self.existing_stopwords = {word.lower() for word in stopwords}
        logger.debug(f"Обновлен набор существующих стоп-слов: {len(self.existing_stopwords)} слов")

    def validate_filename(self, filename: str) -> ValidationResult:
        """
        Валидация имени файла для экспорта

        Проверяет имя файла на соответствие требованиям безопасности
        и корректности для различных операционных систем.

        Args:
            filename (str): Имя файла для валидации

        Returns:
            ValidationResult: Результат валидации с нормализованным именем
        """
        errors = []
        warnings = []

        logger.debug(f"Валидация имени файла: '{filename}'")

        # Проверка на пустое имя
        if not filename or not filename.strip():
            errors.append("Имя файла не может быть пустым")
            return ValidationResult(False, errors, warnings)

        # Нормализация имени файла
        normalized_filename = filename.strip()

        # Проверка длины
        if len(normalized_filename) < 1:
            errors.append("Имя файла слишком короткое")

        if len(normalized_filename) > 50:
            errors.append("Имя файла слишком длинное (максимум 50 символов)")

        # Проверка на допустимые символы для имени файла
        # Разрешены: буквы, цифры, дефисы, подчеркивания
        filename_pattern = re.compile(r'^[а-яёА-ЯЁa-zA-Z0-9\-_]+$', re.UNICODE)
        if not filename_pattern.match(normalized_filename):
            errors.append("Имя файла может содержать только буквы, цифры, дефисы и подчеркивания")

        # Проверка на зарезервированные имена (Windows)
        if normalized_filename.lower() in self.RESERVED_FILENAMES:
            errors.append(f"'{normalized_filename}' является зарезервированным именем файла")

        # Проверка на опасные символы
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
        for char in dangerous_chars:
            if char in normalized_filename:
                errors.append(f"Имя файла содержит недопустимый символ: '{char}'")
                break

        # Предупреждения
        if normalized_filename.startswith('.'):
            warnings.append("Файлы начинающиеся с точки могут быть скрытыми")

        if len(normalized_filename) < 3:
            warnings.append("Короткие имена файлов могут быть неинформативными")

        is_valid = len(errors) == 0

        logger.info(f"Валидация имени файла завершена: valid={is_valid}, filename='{normalized_filename}'")

        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            normalized_data=normalized_filename if is_valid else None
        )

    def normalize_stopword(self, word: str) -> str:
        """
        Нормализация стоп-слова для единообразного хранения

        Приводит слово к стандартному виду: убирает лишние пробелы,
        приводит к нижнему регистру.

        Args:
            word (str): Исходное слово

        Returns:
            str: Нормализованное слово
        """
        if not word:
            return ""

        # Убираем лишние пробелы и приводим к нижнему регистру
        normalized = word.strip().lower()

        # Дополнительная очистка от невидимых символов
        normalized = ''.join(char for char in normalized if char.isprintable())

        return normalized

    def extract_words_from_text(self, text: str) -> List[str]:
        """
        Извлечение слов из текста файла импорта

        Парсит текст, игнорируя комментарии и пустые строки,
        извлекает отдельные слова для валидации.

        Args:
            text (str): Содержимое файла

        Returns:
            List[str]: Список извлеченных слов
        """
        words = []

        try:
            lines = text.split('\n')

            for line in lines:
                line = line.strip()

                # Пропускаем пустые строки и комментарии
                if not line or line.startswith('#'):
                    continue

                # Добавляем слово в список
                words.append(line)

        except Exception as e:
            logger.error(f"Ошибка при извлечении слов из текста: {e}")

        return words
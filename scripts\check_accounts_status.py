#!/usr/bin/env python3
"""
Скрипт для проверки статуса аккаунтов в системе
"""

import asyncio
import json
import sys
from pathlib import Path

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.settings import ACCOUNTS_METADATA_FILE, SESSIONS_DIR
from src.monitoring.telethon_manager import TelethonManager

async def check_accounts_status():
    """Проверка статуса аккаунтов в системе"""
    print("🔍 Проверка статуса аккаунтов...")
    print("=" * 50)
    
    # Проверяем файл метаданных
    print(f"📁 Файл метаданных: {ACCOUNTS_METADATA_FILE}")
    print(f"📁 Существует: {ACCOUNTS_METADATA_FILE.exists()}")
    
    if ACCOUNTS_METADATA_FILE.exists():
        with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        print(f"📄 Записей в метаданных: {len(metadata)}")
        
        for phone, data in metadata.items():
            print(f"\n📞 Аккаунт: {phone}")
            print(f"   📁 Файл сессии: {data.get('session_file', 'Не указан')}")
            print(f"   📊 Мониторинг: {'✅ Включен' if data.get('monitoring') else '❌ Отключен'}")
            print(f"   🔐 Требует авторизации: {'✅ Да' if data.get('needs_reauth') else '❌ Нет'}")
            print(f"   🕐 Обновлено: {data.get('last_updated', 'Неизвестно')}")
    else:
        print("❌ Файл метаданных не найден")
        return
    
    # Проверяем директорию сессий
    print(f"\n📁 Директория сессий: {SESSIONS_DIR}")
    print(f"📁 Существует: {SESSIONS_DIR.exists()}")
    
    if SESSIONS_DIR.exists():
        session_files = list(SESSIONS_DIR.glob('*.session'))
        print(f"📄 Файлов сессий: {len(session_files)}")
        for session_file in session_files:
            print(f"   - {session_file.name}")
    
    # Создаем TelethonManager и проверяем что он видит
    print(f"\n🔧 Проверка TelethonManager...")
    manager = TelethonManager()
    
    # Инициализируем менеджер
    await manager.initialize()
    
    # Получаем список подключенных аккаунтов
    accounts = manager.get_connected_accounts()
    print(f"📊 Аккаунтов в TelethonManager: {len(accounts)}")
    
    if accounts:
        print("\n📋 Аккаунты в системе:")
        for acc in accounts:
            status = "🟢 Активен" if acc['monitoring'] else "🔴 Неактивен"
            print(f"   - {acc['phone']}: {status}")
            print(f"     📁 Файл сессии: {acc['session_file']}")
    else:
        print("❌ TelethonManager не видит ни одного аккаунта")
    
    print(f"\n" + "=" * 50)
    print("📋 РЕЗЮМЕ:")
    print(f"   📄 Записей в метаданных: {len(metadata) if 'metadata' in locals() else 0}")
    print(f"   📁 Файлов сессий: {len(session_files) if 'session_files' in locals() else 0}")
    print(f"   🔧 Аккаунтов в TelethonManager: {len(accounts)}")
    
    if len(accounts) == 0 and len(metadata) > 0:
        print("\n⚠️ ПРОБЛЕМА: Метаданные есть, но TelethonManager не видит аккаунты")
        print("💡 Возможные причины:")
        print("   1. Сессии не авторизованы (нужна повторная авторизация)")
        print("   2. Неправильные пути к файлам сессий")
        print("   3. Проблемы с API_ID/API_HASH")
        print("\n🔧 Рекомендации:")
        print("   1. Удалите старые сессии и создайте новые через бота")
        print("   2. Проверьте настройки в .env файле")
        print("   3. Используйте команду 'Подключить аккаунт' в боте")

if __name__ == "__main__":
    asyncio.run(check_accounts_status())

"""
Юнит-тесты для клавиатур PM Searcher Bot

Покрывает все функции создания клавиатур и переходы между FSM состояниями.
"""

import pytest
from unittest.mock import patch

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton, ReplyKeyboardMarkup, KeyboardButton

from src.bot.keyboards import (
    get_keyboard, stopwords_main_keyboard, stopwords_pagination_keyboard,
    cancel_operation_keyboard, back_to_stopwords_keyboard,
    delete_confirmation_keyboard, import_confirmation_keyboard,
    search_results_keyboard
)
from src.config.environment import env_config
from tests.fixtures.test_constants import TEST_ADMIN_USER_ID, TEST_REGULAR_USER_ID
from tests.utils.test_helpers import extract_callback_data, count_keyboard_buttons


class TestGetKeyboard:
    """Тесты функции get_keyboard()"""
    
    def test_get_keyboard_regular_user(self):
        """Тест клавиатуры для обычного пользователя"""
        with patch.object(env_config, 'is_admin', return_value=False):
            keyboard = get_keyboard(TEST_REGULAR_USER_ID)
            
            assert isinstance(keyboard, ReplyKeyboardMarkup)
            assert keyboard.resize_keyboard is True
            assert keyboard.one_time_keyboard in [False, None]  # Может быть None в новых версиях aiogram
            
            # Проверяем наличие основных кнопок
            buttons_text = []
            for row in keyboard.keyboard:
                for button in row:
                    buttons_text.append(button.text)
            
            assert "Подключить аккаунт" in buttons_text
            assert "Мои аккаунты" in buttons_text
            assert "Управление стоп-словами" in buttons_text

            # Для обычного пользователя не должно быть админских кнопок
            assert "Все подключенные аккаунты" not in buttons_text
            assert "Статистика" not in buttons_text
    
    def test_get_keyboard_admin_user(self):
        """Тест клавиатуры для администратора"""
        with patch.object(env_config, 'is_admin', return_value=True):
            keyboard = get_keyboard(TEST_ADMIN_USER_ID)
            
            assert isinstance(keyboard, ReplyKeyboardMarkup)
            
            # Получаем все тексты кнопок
            buttons_text = []
            for row in keyboard.keyboard:
                for button in row:
                    buttons_text.append(button.text)
            
            # Проверяем наличие основных кнопок
            assert "Подключить аккаунт" in buttons_text
            assert "Мои аккаунты" in buttons_text
            assert "Управление стоп-словами" in buttons_text

            # Проверяем наличие админских кнопок
            assert "Все подключенные аккаунты" in buttons_text
            assert "Статистика" in buttons_text
    
    def test_get_keyboard_different_user_ids(self):
        """Тест клавиатуры для различных user_id"""
        test_user_ids = [1, 123456789, 999999999, 0, -1]
        
        for user_id in test_user_ids:
            with patch.object(env_config, 'is_admin', return_value=False):
                keyboard = get_keyboard(user_id)
                
                assert isinstance(keyboard, ReplyKeyboardMarkup)
                assert len(keyboard.keyboard) > 0
                
                # Проверяем что клавиатура содержит основные кнопки
                buttons_text = []
                for row in keyboard.keyboard:
                    for button in row:
                        buttons_text.append(button.text)
                
                assert len(buttons_text) >= 3  # Минимум 3 основные кнопки

    def test_get_keyboard_multiple_admins(self):
        """Тест клавиатуры для нескольких администраторов"""
        admin_ids = [7738438019, 7506112063, 1290467190]

        for admin_id in admin_ids:
            with patch.object(env_config, 'is_admin', return_value=True):
                keyboard = get_keyboard(admin_id)

                assert isinstance(keyboard, ReplyKeyboardMarkup)

                # Получаем все тексты кнопок
                buttons_text = []
                for row in keyboard.keyboard:
                    for button in row:
                        buttons_text.append(button.text)

                # Проверяем наличие основных кнопок
                assert "Подключить аккаунт" in buttons_text
                assert "Мои аккаунты" in buttons_text
                assert "Управление стоп-словами" in buttons_text

                # Проверяем наличие админских кнопок
                assert "Все подключенные аккаунты" in buttons_text, f"Admin {admin_id} should have admin buttons"
                assert "Статистика" in buttons_text, f"Admin {admin_id} should have admin buttons"

                # Проверяем общее количество кнопок (должно быть 5 для админов)
                assert len(buttons_text) == 5, f"Admin {admin_id} should have 5 buttons, got {len(buttons_text)}"

    def test_get_keyboard_properties(self):
        """Тест свойств клавиатуры"""
        with patch.object(env_config, 'is_admin', return_value=False):
            keyboard = get_keyboard(TEST_REGULAR_USER_ID)

            # Проверяем основные свойства
            assert keyboard.resize_keyboard is True
            assert keyboard.one_time_keyboard in [False, None]  # Может быть None в новых версиях aiogram
            assert keyboard.input_field_placeholder is None
            assert keyboard.selective is None


class TestAccountConnectionKeyboards:
    """Тесты клавиатур для подключения аккаунтов"""

    def test_cancel_phone_input_keyboard_structure(self):
        """Тест структуры клавиатуры отмены ввода номера телефона"""
        from src.bot.keyboards import cancel_phone_input_keyboard

        keyboard = cancel_phone_input_keyboard()

        # Проверяем тип клавиатуры
        assert isinstance(keyboard, InlineKeyboardMarkup)

        # Проверяем структуру
        assert len(keyboard.inline_keyboard) == 1  # Одна строка кнопок
        assert len(keyboard.inline_keyboard[0]) == 1  # Одна кнопка в строке

        # Проверяем кнопку
        button = keyboard.inline_keyboard[0][0]
        assert isinstance(button, InlineKeyboardButton)
        assert button.text == "❌ Отменить"
        assert button.callback_data == "cancel_phone_input"

    def test_cancel_account_connection_keyboard_structure(self):
        """Тест структуры клавиатуры отмены подключения аккаунта"""
        from src.bot.keyboards import cancel_account_connection_keyboard

        keyboard = cancel_account_connection_keyboard()

        # Проверяем тип клавиатуры
        assert isinstance(keyboard, InlineKeyboardMarkup)

        # Проверяем структуру
        assert len(keyboard.inline_keyboard) == 2  # Две строки кнопок
        assert len(keyboard.inline_keyboard[0]) == 1  # Одна кнопка в первой строке
        assert len(keyboard.inline_keyboard[1]) == 1  # Одна кнопка во второй строке

        # Проверяем кнопки
        reauth_button = keyboard.inline_keyboard[0][0]
        cancel_button = keyboard.inline_keyboard[1][0]

        assert isinstance(reauth_button, InlineKeyboardButton)
        assert reauth_button.text == "🔄 Переавторизация"
        assert reauth_button.callback_data == "force_reauth_account"

        assert isinstance(cancel_button, InlineKeyboardButton)
        assert cancel_button.text == "❌ Отмена"
        assert cancel_button.callback_data == "cancel_account_connection"

    def test_cancel_phone_input_keyboard_callback_data(self):
        """Тест callback_data клавиатуры отмены ввода номера"""
        from src.bot.keyboards import cancel_phone_input_keyboard

        keyboard = cancel_phone_input_keyboard()
        callback_data = extract_callback_data(keyboard)

        assert len(callback_data) == 1
        assert callback_data[0] == "cancel_phone_input"

    def test_cancel_account_connection_keyboard_callback_data(self):
        """Тест callback_data клавиатуры отмены подключения аккаунта"""
        from src.bot.keyboards import cancel_account_connection_keyboard

        keyboard = cancel_account_connection_keyboard()
        callback_data = extract_callback_data(keyboard)

        assert len(callback_data) == 2
        assert "force_reauth_account" in callback_data
        assert "cancel_account_connection" in callback_data


class TestStopwordsMainKeyboard:
    """Тесты функции stopwords_main_keyboard()"""
    
    def test_stopwords_main_keyboard_structure(self):
        """Тест структуры главной клавиатуры стоп-слов"""
        keyboard = stopwords_main_keyboard()
        
        assert isinstance(keyboard, InlineKeyboardMarkup)
        assert len(keyboard.inline_keyboard) > 0
        
        # Получаем все callback_data
        callback_data = extract_callback_data(keyboard)
        
        # Проверяем наличие основных действий
        expected_callbacks = [
            "sw_list",
            "sw_add", 
            "sw_search",
            "sw_import",
            "sw_export"
        ]
        
        for expected in expected_callbacks:
            assert any(expected in callback for callback in callback_data), f"Missing callback: {expected}"
    
    def test_stopwords_main_keyboard_button_count(self):
        """Тест количества кнопок в главной клавиатуре"""
        keyboard = stopwords_main_keyboard()
        
        total_buttons = count_keyboard_buttons(keyboard)
        assert total_buttons >= 5  # Минимум 5 основных действий
        assert total_buttons <= 10  # Не слишком много кнопок
    
    def test_stopwords_main_keyboard_button_texts(self):
        """Тест текстов кнопок в главной клавиатуре"""
        keyboard = stopwords_main_keyboard()
        
        # Получаем все тексты кнопок
        button_texts = []
        for row in keyboard.inline_keyboard:
            for button in row:
                button_texts.append(button.text)
        
        # Проверяем наличие ожидаемых текстов
        expected_texts = [
            "📋 Просмотр стоп-слов",
            "➕ Добавить стоп-слово",
            "🔍 Поиск стоп-слов",
            "📥 Импорт",
            "📤 Экспорт"
        ]
        
        for expected_text in expected_texts:
            assert any(expected_text in text for text in button_texts), f"Missing button text: {expected_text}"


class TestStopwordsPaginationKeyboard:
    """Тесты функции stopwords_pagination_keyboard()"""
    
    def test_pagination_keyboard_first_page(self):
        """Тест клавиатуры пагинации для первой страницы"""
        keyboard = stopwords_pagination_keyboard(
            current_page=1,
            total_pages=5
        )
        
        assert isinstance(keyboard, InlineKeyboardMarkup)
        
        callback_data = extract_callback_data(keyboard)
        
        # На первой странице должна быть только кнопка "Вперед"
        assert any("sw_page:2" in callback for callback in callback_data)
        assert not any("sw_page:0" in callback for callback in callback_data)
    
    def test_pagination_keyboard_middle_page(self):
        """Тест клавиатуры пагинации для средней страницы"""
        keyboard = stopwords_pagination_keyboard(
            current_page=3,
            total_pages=5
        )
        
        callback_data = extract_callback_data(keyboard)
        
        # На средней странице должны быть кнопки "Назад" и "Вперед"
        assert any("sw_page:2" in callback for callback in callback_data)  # Предыдущая
        assert any("sw_page:4" in callback for callback in callback_data)  # Следующая
    
    def test_pagination_keyboard_last_page(self):
        """Тест клавиатуры пагинации для последней страницы"""
        keyboard = stopwords_pagination_keyboard(
            current_page=5,
            total_pages=5
        )
        
        callback_data = extract_callback_data(keyboard)
        
        # На последней странице должна быть только кнопка "Назад"
        assert any("sw_page:4" in callback for callback in callback_data)
        assert not any("sw_page:6" in callback for callback in callback_data)
    
    def test_pagination_keyboard_single_page(self):
        """Тест клавиатуры пагинации для единственной страницы"""
        keyboard = stopwords_pagination_keyboard(
            current_page=1,
            total_pages=1
        )
        
        callback_data = extract_callback_data(keyboard)
        
        # Не должно быть кнопок навигации по страницам
        assert not any("sw_page:" in callback for callback in callback_data)
    
    def test_pagination_keyboard_edge_cases(self):
        """Тест граничных случаев пагинации"""
        # Тест с нулевыми страницами
        keyboard = stopwords_pagination_keyboard(0, 0)
        assert isinstance(keyboard, InlineKeyboardMarkup)

        # Тест с отрицательными значениями
        keyboard = stopwords_pagination_keyboard(-1, -1)
        assert isinstance(keyboard, InlineKeyboardMarkup)

        # Тест с очень большими значениями
        keyboard = stopwords_pagination_keyboard(999, 1000)
        assert isinstance(keyboard, InlineKeyboardMarkup)


class TestCancelOperationKeyboard:
    """Тесты функции cancel_operation_keyboard()"""
    
    def test_cancel_operation_keyboard_structure(self):
        """Тест структуры клавиатуры отмены операции"""
        keyboard = cancel_operation_keyboard()
        
        assert isinstance(keyboard, InlineKeyboardMarkup)
        assert len(keyboard.inline_keyboard) == 1  # Одна строка
        assert len(keyboard.inline_keyboard[0]) == 1  # Одна кнопка
        
        button = keyboard.inline_keyboard[0][0]
        assert "❌" in button.text or "Отмена" in button.text
        assert button.callback_data == "cancel_current_operation"
    
    def test_cancel_operation_keyboard_callback(self):
        """Тест callback_data кнопки отмены"""
        keyboard = cancel_operation_keyboard()
        
        callback_data = extract_callback_data(keyboard)
        assert len(callback_data) == 1
        assert callback_data[0] == "cancel_current_operation"


class TestBackToStopwordsKeyboard:
    """Тесты функции back_to_stopwords_keyboard()"""
    
    def test_back_to_stopwords_keyboard_structure(self):
        """Тест структуры клавиатуры возврата к стоп-словам"""
        keyboard = back_to_stopwords_keyboard()
        
        assert isinstance(keyboard, InlineKeyboardMarkup)
        assert len(keyboard.inline_keyboard) >= 1
        
        callback_data = extract_callback_data(keyboard)
        assert "back_to_stopwords_main" in callback_data
    
    def test_back_to_stopwords_keyboard_button_text(self):
        """Тест текста кнопки возврата"""
        keyboard = back_to_stopwords_keyboard()
        
        button_texts = []
        for row in keyboard.inline_keyboard:
            for button in row:
                button_texts.append(button.text)
        
        # Должна быть кнопка с текстом о возврате
        assert any("◀️" in text or "Назад" in text for text in button_texts)


class TestDeleteConfirmationKeyboard:
    """Тесты функции delete_confirmation_keyboard()"""
    
    def test_delete_confirmation_keyboard_structure(self):
        """Тест структуры клавиатуры подтверждения удаления"""
        word = "тестовое_слово"
        keyboard = delete_confirmation_keyboard(word)
        
        assert isinstance(keyboard, InlineKeyboardMarkup)
        
        callback_data = extract_callback_data(keyboard)
        
        # Должны быть кнопки подтверждения и отмены
        assert any(f"sw_delete:confirm:{word}" in callback for callback in callback_data)
        assert any("sw_delete:cancel" in callback for callback in callback_data)
    
    def test_delete_confirmation_keyboard_different_words(self):
        """Тест клавиатуры подтверждения для разных слов"""
        test_words = ["спам", "реклама", "test_word", "слово-123"]
        
        for word in test_words:
            keyboard = delete_confirmation_keyboard(word)
            
            callback_data = extract_callback_data(keyboard)
            
            # Проверяем что слово корректно встроено в callback_data
            assert any(f"sw_delete:confirm:{word}" in callback for callback in callback_data)
            assert any("sw_delete:cancel" in callback for callback in callback_data)
    
    def test_delete_confirmation_keyboard_special_characters(self):
        """Тест клавиатуры подтверждения со специальными символами"""
        special_words = ["слово:с:двоеточием", "слово|с|пайпом", "слово_с_подчеркиванием"]
        
        for word in special_words:
            keyboard = delete_confirmation_keyboard(word)
            
            # Клавиатура должна создаваться без ошибок
            assert isinstance(keyboard, InlineKeyboardMarkup)
            
            callback_data = extract_callback_data(keyboard)
            assert len(callback_data) >= 2  # Минимум 2 кнопки


class TestImportConfirmationKeyboard:
    """Тесты функции import_confirmation_keyboard()"""
    
    def test_import_confirmation_keyboard_structure(self):
        """Тест структуры клавиатуры подтверждения импорта"""
        preview_data = {'new_words': 5, 'duplicates': 2, 'invalid': 1}
        keyboard = import_confirmation_keyboard(preview_data)

        assert isinstance(keyboard, InlineKeyboardMarkup)

        callback_data = extract_callback_data(keyboard)

        # Должны быть кнопки подтверждения и отмены импорта
        assert any("sw_import:confirm" in callback for callback in callback_data)
        assert any("sw_import:cancel" in callback for callback in callback_data)
    
    def test_import_confirmation_keyboard_button_texts(self):
        """Тест текстов кнопок подтверждения импорта"""
        preview_data = {'new_words': 10, 'duplicates': 3, 'invalid': 0}
        keyboard = import_confirmation_keyboard(preview_data)

        button_texts = []
        for row in keyboard.inline_keyboard:
            for button in row:
                button_texts.append(button.text)

        # Должны быть кнопки с соответствующими текстами
        assert any("✅" in text or "Импортировать" in text for text in button_texts)
        assert any("❌" in text or "Отменить" in text for text in button_texts)


class TestSearchResultsKeyboard:
    """Тесты функции search_results_keyboard()"""
    
    def test_search_results_keyboard_structure(self):
        """Тест структуры клавиатуры результатов поиска"""
        keyboard = search_results_keyboard(
            results_count=5,
            current_page=1,
            total_pages=2,
            query="тест"
        )

        assert isinstance(keyboard, InlineKeyboardMarkup)

        callback_data = extract_callback_data(keyboard)

        # Должна быть кнопка возврата к меню стоп-слов
        assert any("back_to_stopwords_main" in callback for callback in callback_data)
    
    def test_search_results_keyboard_minimal_structure(self):
        """Тест минимальной структуры клавиатуры результатов"""
        keyboard = search_results_keyboard(
            results_count=0,
            current_page=1,
            total_pages=1,
            query="пустой"
        )

        # Должна быть хотя бы одна кнопка
        total_buttons = count_keyboard_buttons(keyboard)
        assert total_buttons >= 1


class TestKeyboardsEdgeCases:
    """Тесты граничных случаев для клавиатур"""
    
    def test_keyboards_with_none_parameters(self):
        """Тест клавиатур с None параметрами"""
        # Тест get_keyboard с None user_id
        try:
            keyboard = get_keyboard(None)
            # Если не падает, проверяем что это валидная клавиатура
            assert isinstance(keyboard, ReplyKeyboardMarkup)
        except (TypeError, AttributeError):
            # Ожидаемое поведение для None user_id
            pass
        
        # Тест delete_confirmation_keyboard с None word
        try:
            keyboard = delete_confirmation_keyboard(None)
            assert isinstance(keyboard, InlineKeyboardMarkup)
        except (TypeError, AttributeError):
            # Ожидаемое поведение для None word
            pass
    
    def test_keyboards_with_empty_strings(self):
        """Тест клавиатур с пустыми строками"""
        # Тест delete_confirmation_keyboard с пустым словом
        keyboard = delete_confirmation_keyboard("")
        assert isinstance(keyboard, InlineKeyboardMarkup)
        
        callback_data = extract_callback_data(keyboard)
        # Должны быть callback_data даже с пустым словом
        assert len(callback_data) >= 2
    
    def test_keyboards_memory_usage(self):
        """Тест использования памяти клавиатурами"""
        # Создаем много клавиатур для проверки утечек памяти
        keyboards = []
        
        for i in range(100):
            keyboards.append(get_keyboard(i))
            keyboards.append(stopwords_main_keyboard())
            keyboards.append(cancel_operation_keyboard())
        
        # Проверяем что все клавиатуры созданы корректно
        assert len(keyboards) == 300
        
        for keyboard in keyboards:
            assert isinstance(keyboard, (ReplyKeyboardMarkup, InlineKeyboardMarkup))
    
    def test_keyboards_consistency(self):
        """Тест консистентности клавиатур"""
        # Проверяем что одинаковые вызовы возвращают одинаковые результаты
        keyboard1 = stopwords_main_keyboard()
        keyboard2 = stopwords_main_keyboard()
        
        # Сравниваем структуру
        assert len(keyboard1.inline_keyboard) == len(keyboard2.inline_keyboard)
        
        callback_data1 = extract_callback_data(keyboard1)
        callback_data2 = extract_callback_data(keyboard2)
        
        assert callback_data1 == callback_data2
    
    def test_keyboards_large_parameters(self):
        """Тест клавиатур с большими параметрами"""
        # Тест пагинации с большими числами
        keyboard = stopwords_pagination_keyboard(
            current_page=999999,
            total_pages=1000000
        )
        
        assert isinstance(keyboard, InlineKeyboardMarkup)
        
        # Тест delete_confirmation с длинным словом
        long_word = "очень_длинное_стоп_слово_для_тестирования_граничных_случаев" * 10
        keyboard = delete_confirmation_keyboard(long_word)
        
        assert isinstance(keyboard, InlineKeyboardMarkup)

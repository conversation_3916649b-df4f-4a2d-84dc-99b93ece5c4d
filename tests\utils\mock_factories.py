"""
Фабрики для создания моков в тестах PM Searcher Bot
"""

from typing import Dict, Any, List, Optional
from unittest.mock import Mock, AsyncMock, MagicMock
from dataclasses import dataclass

from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.types import Message, CallbackQuery, User, Chat, Update, Document
from aiogram.fsm.context import FSMContext
from telethon import TelegramClient
from telethon.events import NewMessage
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError

from src.utils.stopwords import StopWordsManager
from src.monitoring.telethon_manager import TelethonManager
from src.bot.alerts import AlertManager
from src.bot.validators import ValidationResult


@dataclass
class MockUserData:
    """Данные для создания мока пользователя"""
    user_id: int = *********
    username: str = "testuser"
    first_name: str = "Test"
    last_name: str = "User"
    is_admin: bool = False
    language_code: str = "ru"


class MockFactory:
    """Фабрика для создания различных моков"""
    
    @staticmethod
    def create_user(data: MockUserData = None) -> User:
        """Создание мока пользователя"""
        if data is None:
            data = MockUserData()
            
        return User(
            id=data.user_id,
            is_bot=False,
            first_name=data.first_name,
            last_name=data.last_name,
            username=data.username,
            language_code=data.language_code
        )
    
    @staticmethod
    def create_chat(chat_id: int = -*********, chat_type: str = "private") -> Chat:
        """Создание мока чата"""
        return Chat(id=chat_id, type=chat_type)
    
    @staticmethod
    def create_message(
        user_data: MockUserData = None,
        text: str = "Test message",
        message_id: int = 1
    ) -> Mock:
        """Создание мока сообщения"""
        if user_data is None:
            user_data = MockUserData()
            
        user = MockFactory.create_user(user_data)
        chat = MockFactory.create_chat(user_data.user_id)
        
        message = Mock(spec=Message)
        message.from_user = user
        message.chat = chat
        message.text = text
        message.message_id = message_id
        message.answer = AsyncMock()
        message.reply = AsyncMock()
        message.edit_text = AsyncMock()
        message.document = None
        
        return message
    
    @staticmethod
    def create_callback_query(
        user_data: MockUserData = None,
        callback_data: str = "test_callback"
    ) -> Mock:
        """Создание мока callback query"""
        if user_data is None:
            user_data = MockUserData()
            
        user = MockFactory.create_user(user_data)
        message = MockFactory.create_message(user_data)
        
        callback = Mock(spec=CallbackQuery)
        callback.from_user = user
        callback.message = message
        callback.data = callback_data
        callback.answer = AsyncMock()
        callback.edit_message_text = AsyncMock()
        callback.edit_message_reply_markup = AsyncMock()
        
        return callback
    
    @staticmethod
    def create_document(
        file_name: str = "test.txt",
        file_size: int = 1024,
        mime_type: str = "text/plain"
    ) -> Mock:
        """Создание мока документа"""
        document = Mock(spec=Document)
        document.file_name = file_name
        document.file_size = file_size
        document.mime_type = mime_type
        document.file_id = "test_file_id"
        
        return document
    
    @staticmethod
    def create_bot() -> Mock:
        """Создание мока бота"""
        bot = Mock(spec=Bot)
        bot.send_message = AsyncMock()
        bot.edit_message_text = AsyncMock()
        bot.edit_message_reply_markup = AsyncMock()
        bot.answer_callback_query = AsyncMock()
        bot.get_file = AsyncMock()
        bot.download_file = AsyncMock()
        bot.session = Mock()
        bot.session.close = AsyncMock()
        
        return bot
    
    @staticmethod
    def create_fsm_context(initial_data: Dict[str, Any] = None) -> Mock:
        """Создание мока FSM контекста"""
        if initial_data is None:
            initial_data = {}
            
        context = Mock(spec=FSMContext)
        context.set_state = AsyncMock()
        context.get_state = AsyncMock(return_value=None)
        context.clear = AsyncMock()
        context.set_data = AsyncMock()
        context.get_data = AsyncMock(return_value=initial_data)
        context.update_data = AsyncMock()
        
        return context


class TelethonMockFactory:
    """Фабрика для создания моков Telethon"""
    
    @staticmethod
    def create_client(
        is_authorized: bool = True,
        should_raise_on_auth: Optional[Exception] = None
    ) -> Mock:
        """Создание мока TelegramClient"""
        client = Mock(spec=TelegramClient)
        client.connect = AsyncMock()
        client.disconnect = AsyncMock()
        client.is_user_authorized = AsyncMock(return_value=is_authorized)
        client.send_code_request = AsyncMock()
        client.add_event_handler = Mock()
        client.remove_event_handler = Mock()
        client.run_until_disconnected = AsyncMock()
        
        if should_raise_on_auth:
            client.sign_in = AsyncMock(side_effect=should_raise_on_auth)
        else:
            client.sign_in = AsyncMock()
            
        return client
    
    @staticmethod
    def create_new_message_event(
        text: str = "Test message",
        is_outgoing: bool = True,
        sender_id: int = *********
    ) -> Mock:
        """Создание мока события нового сообщения"""
        event = Mock(spec=NewMessage.Event)
        event.message = Mock()
        event.message.text = text
        event.message.out = is_outgoing
        event.message.sender_id = sender_id
        event.message.date = None
        
        return event
    
    @staticmethod
    def create_telethon_manager(
        connected_accounts: List[Dict[str, Any]] = None
    ) -> Mock:
        """Создание мока TelethonManager"""
        if connected_accounts is None:
            connected_accounts = []
            
        manager = Mock(spec=TelethonManager)
        manager.clients = {}
        manager.stopwords_manager = Mock()
        manager.start_auth = AsyncMock(return_value=True)
        manager.complete_auth = AsyncMock(return_value=True)
        manager.start_monitoring = AsyncMock()
        manager.disconnect_client = AsyncMock()
        manager.get_connected_accounts = Mock(return_value=connected_accounts)
        manager.reload_stopwords = Mock(return_value=5)
        
        return manager


class ValidationMockFactory:
    """Фабрика для создания моков валидации"""
    
    @staticmethod
    def create_validation_result(
        is_valid: bool = True,
        errors: List[str] = None,
        warnings: List[str] = None,
        normalized_data: Any = None
    ) -> ValidationResult:
        """Создание результата валидации"""
        if errors is None:
            errors = []
        if warnings is None:
            warnings = []
            
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            normalized_data=normalized_data
        )
    
    @staticmethod
    def create_validator_mock() -> Mock:
        """Создание мока валидатора"""
        validator = Mock()
        validator.validate_stopword = Mock(
            return_value=ValidationMockFactory.create_validation_result()
        )
        validator.validate_import_file = Mock(
            return_value=ValidationMockFactory.create_validation_result()
        )
        validator.validate_export_filename = Mock(
            return_value=ValidationMockFactory.create_validation_result()
        )
        validator.check_word_safety = Mock(return_value=True)
        
        return validator


class StopWordsMockFactory:
    """Фабрика для создания моков управления стоп-словами"""
    
    @staticmethod
    def create_stopwords_manager(
        initial_words: List[str] = None
    ) -> Mock:
        """Создание мока StopWordsManager"""
        if initial_words is None:
            initial_words = ["спам", "реклама", "продажа"]
            
        manager = Mock(spec=StopWordsManager)
        manager.stopwords = initial_words.copy()
        manager.load_stopwords = Mock(return_value=initial_words.copy())
        manager.reload_stopwords = Mock(return_value=len(initial_words))
        manager.contains_stopwords = Mock(return_value=(False, []))
        manager.add_stopword = Mock(return_value=True)
        manager.remove_stopword = Mock(return_value=True)
        manager.get_stats = Mock(return_value={
            'total_count': len(initial_words),
            'words': initial_words[:10]
        })
        
        return manager


class AlertMockFactory:
    """Фабрика для создания моков алертов"""
    
    @staticmethod
    def create_alert_manager(bot: Mock = None) -> Mock:
        """Создание мока AlertManager"""
        if bot is None:
            bot = MockFactory.create_bot()
            
        manager = Mock(spec=AlertManager)
        manager.bot = bot
        manager.send_alert_to_user = AsyncMock()
        manager.send_alert_to_admins = AsyncMock()
        manager.send_alert_to_all = AsyncMock()
        manager.send_admin_notification = AsyncMock()
        
        return manager


class ErrorMockFactory:
    """Фабрика для создания моков ошибок"""
    
    @staticmethod
    def create_telethon_errors() -> Dict[str, Exception]:
        """Создание моков ошибок Telethon"""
        return {
            "session_password_needed": SessionPasswordNeededError("2FA required"),
            "phone_code_invalid": PhoneCodeInvalidError("Invalid code"),
            "flood_wait": FloodWaitError("Too many requests", seconds=30),
            "connection_error": ConnectionError("Network error")
        }
    
    @staticmethod
    def create_file_errors() -> Dict[str, Exception]:
        """Создание моков файловых ошибок"""
        return {
            "file_not_found": FileNotFoundError("File not found"),
            "permission_error": PermissionError("Permission denied"),
            "io_error": IOError("I/O operation failed"),
            "unicode_error": UnicodeDecodeError("utf-8", b"", 0, 1, "invalid start byte")
        }


# Удобные функции для быстрого создания часто используемых моков

def create_admin_user() -> MockUserData:
    """Создание данных администратора"""
    return MockUserData(
        user_id=*********,
        username="admin",
        first_name="Admin",
        is_admin=True
    )


def create_regular_user() -> MockUserData:
    """Создание данных обычного пользователя"""
    return MockUserData(
        user_id=*********,
        username="user",
        first_name="User",
        is_admin=False
    )


def create_test_accounts() -> List[Dict[str, Any]]:
    """Создание тестовых аккаунтов"""
    return [
        {
            'user_id': *********,
            'phone': '+***********',
            'monitoring': True
        },
        {
            'user_id': *********,
            'phone': '+***********',
            'monitoring': False
        }
    ]

"""
Обработчики управления настройками мониторинга
"""

import logging
from datetime import datetime
from aiogram import Dispatcher, types
from aiogram.fsm.context import FSMContext

from ...config.environment import env_config
from ...config.monitoring_settings import monitoring_settings
from ...utils.logging import setup_logging
from ..keyboards import monitoring_settings_keyboard, back_to_admin_keyboard

logger = setup_logging(__name__, 'bot.log')

def register_monitoring_settings_handlers(dp: Dispatcher, telethon_manager):
    """Регистрация обработчиков управления настройками мониторинга"""
    
    @dp.message(lambda m: m.text == "⚙️ Настройки мониторинга")
    async def show_monitoring_settings(message: types.Message, state: FSMContext):
        """Показ настроек мониторинга"""
        user_id = message.from_user.id
        
        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return
        
        # Очистка состояния
        await state.clear()
        
        # Получение текущих настроек
        status = monitoring_settings.get_monitoring_status()
        
        # Форматирование времени последнего обновления
        last_updated = status.get('last_updated')
        if last_updated:
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(last_updated)
                last_updated_str = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                last_updated_str = "Неизвестно"
        else:
            last_updated_str = "Никогда"
        
        # Статус мониторинга
        group_status = "✅ Включен" if status['group_chat_monitoring'] else "❌ Отключен"
        private_status = "✅ Включен" if status['private_chat_monitoring'] else "❌ Отключен"
        
        text = (
            f"⚙️ **Настройки мониторинга**\n\n"
            f"📊 **Текущее состояние:**\n"
            f"👥 Групповые чаты: {group_status}\n"
            f"👤 Личные сообщения: {private_status}\n\n"
            f"📅 **Последнее изменение:**\n"
            f"🕐 Время: {last_updated_str}\n"
            f"👤 Пользователь: {status.get('updated_by', 'Неизвестно')}\n\n"
            f"💡 **Информация:**\n"
            f"• Мониторинг личных сообщений всегда включен\n"
            f"• Мониторинг групповых чатов можно включать/отключать\n"
            f"• Изменения применяются ко всем активным аккаунтам\n\n"
            f"👇 Выберите действие:"
        )
        
        await message.answer(
            text,
            parse_mode='Markdown',
            reply_markup=monitoring_settings_keyboard(status['group_chat_monitoring'])
        )
        
        logger.info(f"Пользователь {user_id} просмотрел настройки мониторинга")
    
    @dp.callback_query(lambda c: c.data.startswith("monitoring_"))
    async def handle_monitoring_settings_callback(callback: types.CallbackQuery, state: FSMContext):
        """Обработка callback для настроек мониторинга"""
        user_id = callback.from_user.id
        
        # Только для администратора
        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return
        
        action = callback.data.split("_", 1)[1]
        
        if action == "toggle_groups":
            # Переключение мониторинга групповых чатов
            current_status = monitoring_settings.group_chat_monitoring_enabled
            new_status = not current_status
            
            success = monitoring_settings.set_group_chat_monitoring(new_status, user_id)
            
            if success:
                # Перезагружаем настройки в TelethonManager
                telethon_manager.reload_monitoring_settings()
                
                status_text = "включен" if new_status else "отключен"
                await callback.answer(f"✅ Мониторинг групповых чатов {status_text}", show_alert=True)
                
                # Обновляем сообщение
                status = monitoring_settings.get_monitoring_status()
                
                # Форматирование времени
                last_updated = status.get('last_updated')
                if last_updated:
                    try:
                        dt = datetime.fromisoformat(last_updated)
                        last_updated_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        last_updated_str = "Неизвестно"
                else:
                    last_updated_str = "Никогда"
                
                group_status = "✅ Включен" if status['group_chat_monitoring'] else "❌ Отключен"
                private_status = "✅ Включен" if status['private_chat_monitoring'] else "❌ Отключен"
                
                text = (
                    f"⚙️ **Настройки мониторинга**\n\n"
                    f"📊 **Текущее состояние:**\n"
                    f"👥 Групповые чаты: {group_status}\n"
                    f"👤 Личные сообщения: {private_status}\n\n"
                    f"📅 **Последнее изменение:**\n"
                    f"🕐 Время: {last_updated_str}\n"
                    f"👤 Пользователь: {status.get('updated_by', 'Неизвестно')}\n\n"
                    f"💡 **Информация:**\n"
                    f"• Мониторинг личных сообщений всегда включен\n"
                    f"• Мониторинг групповых чатов можно включать/отключать\n"
                    f"• Изменения применяются ко всем активным аккаунтам\n\n"
                    f"👇 Выберите действие:"
                )
                
                await callback.message.edit_text(
                    text,
                    parse_mode='Markdown',
                    reply_markup=monitoring_settings_keyboard(status['group_chat_monitoring'])
                )
                
                logger.info(f"Пользователь {user_id} изменил мониторинг групповых чатов на: {new_status}")
            else:
                await callback.answer("❌ Ошибка изменения настройки", show_alert=True)
                logger.error(f"Ошибка изменения настройки мониторинга пользователем {user_id}")
        
        elif action == "reload":
            # Перезагрузка настроек
            success = telethon_manager.reload_monitoring_settings()
            
            if success:
                await callback.answer("✅ Настройки перезагружены", show_alert=True)
                logger.info(f"Пользователь {user_id} перезагрузил настройки мониторинга")
            else:
                await callback.answer("❌ Ошибка перезагрузки настроек", show_alert=True)
                logger.error(f"Ошибка перезагрузки настроек пользователем {user_id}")
        
        elif action == "back":
            # Возврат в админ-панель
            await state.clear()

            from ..keyboards import get_keyboard

            welcome_text = (
                "👑 **Административная панель**\n\n"
                "Вы вернулись в главное меню администратора.\n\n"
                "Доступные функции:\n"
                "• Подключить аккаунт\n"
                "• Мои аккаунты\n"
                "• Управление стоп-словами\n"
                "• ⚙️ Настройки мониторинга\n"
                "• Все подключенные аккаунты\n"
                "• Статистика"
            )

            await callback.message.edit_text(
                welcome_text,
                parse_mode='Markdown',
                reply_markup=None
            )

            # Отправляем новое сообщение с reply клавиатурой
            await callback.message.answer(
                "🔙 Возврат в административную панель",
                reply_markup=get_keyboard(user_id)
            )

            await callback.answer()
            logger.info(f"Пользователь {user_id} вернулся в административную панель из настроек мониторинга")
        
        else:
            await callback.answer("❌ Неизвестное действие", show_alert=True)
    
    @dp.message(lambda m: m.text == "🔙 Назад в админку")
    async def back_to_admin(message: types.Message, state: FSMContext):
        """Возврат в административную панель"""
        user_id = message.from_user.id
        
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return
        
        await state.clear()
        
        from ..keyboards import get_keyboard
        await message.answer(
            "🔙 Возврат в административную панель",
            reply_markup=get_keyboard(user_id)
        )

    logger.info("Обработчики настроек мониторинга зарегистрированы")

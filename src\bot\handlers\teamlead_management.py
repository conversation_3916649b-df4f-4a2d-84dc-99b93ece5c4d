"""
Обработчики для управления тимлидами
"""

import logging
from datetime import datetime
from aiogram import Dispatcher, types
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command

from ..states import TeamleadManagement
from ..keyboards import get_keyboard
from src.config.environment import env_config
from src.config.teamleads_manager import teamleads_manager
from src.config.users_manager import users_manager
from src.utils.logging import setup_logging

logger = setup_logging(__name__, 'teamlead_management.log')


def register_teamlead_handlers(dp: Dispatcher, telethon_manager):
    """Регистрация обработчиков управления тимлидами"""
    
    @dp.message(lambda m: m.text == "👥 Управление тимлидами")
    async def teamlead_management_menu(message: types.Message):
        """Главное меню управления тимлидами"""
        user_id = message.from_user.id
        
        # Только для главных администраторов
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return
        
        # Получаем статистику
        all_teamleads = teamleads_manager.get_all_teamleads()
        all_assignments = teamleads_manager.get_all_assignments()
        
        # Получаем доступные номера телефонов
        connected_accounts = telethon_manager.get_connected_accounts()
        available_phones = [acc['phone'] for acc in connected_accounts]
        
        text = (
            f"👥 Управление тимлидами\n\n"
            f"📊 Статистика:\n"
            f"• Тимлидов: {len(all_teamleads)}\n"
            f"• Назначений: {len(all_assignments)}\n"
            f"• Доступных номеров: {len(available_phones)}\n\n"
            f"Используйте кнопки ниже для управления:"
        )

        # Импортируем клавиатуру для управления тимлидами
        from ..keyboards import teamlead_management_keyboard

        await message.answer(text, reply_markup=teamlead_management_keyboard())
    









    @dp.message(Command("teamlead_stats"))
    async def teamlead_statistics(message: types.Message):
        """Детальная статистика по тимлидам"""
        user_id = message.from_user.id

        # Только для главных администраторов
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        all_teamleads = teamleads_manager.get_all_teamleads()
        all_assignments = teamleads_manager.get_all_assignments()

        # Получаем доступные номера телефонов
        connected_accounts = telethon_manager.get_connected_accounts()
        available_phones = [acc['phone'] for acc in connected_accounts]

        # Подсчитываем статистику
        total_teamleads = len(all_teamleads)
        total_assignments = sum(len(teamleads) for teamleads in all_assignments.values())
        phones_with_assignments = len(all_assignments)
        phones_without_assignments = len(available_phones) - phones_with_assignments

        # Находим тимлидов с наибольшим количеством назначений
        teamlead_stats = []
        for username, data in all_teamleads.items():
            assigned_count = len(data.get('assigned_phones', []))
            teamlead_stats.append((username, assigned_count))

        teamlead_stats.sort(key=lambda x: x[1], reverse=True)

        text = (
            f"📊 Детальная статистика тимлидов\n\n"
            f"👥 Общая информация:\n"
            f"• Всего тимлидов: {total_teamleads}\n"
            f"• Всего назначений: {total_assignments}\n"
            f"• Номеров с назначениями: {phones_with_assignments}\n"
            f"• Номеров без назначений: {phones_without_assignments}\n"
            f"• Всего доступных номеров: {len(available_phones)}\n\n"
        )

        if teamlead_stats:
            text += f"🏆 Топ тимлидов по назначениям:\n"
            for i, (username, count) in enumerate(teamlead_stats[:5], 1):
                text += f"{i}. {username}: {count} номеров\n"
            text += "\n"

        # Показываем номера без назначений
        if phones_without_assignments > 0:
            unassigned_phones = [phone for phone in available_phones if phone not in all_assignments]
            text += f"📱 Номера без назначений ({len(unassigned_phones)}):\n"
            for phone in unassigned_phones[:5]:  # Показываем только первые 5
                text += f"• {phone}\n"
            if len(unassigned_phones) > 5:
                text += f"... и еще {len(unassigned_phones) - 5}\n"

        text += f"\n🕐 Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        await message.answer(text)

    @dp.message(Command("users_stats"))
    async def users_statistics(message: types.Message):
        """Статистика локальной базы пользователей"""
        user_id = message.from_user.id

        # Только для главных администраторов
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        stats = users_manager.get_statistics()

        if not stats:
            await message.answer("❌ Ошибка получения статистики пользователей")
            return

        text = (
            f"👥 Статистика базы пользователей\n\n"
            f"📊 Общая информация:\n"
            f"• Всего пользователей: {stats.get('total_users', 0)}\n"
            f"• С никнеймами: {stats.get('users_with_username', 0)}\n"
            f"• С именами: {stats.get('users_with_first_name', 0)}\n\n"
        )

        # Показываем самых активных пользователей
        most_active = stats.get('most_active_users', [])
        if most_active:
            text += f"🏆 Самые активные пользователи:\n"
            for i, user_data in enumerate(most_active, 1):
                username = user_data.get('username', 'без никнейма')
                first_name = user_data.get('first_name', 'Без имени')
                interactions = user_data.get('interaction_count', 0)
                text += f"{i}. @{username} ({first_name}): {interactions} взаимодействий\n"
            text += "\n"

        text += f"🕐 Обновлено: {stats.get('last_updated', 'Неизвестно')[:19]}\n"
        text += f"📁 Файл: {stats.get('database_file', 'Неизвестно')}"

        await message.answer(text)

    @dp.message(Command("search_user"))
    async def search_user_command(message: types.Message):
        """Поиск пользователя в локальной базе"""
        user_id = message.from_user.id

        # Только для главных администраторов
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        # Извлекаем запрос из команды
        command_parts = message.text.split(' ', 1)
        if len(command_parts) < 2:
            await message.answer(
                "🔍 Поиск пользователя в локальной базе\n\n"
                "Использование: /search_user <запрос>\n"
                "Пример: /search_user тимлид1\n\n"
                "Поиск выполняется по никнейму, имени и фамилии"
            )
            return

        query = command_parts[1].strip()
        found_users = users_manager.search_users(query)

        if not found_users:
            await message.answer(f"❌ Пользователи по запросу '{query}' не найдены")
            return

        text = f"🔍 Результаты поиска по запросу '{query}':\n\n"

        for i, user_data in enumerate(found_users[:10], 1):  # Показываем максимум 10
            username = user_data.get('username', 'без никнейма')
            first_name = user_data.get('first_name', '')
            last_name = user_data.get('last_name', '')
            user_id_found = user_data.get('user_id', 'Неизвестно')
            interactions = user_data.get('interaction_count', 0)

            full_name = f"{first_name} {last_name}".strip() or "Без имени"

            text += f"**{i}. @{username}**\n"
            text += f"🆔 ID: {user_id_found}\n"
            text += f"👤 Имя: {full_name}\n"
            text += f"💬 Взаимодействий: {interactions}\n\n"

        if len(found_users) > 10:
            text += f"... и еще {len(found_users) - 10} пользователей"

        await message.answer(text)



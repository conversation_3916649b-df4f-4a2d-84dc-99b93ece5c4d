#!/usr/bin/env python3
"""
Тест системы персистентности аккаунтов
"""

import asyncio
import json
import sys
from pathlib import Path

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.monitoring.telethon_manager import TelethonManager
from src.config.settings import ACCOUNTS_METADATA_FILE, SESSIONS_DIR

async def test_persistence():
    """Тест системы персистентности"""
    print("🔍 Тест системы персистентности аккаунтов")
    print("=" * 50)
    
    # Создаем менеджер
    manager = TelethonManager()
    
    # Проверяем инициализацию
    print("1. Инициализация TelethonManager...")
    await manager.initialize()
    print("✅ Инициализация завершена")
    
    # Проверяем загрузку метаданных
    print("\n2. Проверка загрузки метаданных...")
    # Читаем метаданные напрямую из файла
    metadata = {}
    if ACCOUNTS_METADATA_FILE.exists():
        import json
        with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
    print(f"✅ Загружено метаданных: {len(metadata)}")

    for user_id, data in metadata.items():
        print(f"   - User {user_id}: {data.get('session_file', 'N/A')} (monitoring: {data.get('monitoring', False)})")
    
    # Проверяем восстановленные аккаунты
    print("\n3. Проверка восстановленных аккаунтов...")
    accounts = manager.get_connected_accounts()
    print(f"✅ Восстановлено аккаунтов: {len(accounts)}")
    
    for acc in accounts:
        status = "🟢 Активен" if acc['monitoring'] else "🔴 Неактивен"
        print(f"   - {acc['phone']} (ID: {acc['user_id']}) - {status}")
    
    # Проверяем файлы сессий
    print("\n4. Проверка файлов сессий...")
    if SESSIONS_DIR.exists():
        session_files = list(SESSIONS_DIR.glob('*.session'))
        print(f"✅ Найдено файлов сессий: {len(session_files)}")
        
        for session_file in session_files:
            user_id = session_file.stem
            file_size = session_file.stat().st_size
            print(f"   - {session_file.name}: {file_size} байт")
    else:
        print("❌ Директория сессий не найдена")
    
    # Проверяем файл метаданных
    print("\n5. Проверка файла метаданных...")
    if ACCOUNTS_METADATA_FILE.exists():
        file_size = ACCOUNTS_METADATA_FILE.stat().st_size
        print(f"✅ Файл метаданных существует: {file_size} байт")
        
        with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
            print("Содержимое файла метаданных:")
            print(content)
    else:
        print("❌ Файл метаданных не найден")
    
    print("\n" + "=" * 50)
    print("🎉 Тест завершен!")

if __name__ == "__main__":
    asyncio.run(test_persistence())

"""
Middleware для автоматического сохранения данных пользователей
"""

import logging
from typing import Callable, Dict, Any, Awaitable
from aiogram import BaseMiddleware
from aiogram.types import Message, CallbackQuery, Update

from ...config.users_manager import users_manager

logger = logging.getLogger(__name__)

class UsersMiddleware(BaseMiddleware):
    """
    Middleware для автоматического сохранения информации о пользователях
    при их взаимодействии с ботом
    """
    
    async def __call__(
        self,
        handler: Callable[[Update, Dict[str, Any]], Awaitable[Any]],
        event: Update,
        data: Dict[str, Any]
    ) -> Any:
        """
        Обработка события и сохранение данных пользователя
        
        Args:
            handler: Следующий обработчик в цепочке
            event: Событие от Telegram
            data: Данные контекста
            
        Returns:
            Результат выполнения обработчика
        """
        try:
            # Извлекаем пользователя из события
            user = None
            
            if hasattr(event, 'message') and event.message:
                user = event.message.from_user
            elif hasattr(event, 'callback_query') and event.callback_query:
                user = event.callback_query.from_user
            elif hasattr(event, 'inline_query') and event.inline_query:
                user = event.inline_query.from_user
            elif hasattr(event, 'chosen_inline_result') and event.chosen_inline_result:
                user = event.chosen_inline_result.from_user
            
            # Сохраняем данные пользователя если он найден
            if user and not user.is_bot:
                await self._save_user_data(user)
            
        except Exception as e:
            logger.error(f"Ошибка в UsersMiddleware: {e}")
            # Продолжаем выполнение даже при ошибке middleware
        
        # Вызываем следующий обработчик
        return await handler(event, data)
    
    async def _save_user_data(self, user) -> None:
        """
        Сохранение данных пользователя в локальную базу
        
        Args:
            user: Объект пользователя от Telegram
        """
        try:
            # Извлекаем данные пользователя
            user_id = user.id
            username = user.username
            first_name = user.first_name
            last_name = user.last_name
            is_bot = user.is_bot
            
            # Сохраняем в локальную базу
            success = users_manager.add_or_update_user(
                user_id=user_id,
                username=username,
                first_name=first_name,
                last_name=last_name,
                is_bot=is_bot
            )
            
            if success:
                logger.debug(f"Данные пользователя {user_id} (@{username}) сохранены")
            else:
                logger.warning(f"Не удалось сохранить данные пользователя {user_id}")
                
        except Exception as e:
            logger.error(f"Ошибка сохранения данных пользователя: {e}")


class MessageUsersMiddleware(BaseMiddleware):
    """
    Специализированный middleware для сообщений
    """
    
    async def __call__(
        self,
        handler: Callable[[Message, Dict[str, Any]], Awaitable[Any]],
        event: Message,
        data: Dict[str, Any]
    ) -> Any:
        """
        Обработка сообщения и сохранение данных пользователя
        """
        try:
            # Сохраняем данные отправителя
            if event.from_user and not event.from_user.is_bot:
                await self._save_user_data(event.from_user)
            
            # Сохраняем данные пересланного пользователя (если есть)
            if event.forward_from and not event.forward_from.is_bot:
                await self._save_user_data(event.forward_from)
            
            # Сохраняем данные пользователей из reply (если есть)
            if event.reply_to_message and event.reply_to_message.from_user:
                if not event.reply_to_message.from_user.is_bot:
                    await self._save_user_data(event.reply_to_message.from_user)
            
        except Exception as e:
            logger.error(f"Ошибка в MessageUsersMiddleware: {e}")
        
        return await handler(event, data)
    
    async def _save_user_data(self, user) -> None:
        """Сохранение данных пользователя"""
        try:
            success = users_manager.add_or_update_user(
                user_id=user.id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name,
                is_bot=user.is_bot
            )
            
            if success:
                logger.debug(f"Пользователь {user.id} (@{user.username}) сохранен через MessageMiddleware")
                
        except Exception as e:
            logger.error(f"Ошибка сохранения пользователя в MessageMiddleware: {e}")


class CallbackUsersMiddleware(BaseMiddleware):
    """
    Специализированный middleware для callback-запросов
    """
    
    async def __call__(
        self,
        handler: Callable[[CallbackQuery, Dict[str, Any]], Awaitable[Any]],
        event: CallbackQuery,
        data: Dict[str, Any]
    ) -> Any:
        """
        Обработка callback-запроса и сохранение данных пользователя
        """
        try:
            # Сохраняем данные пользователя
            if event.from_user and not event.from_user.is_bot:
                success = users_manager.add_or_update_user(
                    user_id=event.from_user.id,
                    username=event.from_user.username,
                    first_name=event.from_user.first_name,
                    last_name=event.from_user.last_name,
                    is_bot=event.from_user.is_bot
                )
                
                if success:
                    logger.debug(f"Пользователь {event.from_user.id} сохранен через CallbackMiddleware")
            
        except Exception as e:
            logger.error(f"Ошибка в CallbackUsersMiddleware: {e}")
        
        return await handler(event, data)

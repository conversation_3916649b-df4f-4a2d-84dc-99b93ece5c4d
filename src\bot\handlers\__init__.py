"""
Обработчики команд бота
"""

from aiogram import Dispatcher
from .basic import register_basic_handlers
from .account import register_account_handlers
from .admin import register_admin_handlers

def register_handlers(dp: Dispatcher, telethon_manager, alert_manager, response_tracker=None):
    """Регистрация всех обработчиков"""
    # Сначала регистрируем специфические обработчики
    register_account_handlers(dp, telethon_manager, alert_manager)
    register_admin_handlers(dp, telethon_manager)

    # Регистрация обработчиков управления стоп-словами
    try:
        from .stopwords_management import register_stopwords_handlers, get_stopwords_manager
        register_stopwords_handlers(dp)

        # Передаем тот же экземпляр stopwords_manager в telethon_manager
        telethon_manager.stopwords_manager = get_stopwords_manager()
    except ImportError as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Не удалось импортировать обработчики стоп-слов: {e}")

    # Регистрация обработчиков настроек мониторинга
    try:
        from .monitoring_settings import register_monitoring_settings_handlers
        register_monitoring_settings_handlers(dp, telethon_manager)
    except ImportError as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Не удалось импортировать обработчики настроек мониторинга: {e}")

    # Регистрация обработчиков управления тимлидами
    try:
        from .teamlead_management import register_teamlead_handlers
        from .teamlead_removal import register_teamlead_removal_handlers
        from .teamlead_callbacks import register_teamlead_callback_handlers
        from .teamlead_fsm import register_teamlead_fsm_handlers
        register_teamlead_handlers(dp, telethon_manager)
        register_teamlead_removal_handlers(dp)
        register_teamlead_callback_handlers(dp, telethon_manager)
        register_teamlead_fsm_handlers(dp)
    except ImportError as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Не удалось импортировать обработчики управления тимлидами: {e}")

    # Регистрация обработчиков управления временем ответа
    try:
        from .response_time_management import register_response_time_handlers
        register_response_time_handlers(dp, telethon_manager, response_tracker)
    except ImportError as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Не удалось импортировать обработчики времени ответа: {e}")

    # Регистрация обработчиков управления ролями
    try:
        from .roles_management import register_roles_handlers
        register_roles_handlers(dp)
    except ImportError as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Не удалось импортировать обработчики управления ролями: {e}")

    # Регистрируем базовые обработчики в конце (включая catch-all обработчик)
    register_basic_handlers(dp)

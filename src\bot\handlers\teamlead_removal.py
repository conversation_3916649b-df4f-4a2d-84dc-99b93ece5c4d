"""
Обработчики для удаления тимлидов
"""

import logging
from datetime import datetime
from aiogram import Dispatcher, types
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command

from ..states import TeamleadManagement
from ..keyboards import get_keyboard
from src.config.environment import env_config
from src.config.teamleads_manager import teamleads_manager
from src.utils.logging import setup_logging

logger = setup_logging(__name__, 'teamlead_removal.log')


def register_teamlead_removal_handlers(dp: Dispatcher):
    """Регистрация обработчиков удаления тимлидов"""
    
    @dp.message(Command("remove_teamlead"))
    async def remove_teamlead_start(message: types.Message, state: FSMContext):
        """Начало процесса удаления тимлида"""
        user_id = message.from_user.id
        
        # Только для главных администраторов
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return
        
        all_teamleads = teamleads_manager.get_all_teamleads()
        
        if not all_teamleads:
            await message.answer("❌ Нет тимлидов для удаления.")
            return
        
        # Формируем список тимлидов
        teamleads_list = []
        for i, (username, data) in enumerate(all_teamleads.items(), 1):
            assigned_count = len(data.get('assigned_phones', []))
            teamleads_list.append(f"{i}. {username} (назначений: {assigned_count})")
        
        text = (
            f"⚠️ **Удаление тимлида**\n\n"
            f"Выберите тимлида для удаления:\n\n"
            f"{chr(10).join(teamleads_list)}\n\n"
            f"⚠️ **Внимание:** При удалении тимлида все его назначения будут отменены!\n\n"
            f"Введите номер тимлида из списка:"
        )
        
        await message.answer(text, parse_mode='Markdown')
        await state.update_data(teamleads_list=list(all_teamleads.keys()))
        await state.set_state(TeamleadManagement.confirming_teamlead_deletion)
    
    @dp.message(TeamleadManagement.confirming_teamlead_deletion)
    async def confirm_teamlead_deletion(message: types.Message, state: FSMContext):
        """Подтверждение удаления тимлида"""
        user_id = message.from_user.id
        text = message.text.strip()
        
        data = await state.get_data()
        
        # Проверяем, это выбор тимлида или подтверждение удаления
        if 'selected_username' not in data:
            # Это выбор тимлида
            try:
                selection = int(text)
                teamleads_list = data['teamleads_list']
                
                if selection < 1 or selection > len(teamleads_list):
                    await message.answer(f"❌ Выберите номер от 1 до {len(teamleads_list)}:")
                    return
                
                selected_username = teamleads_list[selection - 1]
                
                # Получаем информацию о тимлиде
                teamlead_data = teamleads_manager.get_all_teamleads()[selected_username]
                assigned_phones = teamlead_data.get('assigned_phones', [])
                
                confirmation_text = (
                    f"⚠️ **Подтверждение удаления**\n\n"
                    f"👤 Тимлид: **{selected_username}**\n"
                    f"🆔 ID: {teamlead_data.get('user_id', 'Неизвестно')}\n"
                    f"📱 Назначенных номеров: {len(assigned_phones)}\n"
                )
                
                if assigned_phones:
                    confirmation_text += f"📞 Номера: {', '.join(assigned_phones)}\n"
                
                confirmation_text += (
                    f"\n❗ **Все назначения этого тимлида будут отменены!**\n\n"
                    f"Для подтверждения введите: **УДАЛИТЬ {selected_username.upper()}**\n"
                    f"Для отмены введите любой другой текст."
                )
                
                await message.answer(confirmation_text, parse_mode='Markdown')
                await state.update_data(selected_username=selected_username)
                
            except ValueError:
                await message.answer("❌ Введите номер тимлида:")
        else:
            # Это подтверждение удаления
            selected_username = data['selected_username']
            expected_confirmation = f"УДАЛИТЬ {selected_username.upper()}"
            
            if text == expected_confirmation:
                # Удаляем тимлида
                success = teamleads_manager.remove_teamlead(selected_username, user_id)
                
                if success:
                    await message.answer(
                        f"✅ **Тимлид удален!**\n\n"
                        f"👤 Тимлид: {selected_username}\n"
                        f"📅 Удален: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                        f"🔄 Все назначения отменены",
                        parse_mode='Markdown',
                        reply_markup=get_keyboard(user_id)
                    )
                    logger.info(f"Администратор {user_id} удалил тимлида {selected_username}")
                else:
                    await message.answer(
                        "❌ Ошибка при удалении тимлида.",
                        reply_markup=get_keyboard(user_id)
                    )
            else:
                await message.answer(
                    "❌ Удаление отменено.",
                    reply_markup=get_keyboard(user_id)
                )
            
            await state.clear()

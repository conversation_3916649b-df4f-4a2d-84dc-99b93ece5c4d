"""
Тесты обработчиков account.py

Покрывает подключение аккаунтов и FSM состояния ConnectAccount
с мокированием TelethonManager и AlertManager.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from aiogram import Dispatcher
from aiogram.types import Message, User, Chat, CallbackQuery
from aiogram.fsm.context import FSMContext

from src.bot.handlers.account import register_account_handlers
from src.bot.states import ConnectAccount
from src.monitoring.telethon_manager import TelethonManager
from src.bot.alerts import AlertManager
from tests.fixtures.test_constants import (
    TEST_REGULAR_USER_ID, VALID_PHONE_NUMBERS, INVALID_PHONE_NUMBERS,
    VALID_CONFIRMATION_CODES, INVALID_CONFIRMATION_CODES
)
from tests.utils.mock_factories import (
    MockFactory, TelethonMockFactory, AlertMockFactory,
    create_regular_user, create_admin_user
)


class TestAccountHandlersRegistration:
    """Тесты регистрации обработчиков аккаунтов"""
    
    def test_register_account_handlers(self):
        """Тест регистрации обработчиков аккаунтов"""
        mock_dp = Mock(spec=Dispatcher)
        mock_telethon_manager = Mock(spec=TelethonManager)
        mock_alert_manager = Mock(spec=AlertManager)
        
        # Мокируем декораторы
        mock_dp.message = Mock(return_value=lambda func: func)
        
        register_account_handlers(mock_dp, mock_telethon_manager, mock_alert_manager)
        
        # Проверяем что декораторы были вызваны
        assert mock_dp.message.call_count >= 3  # Минимум для основных обработчиков


class TestConnectAccountHandler:
    """Тесты обработчика 'Подключить аккаунт'"""
    
    @pytest.mark.asyncio
    async def test_connect_account_initial_request(self):
        """Тест начального запроса на подключение аккаунта"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "Подключить аккаунт")
        state = MockFactory.create_fsm_context()
        
        # Создаем обработчик напрямую
        async def mock_connect_account_handler(message: Message, state: FSMContext):
            # Импортируем клавиатуру для отмены ввода номера
            from src.bot.keyboards import cancel_phone_input_keyboard

            await message.answer(
                "📱 Пожалуйста, введите номер телефона в международном формате:\n"
                "Пример: +**********7",
                reply_markup=cancel_phone_input_keyboard()
            )
            await state.set_state(ConnectAccount.waiting_for_phone)
        
        await mock_connect_account_handler(message, state)
        
        # Проверяем что ответ был отправлен
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]

        assert "номер телефона" in response_text
        assert "международном формате" in response_text
        assert "+**********7" in response_text

        # Проверяем что клавиатура была добавлена
        call_kwargs = call_args[1]
        assert 'reply_markup' in call_kwargs
        assert call_kwargs['reply_markup'] is not None

        # Проверяем что состояние было установлено
        state.set_state.assert_called_once_with(ConnectAccount.waiting_for_phone)
    
    @pytest.mark.asyncio
    async def test_connect_account_multiple_requests(self):
        """Тест множественных запросов на подключение"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "Подключить аккаунт")
        state = MockFactory.create_fsm_context()
        
        # Симулируем что пользователь уже в процессе подключения
        state.get_state.return_value = ConnectAccount.waiting_for_phone
        
        async def mock_connect_account_handler(message: Message, state: FSMContext):
            current_state = await state.get_state()
            if current_state:
                await message.answer("Вы уже в процессе подключения аккаунта.")
            else:
                await message.answer("Введите номер телефона")
                await state.set_state(ConnectAccount.waiting_for_phone)
        
        await mock_connect_account_handler(message, state)
        
        # Проверяем что пользователь получил соответствующее сообщение
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]
        
        assert "уже в процессе" in response_text

    @pytest.mark.asyncio
    async def test_cancel_phone_input(self):
        """Тест отмены ввода номера телефона"""
        user_data = create_regular_user()
        callback = MockFactory.create_callback_query(user_data, "cancel_phone_input")
        state = MockFactory.create_fsm_context()

        # Устанавливаем состояние ожидания номера телефона
        state.get_state.return_value = ConnectAccount.waiting_for_phone

        # Создаем обработчик отмены
        async def mock_cancel_phone_input_handler(callback: CallbackQuery, state: FSMContext):
            await callback.answer()

            try:
                user_id = callback.from_user.id

                # Очищаем состояние FSM
                await state.clear()

                # Возвращаем пользователя в главное меню
                await callback.message.edit_text(
                    "❌ **Ввод номера телефона отменен**\n\n"
                    "Процесс подключения аккаунта был отменен.\n"
                    "Вы можете попробовать подключить аккаунт снова позже.",
                    parse_mode='Markdown'
                )

                # Отправляем новое сообщение с главным меню
                from src.bot.keyboards import get_keyboard
                await callback.message.answer(
                    "🏠 Вы вернулись в главное меню",
                    reply_markup=get_keyboard(user_id)
                )

            except Exception as e:
                await callback.message.edit_text(
                    "❌ Ошибка при отмене ввода номера телефона"
                )

        await mock_cancel_phone_input_handler(callback, state)

        # Проверяем что callback был обработан
        callback.answer.assert_called_once()

        # Проверяем что состояние было очищено
        state.clear.assert_called_once()

        # Проверяем что сообщение было отредактировано
        callback.message.edit_text.assert_called_once()
        edit_call_args = callback.message.edit_text.call_args
        edit_text = edit_call_args[0][0]

        assert "отменен" in edit_text
        assert "Процесс подключения аккаунта был отменен" in edit_text

        # Проверяем что было отправлено новое сообщение с главным меню
        callback.message.answer.assert_called_once()
        answer_call_args = callback.message.answer.call_args
        answer_text = answer_call_args[0][0]

        assert "главное меню" in answer_text


class TestPhoneInputHandler:
    """Тесты обработчика ввода номера телефона"""
    
    @pytest.mark.asyncio
    async def test_phone_input_valid_phone(self):
        """Тест ввода валидного номера телефона"""
        user_data = create_regular_user()
        phone = "+**********7"
        message = MockFactory.create_message(user_data, phone)
        state = MockFactory.create_fsm_context()
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_alert_manager = AlertMockFactory.create_alert_manager()
        
        # Создаем обработчик
        async def mock_phone_handler(message: Message, state: FSMContext):
            phone = message.text.strip()
            user_id = message.from_user.id
            
            try:
                success = await mock_telethon_manager.start_auth(user_id, phone)
                if success:
                    await state.update_data(phone=phone)
                    await message.answer(
                        f"📱 Код подтверждения отправлен на номер {phone}\n"
                        f"Введите полученный код:"
                    )
                    await state.set_state(ConnectAccount.waiting_for_code)
                else:
                    await message.answer("❌ Ошибка отправки кода. Попробуйте еще раз.")
            except Exception as e:
                await message.answer(f"❌ Ошибка: {str(e)}")
        
        await mock_phone_handler(message, state)
        
        # Проверяем что TelethonManager был вызван
        mock_telethon_manager.start_auth.assert_called_once_with(user_data.user_id, phone)
        
        # Проверяем что ответ был отправлен
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]
        
        assert "Код подтверждения отправлен" in response_text
        assert phone in response_text
        
        # Проверяем что данные были сохранены
        state.update_data.assert_called_once_with(phone=phone)
        
        # Проверяем что состояние было изменено
        state.set_state.assert_called_once_with(ConnectAccount.waiting_for_code)
    
    @pytest.mark.asyncio
    async def test_phone_input_telethon_error(self):
        """Тест ошибки TelethonManager при вводе телефона"""
        user_data = create_regular_user()
        phone = "+**********7"
        message = MockFactory.create_message(user_data, phone)
        state = MockFactory.create_fsm_context()
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_telethon_manager.start_auth.side_effect = Exception("Network error")
        
        async def mock_phone_handler(message: Message, state: FSMContext):
            phone = message.text.strip()
            user_id = message.from_user.id
            
            try:
                await mock_telethon_manager.start_auth(user_id, phone)
            except Exception as e:
                await message.answer(f"❌ Ошибка: {str(e)}")
        
        await mock_phone_handler(message, state)
        
        # Проверяем что ошибка была обработана
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]
        
        assert "❌ Ошибка" in response_text
        assert "Network error" in response_text
        
        # Состояние не должно было измениться
        state.set_state.assert_not_called()
    
    @pytest.mark.parametrize("phone", VALID_PHONE_NUMBERS)
    @pytest.mark.asyncio
    async def test_phone_input_parametrized_valid(self, phone):
        """Параметризованный тест валидных номеров телефонов"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, phone)
        state = MockFactory.create_fsm_context()
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        
        async def mock_phone_handler(message: Message, state: FSMContext):
            phone_input = message.text.strip()
            user_id = message.from_user.id
            
            await mock_telethon_manager.start_auth(user_id, phone_input)
            await state.update_data(phone=phone_input)
            await message.answer("Code sent")
        
        await mock_phone_handler(message, state)
        
        # Проверяем что номер был обработан
        mock_telethon_manager.start_auth.assert_called_once_with(user_data.user_id, phone)
        state.update_data.assert_called_once_with(phone=phone)
    
    @pytest.mark.parametrize("phone", INVALID_PHONE_NUMBERS)
    @pytest.mark.asyncio
    async def test_phone_input_parametrized_invalid(self, phone):
        """Параметризованный тест невалидных номеров телефонов"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, phone)
        state = MockFactory.create_fsm_context()
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        
        # Для невалидных номеров TelethonManager может выбросить исключение
        mock_telethon_manager.start_auth.side_effect = Exception("Invalid phone")
        
        async def mock_phone_handler(message: Message, state: FSMContext):
            phone_input = message.text.strip()
            user_id = message.from_user.id
            
            try:
                await mock_telethon_manager.start_auth(user_id, phone_input)
                await message.answer("Success")
            except Exception:
                await message.answer("Invalid phone format")
        
        await mock_phone_handler(message, state)
        
        # Проверяем что ошибка была обработана
        message.answer.assert_called_once_with("Invalid phone format")


class TestCodeInputHandler:
    """Тесты обработчика ввода кода подтверждения"""
    
    @pytest.mark.asyncio
    async def test_code_input_valid_code(self):
        """Тест ввода валидного кода подтверждения"""
        user_data = create_regular_user()
        code = "12345"
        phone = "+**********7"
        message = MockFactory.create_message(user_data, code)
        state = MockFactory.create_fsm_context({"phone": phone})
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_alert_manager = AlertMockFactory.create_alert_manager()
        
        async def mock_code_handler(message: Message, state: FSMContext):
            code_input = message.text.strip()
            user_id = message.from_user.id
            data = await state.get_data()
            phone = data.get('phone')
            
            try:
                # Создание callback для алертов
                async def alert_callback(alert_text):
                    await mock_alert_manager.send_alert_to_all(user_id, alert_text)
                
                await mock_telethon_manager.complete_auth(user_id, phone, code_input, alert_callback)
                
                success_message = (
                    f"✅ Аккаунт {phone} успешно подключён!\n"
                    f"🔍 Мониторинг исходящих сообщений запущен\n"
                    f"⚠️ При обнаружении стоп-слов вы получите уведомление"
                )
                await message.answer(success_message)
                await state.clear()
                
            except Exception as e:
                await message.answer(f"❌ Ошибка: {str(e)}")
        
        await mock_code_handler(message, state)
        
        # Проверяем что complete_auth был вызван
        mock_telethon_manager.complete_auth.assert_called_once()
        call_args = mock_telethon_manager.complete_auth.call_args[0]
        assert call_args[0] == user_data.user_id
        assert call_args[1] == phone
        assert call_args[2] == code
        
        # Проверяем что успешное сообщение было отправлено
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]
        
        assert "✅ Аккаунт" in response_text
        assert "успешно подключён" in response_text
        assert phone in response_text
        
        # Проверяем что состояние было очищено
        state.clear.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_code_input_invalid_code(self):
        """Тест ввода невалидного кода подтверждения"""
        user_data = create_regular_user()
        code = "00000"
        phone = "+**********7"
        message = MockFactory.create_message(user_data, code)
        state = MockFactory.create_fsm_context({"phone": phone})
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_telethon_manager.complete_auth.side_effect = Exception("Неверный код подтверждения")
        
        async def mock_code_handler(message: Message, state: FSMContext):
            code_input = message.text.strip()
            user_id = message.from_user.id
            data = await state.get_data()
            phone = data.get('phone')
            
            try:
                await mock_telethon_manager.complete_auth(user_id, phone, code_input, None)
                await message.answer("Success")
            except Exception as e:
                await message.answer(f"❌ Ошибка: {str(e)}")
        
        await mock_code_handler(message, state)
        
        # Проверяем что ошибка была обработана
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]
        
        assert "❌ Ошибка" in response_text
        assert "Неверный код" in response_text
    
    @pytest.mark.asyncio
    async def test_code_input_missing_phone_data(self):
        """Тест ввода кода без сохраненного номера телефона"""
        user_data = create_regular_user()
        code = "12345"
        message = MockFactory.create_message(user_data, code)
        state = MockFactory.create_fsm_context({})  # Пустые данные
        
        async def mock_code_handler(message: Message, state: FSMContext):
            data = await state.get_data()
            phone = data.get('phone')
            
            if not phone:
                await message.answer("❌ Ошибка: номер телефона не найден. Начните процесс заново.")
                await state.clear()
            else:
                await message.answer("Processing code")
        
        await mock_code_handler(message, state)
        
        # Проверяем что была отправлена ошибка
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]
        
        assert "номер телефона не найден" in response_text
        assert "начните процесс заново" in response_text.lower()
        
        # Состояние должно быть очищено
        state.clear.assert_called_once()
    
    @pytest.mark.parametrize("code", VALID_CONFIRMATION_CODES)
    @pytest.mark.asyncio
    async def test_code_input_parametrized_valid(self, code):
        """Параметризованный тест валидных кодов подтверждения"""
        user_data = create_regular_user()
        phone = "+**********7"
        message = MockFactory.create_message(user_data, code)
        state = MockFactory.create_fsm_context({"phone": phone})
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        
        async def mock_code_handler(message: Message, state: FSMContext):
            code_input = message.text.strip()
            user_id = message.from_user.id
            data = await state.get_data()
            phone = data.get('phone')
            
            await mock_telethon_manager.complete_auth(user_id, phone, code_input, None)
            await message.answer("Success")
        
        await mock_code_handler(message, state)
        
        # Проверяем что код был обработан
        mock_telethon_manager.complete_auth.assert_called_once()
        call_args = mock_telethon_manager.complete_auth.call_args[0]
        assert call_args[2] == code
    
    @pytest.mark.parametrize("code", INVALID_CONFIRMATION_CODES)
    @pytest.mark.asyncio
    async def test_code_input_parametrized_invalid(self, code):
        """Параметризованный тест невалидных кодов подтверждения"""
        user_data = create_regular_user()
        phone = "+**********7"
        message = MockFactory.create_message(user_data, code)
        state = MockFactory.create_fsm_context({"phone": phone})
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_telethon_manager.complete_auth.side_effect = Exception("Invalid code")
        
        async def mock_code_handler(message: Message, state: FSMContext):
            code_input = message.text.strip()
            user_id = message.from_user.id
            data = await state.get_data()
            phone = data.get('phone')
            
            try:
                await mock_telethon_manager.complete_auth(user_id, phone, code_input, None)
                await message.answer("Success")
            except Exception:
                await message.answer("Invalid code")
        
        await mock_code_handler(message, state)
        
        # Проверяем что ошибка была обработана
        message.answer.assert_called_once_with("Invalid code")


class TestMyAccountsHandler:
    """Тесты обработчика 'Мои аккаунты'"""

    @pytest.mark.asyncio
    async def test_my_accounts_no_accounts(self):
        """Тест просмотра аккаунтов когда их нет"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "Мои аккаунты")

        mock_telethon_manager = TelethonMockFactory.create_telethon_manager([])

        async def mock_my_accounts_handler(message: Message):
            user_id = message.from_user.id
            accounts = mock_telethon_manager.get_connected_accounts()

            # Используем реальную функциональность "Мои аккаунты"
            if not accounts:
                text = "📱 **Управление аккаунтами**\n\n❌ В системе нет подключенных аккаунтов"
            else:
                text = f"📱 **Управление аккаунтами**\n\n📊 Всего аккаунтов: {len(accounts)}\n\n"
                text += "Выберите аккаунт для управления:"

            await message.answer(text)

        await mock_my_accounts_handler(message)

        # Проверяем что был вызван get_connected_accounts
        mock_telethon_manager.get_connected_accounts.assert_called_once()

        # Проверяем ответ
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]

        assert "В системе нет подключенных аккаунтов" in response_text

    @pytest.mark.asyncio
    async def test_my_accounts_with_accounts(self):
        """Тест просмотра аккаунтов когда они есть"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "Мои аккаунты")

        # Создаем тестовые аккаунты
        test_accounts = [
            {
                'user_id': user_data.user_id,
                'phone': '+***********',
                'monitoring': True
            },
            {
                'user_id': user_data.user_id,
                'phone': '+***********',
                'monitoring': False
            },
            {
                'user_id': 999999,  # Чужой аккаунт
                'phone': '+***********',
                'monitoring': True
            }
        ]

        mock_telethon_manager = TelethonMockFactory.create_telethon_manager(test_accounts)

        async def mock_my_accounts_handler(message: Message):
            user_id = message.from_user.id
            accounts = mock_telethon_manager.get_connected_accounts()

            # Используем реальную функциональность "Мои аккаунты"
            if not accounts:
                text = "📱 **Управление аккаунтами**\n\n❌ В системе нет подключенных аккаунтов"
            else:
                text = f"📱 **Управление аккаунтами**\n\n📊 Всего аккаунтов: {len(accounts)}\n\n"
                text += "Выберите аккаунт для управления:"

            await message.answer(text)

        await mock_my_accounts_handler(message)

        # Проверяем ответ
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]

        assert "Управление аккаунтами" in response_text
        assert "Всего аккаунтов: 3" in response_text
        assert "Выберите аккаунт для управления" in response_text

    @pytest.mark.asyncio
    async def test_my_accounts_only_active(self):
        """Тест просмотра только активных аккаунтов"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "Мои аккаунты")

        test_accounts = [
            {
                'user_id': user_data.user_id,
                'phone': '+***********',
                'monitoring': True
            },
            {
                'user_id': user_data.user_id,
                'phone': '+***********',
                'monitoring': True
            }
        ]

        mock_telethon_manager = TelethonMockFactory.create_telethon_manager(test_accounts)

        async def mock_my_accounts_handler(message: Message):
            user_id = message.from_user.id
            accounts = mock_telethon_manager.get_connected_accounts()

            # Используем реальную функциональность "Мои аккаунты"
            if not accounts:
                text = "📱 **Управление аккаунтами**\n\n❌ В системе нет подключенных аккаунтов"
            else:
                text = f"📱 **Управление аккаунтами**\n\n📊 Всего аккаунтов: {len(accounts)}\n\n"
                text += "Выберите аккаунт для управления:"

            await message.answer(text)

        await mock_my_accounts_handler(message)

        # Проверяем что отображается правильное количество аккаунтов
        call_args = message.answer.call_args
        response_text = call_args[0][0]

        assert "Управление аккаунтами" in response_text
        assert "Всего аккаунтов: 2" in response_text

class TestAccountHandlersEdgeCases:
    """Тесты граничных случаев для обработчиков аккаунтов"""

    @pytest.mark.asyncio
    async def test_fsm_state_corruption(self):
        """Тест обработки поврежденного состояния FSM"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "12345")

        # Создаем поврежденное состояние
        state = MockFactory.create_fsm_context({"corrupted": "data"})
        state.get_data.return_value = {"invalid": "structure"}

        async def mock_code_handler(message: Message, state: FSMContext):
            try:
                data = await state.get_data()
                phone = data.get('phone')

                if not phone:
                    await message.answer("❌ Данные сессии повреждены. Начните заново.")
                    await state.clear()
                else:
                    await message.answer("Processing")
            except Exception:
                await message.answer("❌ Ошибка состояния")
                await state.clear()

        await mock_code_handler(message, state)

        # Проверяем что ошибка была обработана
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]

        assert "повреждены" in response_text or "Ошибка состояния" in response_text

    @pytest.mark.asyncio
    async def test_telethon_manager_unavailable(self):
        """Тест недоступности TelethonManager"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "+**********7")
        state = MockFactory.create_fsm_context()

        # TelethonManager недоступен
        mock_telethon_manager = None

        async def mock_phone_handler(message: Message, state: FSMContext):
            if mock_telethon_manager is None:
                await message.answer("❌ Сервис временно недоступен")
                return

            # Обычная обработка
            await message.answer("Processing")

        await mock_phone_handler(message, state)

        # Проверяем что была отправлена ошибка
        message.answer.assert_called_once_with("❌ Сервис временно недоступен")

    @pytest.mark.asyncio
    async def test_concurrent_account_connections(self):
        """Тест одновременного подключения аккаунтов"""
        import asyncio

        # Создаем несколько пользователей
        users_data = [create_regular_user() for _ in range(3)]
        for i, user_data in enumerate(users_data):
            user_data.user_id = TEST_REGULAR_USER_ID + i

        messages = [
            MockFactory.create_message(user_data, f"+**********{i}")
            for i, user_data in enumerate(users_data)
        ]
        states = [MockFactory.create_fsm_context() for _ in range(3)]

        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()

        async def mock_phone_handler(message: Message, state: FSMContext):
            phone = message.text.strip()
            user_id = message.from_user.id

            # Симулируем небольшую задержку
            await asyncio.sleep(0.01)

            await mock_telethon_manager.start_auth(user_id, phone)
            await message.answer("Code sent")

        # Запускаем обработчики одновременно
        tasks = [
            mock_phone_handler(msg, state)
            for msg, state in zip(messages, states)
        ]
        await asyncio.gather(*tasks)

        # Проверяем что все обработчики отработали
        assert mock_telethon_manager.start_auth.call_count == 3
        for message in messages:
            message.answer.assert_called_once_with("Code sent")

    @pytest.mark.asyncio
    async def test_alert_callback_error(self):
        """Тест ошибки в callback алертов"""
        user_data = create_regular_user()
        code = "12345"
        phone = "+**********7"
        message = MockFactory.create_message(user_data, code)
        state = MockFactory.create_fsm_context({"phone": phone})

        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_alert_manager = AlertMockFactory.create_alert_manager()
        mock_alert_manager.send_alert_to_all.side_effect = Exception("Alert error")

        async def mock_code_handler(message: Message, state: FSMContext):
            code_input = message.text.strip()
            user_id = message.from_user.id
            data = await state.get_data()
            phone = data.get('phone')

            try:
                async def alert_callback(alert_text):
                    try:
                        await mock_alert_manager.send_alert_to_all(user_id, alert_text)
                    except Exception:
                        # Ошибки алертов не должны прерывать основной процесс
                        pass

                await mock_telethon_manager.complete_auth(user_id, phone, code_input, alert_callback)
                await message.answer("✅ Аккаунт подключен")
                await state.clear()

            except Exception as e:
                await message.answer(f"❌ Ошибка: {str(e)}")

        await mock_code_handler(message, state)

        # Проверяем что подключение прошло успешно несмотря на ошибку алертов
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]

        assert "✅ Аккаунт подключен" in response_text
        state.clear.assert_called_once()

    @pytest.mark.asyncio
    async def test_state_cleanup_on_error(self):
        """Тест очистки состояния при ошибках"""
        user_data = create_regular_user()
        phone = "+**********7"
        message = MockFactory.create_message(user_data, phone)
        state = MockFactory.create_fsm_context()

        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_telethon_manager.start_auth.side_effect = Exception("Critical error")

        async def mock_phone_handler(message: Message, state: FSMContext):
            try:
                phone = message.text.strip()
                user_id = message.from_user.id

                await mock_telethon_manager.start_auth(user_id, phone)
                await state.update_data(phone=phone)
                await message.answer("Success")

            except Exception as e:
                await message.answer(f"❌ Ошибка: {str(e)}")
                # Очищаем состояние при критической ошибке
                await state.clear()

        await mock_phone_handler(message, state)

        # Проверяем что состояние было очищено
        state.clear.assert_called_once()

        # Проверяем что данные не были сохранены
        state.update_data.assert_not_called()

    @pytest.mark.asyncio
    async def test_message_text_sanitization(self):
        """Тест санитизации текста сообщений"""
        user_data = create_regular_user()

        # Тестируем различные потенциально опасные входные данные
        malicious_inputs = [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "+**********\x00\x01\x02"  # С бинарными данными
        ]

        for malicious_input in malicious_inputs:
            message = MockFactory.create_message(user_data, malicious_input)
            state = MockFactory.create_fsm_context()

            mock_telethon_manager = TelethonMockFactory.create_telethon_manager()

            async def mock_phone_handler(message: Message, state: FSMContext):
                # Базовая санитизация
                phone = message.text.strip() if message.text else ""

                # Проверка на подозрительные символы
                if any(char in phone for char in ['<', '>', ';', '--', '\x00']):
                    await message.answer("❌ Недопустимый формат номера")
                    return

                await message.answer("Processing")

            await mock_phone_handler(message, state)

            # Проверяем что подозрительный ввод был отклонен
            message.answer.assert_called_once()
            call_args = message.answer.call_args
            response_text = call_args[0][0]

            if any(char in malicious_input for char in ['<', '>', ';', '--', '\x00']):
                assert "Недопустимый формат" in response_text
            else:
                assert "Processing" in response_text

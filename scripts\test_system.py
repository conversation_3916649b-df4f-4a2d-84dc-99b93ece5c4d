#!/usr/bin/env python3
"""
Тестирование основных компонентов системы
"""

import os
import sys
import asyncio
from pathlib import Path

# Добавляем корневую папку проекта в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.environment import env_config
from src.config.settings import SESSIONS_DIR, LOGS_DIR, STOPWORDS_FILE
from src.monitoring.telethon_manager import TelethonManager
from src.utils.logging import setup_logging

logger = setup_logging(__name__)

def test_environment():
    """Тест переменных окружения"""
    print("🧪 Тестирование переменных окружения...")
    
    is_valid, missing_vars = env_config.validate()
    
    if is_valid:
        print(f"✅ BOT_TOKEN: {'*' * 10}...")
        print(f"✅ API_ID: {env_config.API_ID}")
        print(f"✅ API_HASH: {'*' * 10}...")
        print(f"✅ ADMIN_CHAT_IDS: {len(env_config.ADMIN_CHAT_IDS)} админов")
    else:
        print("❌ Отсутствуют переменные:")
        for var in missing_vars:
            print(f"   - {var}")
    
    print()

def test_directories():
    """Тест создания директорий"""
    print("🧪 Тестирование директорий...")
    
    directories = [SESSIONS_DIR, LOGS_DIR]
    
    for directory in directories:
        if directory.exists():
            print(f"✅ {directory}: существует")
        else:
            print(f"❌ {directory}: не существует")
    
    print()

def test_stopwords():
    """Тест загрузки стоп-слов"""
    print("🧪 Тестирование стоп-слов...")
    
    try:
        manager = TelethonManager()
        stopwords = manager.stopwords_manager.stopwords
        
        print(f"📝 Загружено стоп-слов: {len(stopwords)}")
        
        if stopwords:
            print("📋 Первые 5 стоп-слов:")
            for i, word in enumerate(stopwords[:5]):
                print(f"   {i+1}. {word}")
        
        # Тест проверки стоп-слов
        test_texts = [
            "Привет, как дела?",
            "Хочешь заработать деньги?",
            "Купи эту акцию сейчас!",
            "Обычное сообщение без проблем"
        ]
        
        print("\n🔍 Тестирование проверки текстов:")
        for text in test_texts:
            has_stopwords, found_words = manager.stopwords_manager.contains_stopwords(text)
            status = "❌ СТОП-СЛОВА" if has_stopwords else "✅ ЧИСТО"
            print(f"   {status}: '{text}'")
            if found_words:
                print(f"      Найдено: {', '.join(found_words)}")
    
    except Exception as e:
        print(f"❌ Ошибка тестирования стоп-слов: {e}")
    
    print()

def test_config():
    """Тест конфигурации"""
    print("🧪 Тестирование конфигурации...")
    
    # Проверка файла стоп-слов
    if STOPWORDS_FILE.exists():
        print(f"✅ Файл стоп-слов: {STOPWORDS_FILE}")
        
        with open(STOPWORDS_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        comment_lines = len([line for line in lines if line.strip().startswith('#')])
        empty_lines = len([line for line in lines if not line.strip()])
        word_lines = total_lines - comment_lines - empty_lines
        
        print(f"   📊 Всего строк: {total_lines}")
        print(f"   💬 Комментариев: {comment_lines}")
        print(f"   📝 Стоп-слов: {word_lines}")
        print(f"   🔳 Пустых строк: {empty_lines}")
    else:
        print(f"❌ Файл стоп-слов не найден: {STOPWORDS_FILE}")
    
    print()

async def test_telethon_manager():
    """Тест TelethonManager"""
    print("🧪 Тестирование TelethonManager...")
    
    try:
        manager = TelethonManager()
        print("✅ TelethonManager создан успешно")
        
        # Тест методов
        accounts = manager.get_connected_accounts()
        print(f"📱 Подключенных аккаунтов: {len(accounts)}")
        
        # Тест перезагрузки стоп-слов
        count = manager.reload_stopwords()
        print(f"🔄 Перезагружено стоп-слов: {count}")
        
    except Exception as e:
        print(f"❌ Ошибка TelethonManager: {e}")
    
    print()

def test_imports():
    """Тест импорта модулей"""
    print("🧪 Тестирование импорта модулей...")
    
    modules = [
        ('aiogram', 'aiogram'),
        ('telethon', 'telethon'),
        ('dotenv', 'python-dotenv'),
        ('asyncio', 'встроенный модуль')
    ]
    
    for module_name, package_name in modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name} ({package_name})")
        except ImportError:
            print(f"❌ {module_name} ({package_name}) - не установлен")
    
    print()

def test_project_structure():
    """Тест структуры проекта"""
    print("🧪 Тестирование структуры проекта...")
    
    required_paths = [
        project_root / 'src',
        project_root / 'src' / 'bot',
        project_root / 'src' / 'monitoring',
        project_root / 'src' / 'config',
        project_root / 'src' / 'utils',
        project_root / 'data',
        project_root / 'scripts',
        STOPWORDS_FILE,
        project_root / 'data' / '.env'
    ]
    
    for path in required_paths:
        if path.exists():
            print(f"✅ {path.name}: существует")
        else:
            print(f"❌ {path.name}: не найден")
    
    print()

def main():
    """Главная функция тестирования"""
    print("🔍 PM Searcher - Тестирование системы (новая архитектура)")
    print("=" * 60)
    
    # Запуск тестов
    test_imports()
    test_project_structure()
    test_environment()
    test_directories()
    test_config()
    test_stopwords()
    
    # Асинхронные тесты
    asyncio.run(test_telethon_manager())
    
    print("🏁 Тестирование завершено!")
    print("\n💡 Если все тесты прошли успешно, система готова к работе.")
    print("🚀 Запустите систему командой: python main.py")

if __name__ == "__main__":
    main()

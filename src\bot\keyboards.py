"""
Клавиатуры для Telegram бота
"""

from aiogram.types import ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
from ..config.environment import env_config

# Основная клавиатура для пользователей (оставлена для совместимости)
main_keyboard = ReplyKeyboardMarkup(
    keyboard=[
        [KeyboardButton(text="Подключить аккаунт")],
        [KeyboardButton(text="Мои аккаунты")],
        [KeyboardButton(text="Управление стоп-словами")]
    ],
    resize_keyboard=True
)

# Административная клавиатура (полный доступ для администраторов)
admin_keyboard = ReplyKeyboardMarkup(
    keyboard=[
        [KeyboardButton(text="Подключить аккаунт")],
        [KeyboardButton(text="Мои аккаунты")],
        [KeyboardButton(text="Управление стоп-словами")],
        [KeyboardButton(text="⚙️ Настройки мониторинга")],
        [KeyboardButton(text="👥 Управление тимлидами")],
        [KeyboardButton(text="⏰ Время ответа")],
        [KeyboardButton(text="👤 Управление ролями")],
        [KeyboardButton(text="Статистика")]
    ],
    resize_keyboard=True
)

# Клавиатура для тимлидов (ограниченный доступ)
teamlead_keyboard = ReplyKeyboardMarkup(
    keyboard=[
        [KeyboardButton(text="Мои аккаунты")],
        [KeyboardButton(text="Статистика")]
    ],
    resize_keyboard=True
)

def get_keyboard(user_id: int) -> ReplyKeyboardMarkup:
    """
    Получение клавиатуры в зависимости от роли пользователя

    Роли:
    - admin: полный доступ ко всем функциям
    - teamlead: доступ к основным функциям (без управления тимлидами и настроек)
    - none: нет доступа к боту
    """
    from src.config.roles_manager import roles_manager

    user_role = roles_manager.get_user_role(user_id)

    if user_role == "admin":
        # Администраторы получают полный доступ
        return admin_keyboard
    elif user_role == "teamlead":
        # Тимлиды получают ограниченный доступ
        return teamlead_keyboard
    else:
        # Пользователи без роли не получают клавиатуру
        return None


# =======================================
# КЛАВИАТУРЫ ДЛЯ ПОДКЛЮЧЕНИЯ АККАУНТОВ
# =======================================

def cancel_phone_input_keyboard() -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для отмены ввода номера телефона

    Используется в состоянии ожидания номера телефона,
    когда пользователь может захотеть отменить процесс подключения.

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопкой отмены
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="❌ Отменить",
                callback_data="cancel_phone_input"
            )
        ]
    ]

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def cancel_account_connection_keyboard() -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для отмены подключения аккаунта

    Используется в состоянии ожидания кода подтверждения,
    когда пользователь может захотеть отменить процесс подключения.

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопкой отмены
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="🔄 Переавторизация",
                callback_data="force_reauth_account"
            )
        ],
        [
            InlineKeyboardButton(
                text="❌ Отмена",
                callback_data="cancel_account_connection"
            )
        ]
    ]

    return InlineKeyboardMarkup(inline_keyboard=buttons)


# =======================================
# КЛАВИАТУРЫ ДЛЯ УПРАВЛЕНИЯ СТОП-СЛОВАМИ  
# =======================================

def stopwords_main_keyboard() -> ReplyKeyboardMarkup:
    """
    Главное меню управления стоп-словами (Reply-клавиатура)
    
    Предоставляет основную навигацию по функциям управления стоп-словами:
    - Просмотр всех стоп-слов с пагинацией
    - Добавление новых стоп-слов
    - Поиск по существующим стоп-словам
    - Импорт/экспорт стоп-слов из/в файл
    - Возврат в административную панель
    
    Returns:
        ReplyKeyboardMarkup: Клавиатура с основными функциями управления
    """
    keyboard = ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="📋 Просмотр стоп-слов")],
            [KeyboardButton(text="➕ Добавить стоп-слово")],
            [KeyboardButton(text="🔍 Поиск стоп-слов")],
            [KeyboardButton(text="📥 Импорт из файла"), KeyboardButton(text="📤 Экспорт в файл")],
            [KeyboardButton(text="🔙 Назад в админку")]
        ],
        resize_keyboard=True,
        one_time_keyboard=False
    )
    return keyboard











def delete_confirmation_keyboard(stopword: str) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для подтверждения удаления конкретного стоп-слова
    
    Предоставляет финальное подтверждение перед удалением стоп-слова
    для предотвращения случайных удалений.
    
    Args:
        stopword (str): Стоп-слово которое будет удалено
        
    Returns:
        InlineKeyboardMarkup: Клавиатура подтверждения удаления
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="✅ Да, удалить",
                callback_data=f"sw_delete:confirm:{stopword}"
            ),
            InlineKeyboardButton(
                text="❌ Нет, отмена",
                callback_data="sw_delete:cancel"
            )
        ]
    ]
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def import_confirmation_keyboard(preview_data: dict) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для подтверждения импорта стоп-слов
    
    Показывает предварительный просмотр данных импорта и позволяет
    пользователю подтвердить или отменить операцию импорта.
    
    Args:
        preview_data (dict): Данные предварительного просмотра с информацией
                           о количестве новых слов, дубликатов и т.д.
        
    Returns:
        InlineKeyboardMarkup: Клавиатура подтверждения импорта
    """
    new_words_count = preview_data.get('new_words', 0)
    
    buttons = [
        [
            InlineKeyboardButton(
                text=f"✅ Импортировать ({new_words_count} слов)",
                callback_data="sw_import:confirm"
            )
        ],
        [
            InlineKeyboardButton(
                text="❌ Отменить импорт",
                callback_data="sw_import:cancel"
            )
        ]
    ]
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def export_options_keyboard() -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для выбора вариантов экспорта стоп-слов
    
    Предоставляет различные опции экспорта:
    - Экспорт всех стоп-слов
    - Экспорт только результатов последнего поиска (если был выполнен поиск)
    
    Returns:
        InlineKeyboardMarkup: Клавиатура с опциями экспорта
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="📤 Все стоп-слова",
                callback_data="sw_export:all"
            )
        ],
        [
            InlineKeyboardButton(
                text="🔍 Результаты поиска",
                callback_data="sw_export:search"
            )
        ],
        [
            InlineKeyboardButton(
                text="❌ Отмена",
                callback_data="sw_export:cancel"
            )
        ]
    ]
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def back_to_stopwords_keyboard() -> InlineKeyboardMarkup:
    """
    Вспомогательная inline-клавиатура для возврата в главное меню стоп-слов
    
    Простая клавиатура с одной кнопкой возврата, используется в различных
    контекстах когда нужно предоставить только опцию возврата.
    
    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопкой возврата
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="🔙 Назад к стоп-словам", 
                callback_data="back_to_stopwords_main"
            )
        ]
    ]
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def cancel_operation_keyboard() -> InlineKeyboardMarkup:
    """
    Вспомогательная inline-клавиатура для отмены текущей операции
    
    Универсальная клавиатура отмены, используется в различных сценариях
    когда пользователю нужно прервать текущую операцию и вернуться назад.
    
    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопкой отмены
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="❌ Отменить операцию", 
                callback_data="cancel_current_operation"
            )
        ]
    ]
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def stopwords_pagination_keyboard(current_page: int, total_pages: int, search_query: str = None) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для пагинации списка стоп-слов

    Создает клавиатуру с кнопками навигации по страницам списка стоп-слов.
    Поддерживает как обычный просмотр, так и результаты поиска.

    Args:
        current_page (int): Текущая страница (начиная с 1)
        total_pages (int): Общее количество страниц
        search_query (str, optional): Поисковый запрос (если это результаты поиска)

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопками пагинации
    """
    buttons = []

    # Кнопки навигации по страницам
    nav_buttons = []

    # Кнопка "Предыдущая страница"
    if current_page > 1:
        nav_buttons.append(
            InlineKeyboardButton(
                text="◀️ Пред",
                callback_data=f"sw_page:{current_page - 1}"
            )
        )

    # Информация о текущей странице
    nav_buttons.append(
        InlineKeyboardButton(
            text=f"{current_page}/{total_pages}",
            callback_data="sw_page_info"
        )
    )

    # Кнопка "Следующая страница"
    if current_page < total_pages:
        nav_buttons.append(
            InlineKeyboardButton(
                text="След ▶️",
                callback_data=f"sw_page:{current_page + 1}"
            )
        )

    if nav_buttons:
        buttons.append(nav_buttons)

    # Дополнительные кнопки в зависимости от контекста
    if search_query:
        # Для результатов поиска
        buttons.append([
            InlineKeyboardButton(
                text="🔍 Новый поиск",
                callback_data="sw_new_search"
            ),
            InlineKeyboardButton(
                text="📤 Экспорт результатов",
                callback_data="sw_export_search_results"
            )
        ])
    else:
        # Для обычного списка
        buttons.append([
            InlineKeyboardButton(
                text="🔍 Поиск",
                callback_data="sw_start_search"
            ),
            InlineKeyboardButton(
                text="➕ Добавить",
                callback_data="sw_add_new"
            )
        ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def stopword_actions_keyboard(stopword: str) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для действий с конкретным стоп-словом

    Предоставляет опции для работы с выбранным стоп-словом:
    удаление, редактирование и другие операции.

    Args:
        stopword (str): Стоп-слово для которого создается меню действий

    Returns:
        InlineKeyboardMarkup: Клавиатура с действиями для стоп-слова
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="🗑️ Удалить",
                callback_data=f"sw_action:delete:{stopword}"
            ),
            InlineKeyboardButton(
                text="📋 Копировать",
                callback_data=f"sw_action:copy:{stopword}"
            )
        ],
        [
            InlineKeyboardButton(
                text="🔍 Найти похожие",
                callback_data=f"sw_action:find_similar:{stopword}"
            )
        ],
        [
            InlineKeyboardButton(
                text="❌ Отмена",
                callback_data="sw_action:cancel"
            )
        ]
    ]

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def search_results_keyboard(results_count: int, current_page: int, total_pages: int, query: str) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для результатов поиска стоп-слов

    Специализированная клавиатура для отображения результатов поиска
    с дополнительными опциями для работы с найденными словами.

    Args:
        results_count (int): Количество найденных результатов
        current_page (int): Текущая страница результатов
        total_pages (int): Общее количество страниц результатов
        query (str): Поисковый запрос

    Returns:
        InlineKeyboardMarkup: Клавиатура для результатов поиска
    """
    buttons = []

    # Навигация по страницам результатов (если больше одной страницы)
    if total_pages > 1:
        nav_buttons = []

        if current_page > 1:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="◀️ Пред",
                    callback_data=f"sw_search_page:{current_page - 1}"
                )
            )

        nav_buttons.append(
            InlineKeyboardButton(
                text=f"{current_page}/{total_pages}",
                callback_data="sw_search_page_info"
            )
        )

        if current_page < total_pages:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="След ▶️",
                    callback_data=f"sw_search_page:{current_page + 1}"
                )
            )

        buttons.append(nav_buttons)

    # Действия с результатами поиска
    if results_count > 0:
        buttons.append([
            InlineKeyboardButton(
                text="📤 Экспорт результатов",
                callback_data="sw_export_search_results"
            ),
            InlineKeyboardButton(
                text="🗑️ Удалить все найденные",
                callback_data="sw_delete_search_results"
            )
        ])

    # Дополнительные опции поиска
    buttons.append([
        InlineKeyboardButton(
            text="🔍 Новый поиск",
            callback_data="sw_new_search"
        ),
        InlineKeyboardButton(
            text="📋 Показать все",
            callback_data="sw_show_all_stopwords"
        )
    ])

    # Кнопка возврата
    buttons.append([
        InlineKeyboardButton(
            text="🔙 Назад к стоп-словам",
            callback_data="back_to_stopwords_main"
        )
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


# =======================================
# КЛАВИАТУРЫ ДЛЯ УПРАВЛЕНИЯ АККАУНТАМИ
# =======================================

def accounts_management_keyboard(accounts: list, user_id: int, user_role: str = None) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для управления аккаунтами

    Создает клавиатуру со списком всех аккаунтов и кнопками управления.
    Каждый аккаунт отображается с кнопкой удаления.

    Args:
        accounts (list): Список аккаунтов с информацией
        user_id (int): ID пользователя для проверки прав доступа

    Returns:
        InlineKeyboardMarkup: Клавиатура с аккаунтами и кнопками управления
    """
    buttons = []

    # Добавляем кнопки для каждого аккаунта (если есть)
    if accounts:
        for i, account in enumerate(accounts):
            # Поддерживаем как старый формат (user_id), так и новый (phone)
            phone = account['phone']
            monitoring = account['monitoring']

            # Статус аккаунта
            authorized = account.get('authorized', True)
            needs_reauth = account.get('needs_reauth', False)

            if not authorized or needs_reauth:
                status_emoji = "⚠️"
                status_text = "Требует авторизации"
            elif monitoring:
                status_emoji = "🟢"
                status_text = "Активен"
            else:
                status_emoji = "🔴"
                status_text = "Неактивен"

            # Информация об аккаунте
            account_info = f"{status_emoji} {phone} ({status_text})"

            # Кнопка с номером и статусом + кнопка удаления
            account_buttons = [
                InlineKeyboardButton(
                    text=account_info,
                    callback_data=f"account_details:{phone}"
                )
            ]

            # Добавляем кнопку удаления только для администраторов
            # (все аккаунты теперь общие)
            from ..config.environment import env_config
            if env_config.is_admin(user_id):
                account_buttons.append(
                    InlineKeyboardButton(
                        text="🗑️ Удалить",
                        callback_data=f"delete_account:{phone}"
                    )
                )

            buttons.append(account_buttons)

            # Добавляем разделитель между аккаунтами (кроме последнего)
            if i < len(accounts) - 1:
                buttons.append([
                    InlineKeyboardButton(
                        text="─────────────────",
                        callback_data="separator"
                    )
                ])

    # Определяем роль если не передана
    if user_role is None:
        from src.config.roles_manager import roles_manager
        user_role = roles_manager.get_user_role(user_id)

    # Кнопка добавления нового аккаунта (только для администраторов)
    if user_role == "admin":
        buttons.append([
            InlineKeyboardButton(
                text="➕ Подключить новый аккаунт",
                callback_data="add_new_account"
            )
        ])

    # Кнопка обновления списка
    buttons.append([
        InlineKeyboardButton(
            text="🔄 Обновить список",
            callback_data="refresh_accounts_list"
        )
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def account_deletion_confirmation_keyboard(phone: str, _unused_param: str = None) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для подтверждения удаления аккаунта

    Предоставляет финальное подтверждение перед удалением аккаунта
    для предотвращения случайных удалений.

    Args:
        phone (str): Номер телефона аккаунта для удаления
        _unused_param (str): Неиспользуемый параметр для совместимости

    Returns:
        InlineKeyboardMarkup: Клавиатура подтверждения удаления
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="✅ Да, удалить аккаунт",
                callback_data=f"confirm_delete_account:{phone}"
            )
        ],
        [
            InlineKeyboardButton(
                text="❌ Нет, отменить",
                callback_data="cancel_delete_account"
            )
        ],
        [
            InlineKeyboardButton(
                text="🔙 Назад к списку аккаунтов",
                callback_data="back_to_accounts_list"
            )
        ]
    ]

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def account_details_keyboard(phone: str, current_user_id: int) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для просмотра деталей аккаунта

    Показывает дополнительные действия для конкретного аккаунта.

    Args:
        phone (str): Номер телефона аккаунта
        current_user_id (int): ID текущего пользователя

    Returns:
        InlineKeyboardMarkup: Клавиатура с действиями для аккаунта
    """
    buttons = []

    # Проверяем права доступа (только администраторы)
    from ..config.environment import env_config
    has_delete_rights = env_config.is_admin(current_user_id)

    if has_delete_rights:
        buttons.append([
            InlineKeyboardButton(
                text="🗑️ Удалить аккаунт",
                callback_data=f"delete_account:{phone}"
            )
        ])

    # Кнопка возврата к списку
    buttons.append([
        InlineKeyboardButton(
            text="🔙 Назад к списку аккаунтов",
            callback_data="back_to_accounts_list"
        )
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


# =======================================
# КЛАВИАТУРЫ ДЛЯ НАСТРОЕК МОНИТОРИНГА
# =======================================

def monitoring_settings_keyboard(group_monitoring_enabled: bool) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для управления настройками мониторинга

    Предоставляет кнопки для включения/отключения мониторинга групповых чатов
    и другие настройки мониторинга.

    Args:
        group_monitoring_enabled (bool): Текущее состояние мониторинга групповых чатов

    Returns:
        InlineKeyboardMarkup: Клавиатура с настройками мониторинга
    """
    # Текст кнопки в зависимости от текущего состояния
    toggle_text = "❌ Отключить групповые чаты" if group_monitoring_enabled else "✅ Включить групповые чаты"

    buttons = [
        [
            InlineKeyboardButton(
                text=toggle_text,
                callback_data="monitoring_toggle_groups"
            )
        ],
        [
            InlineKeyboardButton(
                text="🔄 Перезагрузить настройки",
                callback_data="monitoring_reload"
            )
        ],
        [
            InlineKeyboardButton(
                text="🔙 Назад в админку",
                callback_data="monitoring_back"
            )
        ]
    ]

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def back_to_admin_keyboard() -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для возврата в административную панель

    Простая клавиатура с кнопкой возврата в админ-панель.

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопкой возврата
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="🔙 Назад в админку",
                callback_data="monitoring_back"
            )
        ]
    ]

    return InlineKeyboardMarkup(inline_keyboard=buttons)


# =======================================
# КЛАВИАТУРЫ ДЛЯ УПРАВЛЕНИЯ ТИМЛИДАМИ
# =======================================

def teamlead_management_keyboard() -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для управления тимлидами

    Предоставляет основные функции управления тимлидами:
    - Добавление нового тимлида
    - Назначение номеров тимлиду
    - Просмотр назначений
    - Удаление тимлида

    Returns:
        InlineKeyboardMarkup: Клавиатура с функциями управления тимлидами
    """
    buttons = [
        [
            InlineKeyboardButton(
                text="➕ Добавить тимлида",
                callback_data="teamlead_add"
            ),
            InlineKeyboardButton(
                text="📱 Назначить номера",
                callback_data="teamlead_assign_phones"
            )
        ],
        [
            InlineKeyboardButton(
                text="👁️ Просмотр назначений",
                callback_data="teamlead_view_assignments"
            ),
            InlineKeyboardButton(
                text="📋 Список тимлидов",
                callback_data="teamlead_list"
            )
        ],
        [
            InlineKeyboardButton(
                text="🗑️ Удалить тимлида",
                callback_data="teamlead_remove"
            )
        ],
        [
            InlineKeyboardButton(
                text="🔙 Назад в админку",
                callback_data="teamlead_back_to_admin"
            )
        ]
    ]

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def teamlead_selection_keyboard(teamleads: dict, action: str) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для выбора тимлида из списка

    Args:
        teamleads (dict): Словарь тимлидов {username: data}
        action (str): Действие для callback_data (assign, remove, view)

    Returns:
        InlineKeyboardMarkup: Клавиатура с тимлидами для выбора
    """
    buttons = []

    for username, data in teamleads.items():
        assigned_count = len(data.get('assigned_phones', []))
        button_text = f"{username} ({assigned_count} номеров)"

        buttons.append([
            InlineKeyboardButton(
                text=button_text,
                callback_data=f"teamlead_{action}:{username}"
            )
        ])

    # Кнопка отмены
    buttons.append([
        InlineKeyboardButton(
            text="❌ Отмена",
            callback_data="teamlead_cancel"
        )
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def phone_selection_keyboard(phones: list, selected_phones: list = None) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для выбора номеров телефонов

    Args:
        phones (list): Список доступных номеров
        selected_phones (list): Список уже выбранных номеров

    Returns:
        InlineKeyboardMarkup: Клавиатура с номерами для выбора
    """
    if selected_phones is None:
        selected_phones = []

    buttons = []

    for phone in phones:
        is_selected = phone in selected_phones
        emoji = "✅" if is_selected else "⭕"
        button_text = f"{emoji} {phone}"

        buttons.append([
            InlineKeyboardButton(
                text=button_text,
                callback_data=f"phone_toggle:{phone}"
            )
        ])

    # Кнопки управления
    action_buttons = []

    if selected_phones:
        action_buttons.append(
            InlineKeyboardButton(
                text="💾 Сохранить выбор",
                callback_data="phone_save_selection"
            )
        )

    action_buttons.append(
        InlineKeyboardButton(
            text="🔄 Выбрать все",
            callback_data="phone_select_all"
        )
    )

    if action_buttons:
        buttons.append(action_buttons)

    # Кнопка отмены
    buttons.append([
        InlineKeyboardButton(
            text="❌ Отмена",
            callback_data="phone_cancel"
        )
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def teamlead_confirmation_keyboard(action: str, username: str) -> InlineKeyboardMarkup:
    """
    Inline-клавиатура для подтверждения действий с тимлидом

    Args:
        action (str): Действие для подтверждения (remove, clear_assignments)
        username (str): Имя тимлида

    Returns:
        InlineKeyboardMarkup: Клавиатура подтверждения
    """
    action_texts = {
        'remove': 'удалить тимлида',
        'clear_assignments': 'очистить назначения'
    }

    action_text = action_texts.get(action, 'выполнить действие')

    buttons = [
        [
            InlineKeyboardButton(
                text=f"✅ Да, {action_text}",
                callback_data=f"teamlead_confirm_{action}:{username}"
            )
        ],
        [
            InlineKeyboardButton(
                text="❌ Нет, отменить",
                callback_data="teamlead_cancel_action"
            )
        ]
    ]

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def response_time_management_keyboard():
    """Клавиатура для управления настройками времени ответа"""
    buttons = [
        [
            InlineKeyboardButton(text="⚙️ Основные настройки", callback_data="response_time_main_settings"),
            InlineKeyboardButton(text="📱 Настройки аккаунтов", callback_data="response_time_account_settings")
        ],
        [
            InlineKeyboardButton(text="🔄 Обновить", callback_data="refresh_response_time_management"),
            InlineKeyboardButton(text="🏠 Главное меню", callback_data="back_to_main")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def response_time_main_settings_keyboard(enabled: bool):
    """Клавиатура для основных настроек времени ответа"""
    status_text = "🟢 Включить" if not enabled else "🔴 Выключить"
    status_callback = "enable_response_time" if not enabled else "disable_response_time"

    buttons = [
        [
            InlineKeyboardButton(text=status_text, callback_data=status_callback)
        ],
        [
            InlineKeyboardButton(text="⏰ Время ответа по умолчанию", callback_data="set_default_response_time")
        ],
        [
            InlineKeyboardButton(text="🔄 Интервал проверки", callback_data="set_check_interval")
        ],
        [
            InlineKeyboardButton(text="◀️ Назад", callback_data="back_to_response_time_management")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def response_time_account_settings_keyboard(accounts: list):
    """Клавиатура для настроек времени ответа по аккаунтам"""
    buttons = []

    # Добавляем кнопки для каждого аккаунта
    for account in accounts:
        phone = account['phone']
        # Показываем статус авторизации
        if account.get('authorized', True) and not account.get('needs_reauth', False):
            status_emoji = "✅"
        else:
            status_emoji = "⚠️"

        buttons.append([
            InlineKeyboardButton(
                text=f"{status_emoji} {phone}",
                callback_data=f"response_time_account:{phone}"
            )
        ])

    # Кнопки массового управления
    if accounts:  # Показываем только если есть аккаунты
        buttons.extend([
            [
                InlineKeyboardButton(text="🔧 Массовые настройки", callback_data="response_time_bulk_settings")
            ]
        ])

    # Кнопки управления
    buttons.extend([
        [
            InlineKeyboardButton(text="🔄 Обновить список", callback_data="refresh_response_time_accounts")
        ],
        [
            InlineKeyboardButton(text="◀️ Назад", callback_data="back_to_response_time_management")
        ]
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def response_time_cancel_keyboard():
    """Клавиатура с кнопкой отмены для ввода времени ответа"""
    buttons = [
        [
            InlineKeyboardButton(text="❌ Отменить", callback_data="cancel_response_time_input")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def response_time_account_detail_keyboard(phone: str, has_custom_settings: bool):
    """Клавиатура для детальных настроек аккаунта"""
    buttons = [
        [
            InlineKeyboardButton(text="⏰ Установить время ответа", callback_data=f"set_account_response_time:{phone}")
        ]
    ]

    if has_custom_settings:
        buttons.append([
            InlineKeyboardButton(text="🗑️ Сбросить к умолчанию", callback_data=f"reset_account_response_time:{phone}")
        ])

    buttons.extend([
        [
            InlineKeyboardButton(text="◀️ Назад", callback_data="back_to_response_time_account_settings")
        ]
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def response_time_bulk_settings_keyboard():
    """Клавиатура для массовых настроек времени ответа"""
    buttons = [
        [
            InlineKeyboardButton(text="⏰ Установить время для всех", callback_data="bulk_set_response_time")
        ],
        [
            InlineKeyboardButton(text="🗑️ Сбросить все к умолчанию", callback_data="bulk_clear_response_time")
        ],
        [
            InlineKeyboardButton(text="◀️ Назад", callback_data="back_to_response_time_account_settings")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def bulk_operation_confirmation_keyboard(operation_type: str, accounts_count: int):
    """Клавиатура подтверждения массовой операции"""
    if operation_type == "set":
        confirm_text = f"✅ Да, установить для {accounts_count} аккаунтов"
        confirm_callback = "confirm_bulk_set_response_time"
    elif operation_type == "clear":
        confirm_text = f"✅ Да, сбросить {accounts_count} аккаунтов"
        confirm_callback = "confirm_bulk_clear_response_time"
    else:
        confirm_text = "✅ Подтвердить"
        confirm_callback = "confirm_bulk_operation"

    buttons = [
        [
            InlineKeyboardButton(text=confirm_text, callback_data=confirm_callback)
        ],
        [
            InlineKeyboardButton(text="❌ Отмена", callback_data="response_time_bulk_settings")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def roles_management_keyboard():
    """Клавиатура для управления ролями"""
    buttons = [
        [
            InlineKeyboardButton(text="👤 Назначить тимлида", callback_data="assign_teamlead_role")
        ],
        [
            InlineKeyboardButton(text="🗑️ Удалить роль", callback_data="remove_user_role")
        ],
        [
            InlineKeyboardButton(text="👥 Просмотреть все роли", callback_data="view_all_roles")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def back_to_roles_management_keyboard():
    """Клавиатура возврата к управлению ролями"""
    buttons = [
        [
            InlineKeyboardButton(text="◀️ Назад", callback_data="back_to_roles_management")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)

"""
Тесты настроек мониторинга

Покрывает функциональность MonitoringSettings включая новую фильтрацию ботов.
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import patch, Mock

from src.config.monitoring_settings import MonitoringSettings


class TestMonitoringSettings:
    """Тесты класса MonitoringSettings"""
    
    def test_default_settings_initialization(self):
        """Тест инициализации настроек по умолчанию"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                # Проверяем настройки по умолчанию
                assert settings.group_chat_monitoring_enabled is True
                assert settings.private_chat_monitoring_enabled is True
                assert settings.bot_filtering_enabled is True
    
    def test_load_existing_settings(self):
        """Тест загрузки существующих настроек из файла"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            # Создаем файл с настройками
            test_settings = {
                "group_chat_monitoring_enabled": False,
                "private_chat_monitoring_enabled": True,
                "bot_filtering_enabled": False,
                "last_updated": "2023-01-01T12:00:00",
                "updated_by": 123456789
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(test_settings, f)
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                assert settings.group_chat_monitoring_enabled is False
                assert settings.private_chat_monitoring_enabled is True
                assert settings.bot_filtering_enabled is False
    
    def test_set_group_chat_monitoring(self):
        """Тест установки настройки мониторинга групповых чатов"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                # Отключаем мониторинг групповых чатов
                result = settings.set_group_chat_monitoring(False, updated_by=123456789)
                
                assert result is True
                assert settings.group_chat_monitoring_enabled is False
                
                # Проверяем что настройки сохранились в файл
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    assert saved_settings["group_chat_monitoring_enabled"] is False
                    assert saved_settings["updated_by"] == 123456789
    
    def test_set_bot_filtering(self):
        """Тест установки настройки фильтрации ботов"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                # Отключаем фильтрацию ботов
                result = settings.set_bot_filtering(False, updated_by=987654321)
                
                assert result is True
                assert settings.bot_filtering_enabled is False
                
                # Проверяем что настройки сохранились в файл
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    assert saved_settings["bot_filtering_enabled"] is False
                    assert saved_settings["updated_by"] == 987654321
    
    def test_get_monitoring_status(self):
        """Тест получения полного статуса мониторинга"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                status = settings.get_monitoring_status()
                
                assert "group_chat_monitoring" in status
                assert "private_chat_monitoring" in status
                assert "bot_filtering" in status
                assert "last_updated" in status
                assert "updated_by" in status
                
                # Проверяем значения по умолчанию
                assert status["group_chat_monitoring"] is True
                assert status["private_chat_monitoring"] is True
                assert status["bot_filtering"] is True
    
    def test_should_monitor_chat(self):
        """Тест определения необходимости мониторинга чата"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                # По умолчанию все типы чатов мониторятся
                assert settings.should_monitor_chat('private') is True
                assert settings.should_monitor_chat('group') is True
                assert settings.should_monitor_chat('supergroup') is True
                assert settings.should_monitor_chat('channel') is True
                
                # Отключаем мониторинг групповых чатов
                settings.set_group_chat_monitoring(False)
                
                # Приватные чаты все еще мониторятся
                assert settings.should_monitor_chat('private') is True
                # Групповые чаты не мониторятся
                assert settings.should_monitor_chat('group') is False
                assert settings.should_monitor_chat('supergroup') is False
                assert settings.should_monitor_chat('channel') is False
    
    def test_reload_settings(self):
        """Тест перезагрузки настроек"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                # Изменяем настройки
                settings.set_bot_filtering(False)
                assert settings.bot_filtering_enabled is False
                
                # Изменяем файл напрямую
                new_settings = {
                    "group_chat_monitoring_enabled": True,
                    "private_chat_monitoring_enabled": True,
                    "bot_filtering_enabled": True,
                    "last_updated": None,
                    "updated_by": None
                }
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(new_settings, f)
                
                # Перезагружаем настройки
                result = settings.reload_settings()
                
                assert result is True
                assert settings.bot_filtering_enabled is True
    
    def test_error_handling_in_save_settings(self):
        """Тест обработки ошибок при сохранении настроек"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "nonexistent_dir" / "monitoring_config.json"
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                settings = MonitoringSettings()
                
                # Пытаемся сохранить в несуществующую директорию
                # Но MonitoringSettings должен создать директорию автоматически
                result = settings.set_bot_filtering(False)
                
                # Проверяем что операция прошла успешно
                assert result is True
                assert settings.bot_filtering_enabled is False
    
    def test_error_handling_in_load_settings(self):
        """Тест обработки ошибок при загрузке настроек"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "monitoring_config.json"
            
            # Создаем файл с невалидным JSON
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write("invalid json content")
            
            with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', config_file):
                # Должны получить настройки по умолчанию при ошибке загрузки
                settings = MonitoringSettings()
                
                assert settings.group_chat_monitoring_enabled is True
                assert settings.private_chat_monitoring_enabled is True
                assert settings.bot_filtering_enabled is True

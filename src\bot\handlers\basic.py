"""
Основные обработчики команд
"""

from aiogram import Dispatcher, types
from aiogram.filters import Command
from ...config.environment import env_config
from ..keyboards import get_keyboard

def register_basic_handlers(dp: Dispatcher):
    """Регистрация основных обработчиков"""
    
    @dp.message(Command("start"))
    async def cmd_start(message: types.Message):
        user_id = message.from_user.id

        # Проверяем роль пользователя
        from src.config.roles_manager import roles_manager
        user_role = roles_manager.get_user_role(user_id)

        if user_role == "none":
            # Пользователь без роли
            welcome_text = (
                "❌ **Доступ запрещен**\n\n"
                "У вас нет прав для использования этого бота.\n\n"
                "Для получения доступа обратитесь к администратору."
            )
            await message.answer(welcome_text, parse_mode='Markdown')
            return

        # Пользователь с ролью (admin или teamlead)
        username = message.from_user.username or "Пользователь"
        first_name = message.from_user.first_name or ""

        if user_role == "admin":
            welcome_text = (
                f"👑 **Добро пожаловать, администратор!**\n\n"
                f"Привет, {first_name}! У вас полный доступ к системе мониторинга сообщений.\n\n"
                f"🔧 **Доступные функции:**\n"
                f"• Подключение и управление аккаунтами\n"
                f"• Настройка мониторинга и стоп-слов\n"
                f"• Управление тимлидами и ролями\n"
                f"• Настройка времени ответа\n"
                f"• Просмотр статистики"
            )
        else:  # teamlead
            welcome_text = (
                f"👤 **Добро пожаловать, тимлид!**\n\n"
                f"Привет, {first_name}! Вы назначены тимлидом в системе мониторинга.\n\n"
                f"📋 **Доступные функции:**\n"
                f"• Просмотр назначенных вам аккаунтов\n"
                f"• Просмотр статистики\n\n"
                f"💡 **Примечание:** Вы будете получать уведомления от аккаунтов, которые вам назначил администратор."
            )

        keyboard = get_keyboard(user_id)
        await message.answer(welcome_text, reply_markup=keyboard, parse_mode='Markdown')

    @dp.message(Command("help"))
    async def cmd_help(message: types.Message):
        user_id = message.from_user.id
        
        help_text = (
            "🔍 Система мониторинга сообщений\n\n"
            "📱 Основные функции:\n"
            "• Подключить аккаунт - добавить Telegram аккаунт\n"
            "• Мои аккаунты - просмотр ваших аккаунтов\n"
            "• Управление стоп-словами - настройка фильтров\n\n"
            "🤖 Команды:\n"
            "/start - главное меню\n"
            "/help - эта справка\n"
        )
        
        if env_config.is_admin(user_id):
            help_text += (
                "\n👑 Администраторские команды:\n"
                "/reload_stopwords - перезагрузить стоп-слова\n"
                "• Статистика\n"
            )
        
        await message.answer(help_text)

    # Обработчик неизвестных текстовых сообщений (не команд и не кнопок)
    @dp.message()
    async def unknown_message(message: types.Message):
        # Игнорируем сообщения, которые могут быть обработаны другими обработчиками
        if message.text and (
            message.text.startswith('/') or  # команды
            message.text in ["Подключить аккаунт", "Мои аккаунты", "Управление стоп-словами",
                           "Все подключенные аккаунты", "Статистика",
                           "📋 Просмотр стоп-слов", "➕ Добавить стоп-слово",
                           "🔍 Поиск стоп-слов", "📥 Импорт из файла", "📤 Экспорт в файл",
                           "🔙 Назад в главное меню"]  # добавляем кнопку возврата
        ):
            return

        await message.answer(
            "❓ Неизвестная команда. Используйте кнопки меню или /help для справки.",
            reply_markup=get_keyboard(message.from_user.id)
        )

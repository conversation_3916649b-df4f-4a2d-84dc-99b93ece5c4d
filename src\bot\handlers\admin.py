"""
Административные обработчики
"""

from datetime import datetime
from aiogram import Dispatcher, types
from aiogram.filters import Command
from ...config.environment import env_config
from ...utils.logging import setup_logging

logger = setup_logging(__name__)

def register_admin_handlers(dp: <PERSON><PERSON>atcher, telethon_manager):
    """Регистрация административных обработчиков"""
    
    # Обработчик "Управление стоп-словами" перенесен в stopwords_management.py
    # для более продвинутого функционала



    @dp.message(lambda m: m.text == "Статистика")
    async def statistics(message: types.Message):
        user_id = message.from_user.id
        
        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return
        
        accounts = telethon_manager.get_connected_accounts()
        active_accounts = [acc for acc in accounts if acc['monitoring']]
        stopwords_count = len(telethon_manager.stopwords_manager.stopwords)
        
        text = (
            f"📊 Статистика системы\n\n"
            f"📱 Всего аккаунтов: {len(accounts)}\n"
            f"🟢 Активных: {len(active_accounts)}\n"
            f"🔴 Неактивных: {len(accounts) - len(active_accounts)}\n"
            f"📝 Стоп-слов: {stopwords_count}\n"
            f"👑 Администраторов: {len(env_config.ADMIN_CHAT_IDS)}\n"
            f"🕐 Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        
        await message.answer(text)

    @dp.message(Command("reload_stopwords"))
    async def reload_stopwords_cmd(message: types.Message):
        user_id = message.from_user.id
        
        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return
        
        try:
            count = telethon_manager.reload_stopwords()
            await message.answer(f"✅ Стоп-слова перезагружены. Загружено: {count}")
            logger.info(f"Администратор {user_id} перезагрузил стоп-слова")
        except Exception as e:
            await message.answer(f"❌ Ошибка перезагрузки: {e}")
            logger.error(f"Ошибка перезагрузки стоп-слов: {e}")

    @dp.message(Command("system_check"))
    async def system_check(message: types.Message):
        """Проверка целостности системы (только для администраторов)"""
        user_id = message.from_user.id

        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        try:
            await message.answer("🔍 Проверка целостности системы...", parse_mode='Markdown')

            # Выполняем проверку
            report = await telethon_manager.validate_system_integrity()

            # Формируем отчет
            status_emoji = {
                'healthy': '✅',
                'warning': '⚠️',
                'error': '❌'
            }.get(report['status'], '❓')

            text = f"{status_emoji} **Отчет о состоянии системы**\n\n"
            text += f"📊 **Общая статистика:**\n"
            text += f"• Статус: {report['status'].upper()}\n"
            text += f"• Активных аккаунтов: {report['accounts_count']}\n"
            text += f"• Мониторинг активен: {report['active_monitoring']}\n"
            text += f"• Файлов сессий: {report['session_files_found']}\n"
            text += f"• Записей метаданных: {report['metadata_entries']}\n"

            if report['issues']:
                text += f"\n⚠️ **Обнаруженные проблемы:**\n"
                for issue in report['issues']:
                    text += f"• {issue}\n"

            if report['recommendations']:
                text += f"\n💡 **Рекомендации:**\n"
                for rec in report['recommendations']:
                    text += f"• {rec}\n"

            text += f"\n🕐 Проверка выполнена: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            await message.answer(text, parse_mode='Markdown')
            logger.info(f"Администратор {user_id} выполнил проверку системы: статус={report['status']}")

        except Exception as e:
            logger.error(f"Ошибка при проверке системы: {e}")
            await message.answer(
                f"❌ **Ошибка проверки системы**\n\n"
                f"Произошла ошибка при выполнении проверки: {e}",
                parse_mode='Markdown'
            )

    @dp.message(Command("delete_account"))
    async def delete_account_command(message: types.Message):
        """Удаление аккаунта по команде (только для администраторов)"""
        user_id = message.from_user.id

        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        try:
            # Получаем номер телефона из команды
            command_parts = message.text.split()
            if len(command_parts) != 2:
                await message.answer(
                    "❌ **Неверный формат команды**\n\n"
                    "Используйте: `/delete_account ************`\n"
                    "Где `************` - номер телефона аккаунта для удаления",
                    parse_mode='Markdown'
                )
                return

            phone = command_parts[1].strip()

            # Проверяем, что аккаунт существует
            accounts = telethon_manager.get_connected_accounts()
            target_account = None
            for acc in accounts:
                if acc['phone'] == phone:
                    target_account = acc
                    break

            if not target_account:
                await message.answer(
                    f"❌ **Аккаунт не найден**\n\n"
                    f"Аккаунт с номером `{phone}` не найден в системе.\n\n"
                    f"Доступные аккаунты:\n" +
                    "\n".join([f"• `{acc['phone']}`" for acc in accounts]),
                    parse_mode='Markdown'
                )
                return

            # Показываем подтверждение
            status = "🟢 Активен" if target_account['monitoring'] else "🔴 Неактивен"

            await message.answer(
                f"⚠️ **ПОДТВЕРЖДЕНИЕ УДАЛЕНИЯ**\n\n"
                f"Вы действительно хотите удалить аккаунт?\n\n"
                f"📞 **Номер:** `{phone}`\n"
                f"📊 **Статус:** {status}\n\n"
                f"**Это действие необратимо!**\n\n"
                f"Для подтверждения отправьте:\n"
                f"`/confirm_delete {phone}`",
                parse_mode='Markdown'
            )

            logger.info(f"Администратор {user_id} запросил удаление аккаунта {phone} через команду")

        except Exception as e:
            logger.error(f"Ошибка при запросе удаления аккаунта: {e}")
            await message.answer(
                f"❌ **Ошибка**\n\n"
                f"Произошла ошибка при обработке запроса: {e}",
                parse_mode='Markdown'
            )

    @dp.message(Command("connection_status"))
    async def connection_status_command(message: types.Message):
        """Получить статус соединений (только для администраторов)"""
        user_id = message.from_user.id

        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        try:
            await message.answer("🔍 Получение статуса соединений...", parse_mode='Markdown')

            # Получаем детальный статус
            status = await telethon_manager.get_detailed_connection_status()

            # Формируем отчет
            text = f"🔗 **Статус соединений**\n\n"
            text += f"📊 **Сводка:**\n"
            text += f"• Всего клиентов: {status['total_clients']}\n"
            text += f"• Подключено: {status['summary']['connected']}\n"
            text += f"• Отключено: {status['summary']['disconnected']}\n"
            text += f"• Мониторинг активен: {status['summary']['monitoring']}\n"
            text += f"• Переподключается: {status['summary']['reconnecting']}\n"
            text += f"• Ошибок за час: {status['summary']['errors_last_hour']}\n"
            text += f"• Мониторинг здоровья: {'🟢 Активен' if status['monitoring_active'] else '🔴 Неактивен'}\n\n"

            if status['clients']:
                text += f"📱 **Детали по клиентам:**\n"
                for phone, client_info in status['clients'].items():
                    if 'error' in client_info:
                        text += f"• `{phone}`: ❌ Ошибка - {client_info['error'][:50]}...\n"
                        continue

                    conn_status = "🟢" if client_info['connected'] else "🔴"
                    mon_status = "🔍" if client_info['monitoring'] else "⏸️"
                    health_status = "💚" if client_info['health']['is_healthy'] else "💔"
                    reconnect_status = "🔄" if client_info['reconnecting'] else ""

                    text += f"• `{phone}`: {conn_status}{mon_status}{health_status}{reconnect_status}\n"

                    if client_info['health']['reconnect_attempts'] > 0:
                        text += f"  └ Попыток переподключения: {client_info['health']['reconnect_attempts']}\n"

                    if client_info['health']['last_error']:
                        error_short = client_info['health']['last_error'][:30]
                        text += f"  └ Последняя ошибка: {error_short}...\n"

            text += f"\n🕐 Обновлено: {datetime.now().strftime('%H:%M:%S')}"

            await message.answer(text, parse_mode='Markdown')
            logger.info(f"Администратор {user_id} запросил статус соединений")

        except Exception as e:
            logger.error(f"Ошибка при получении статуса соединений: {e}")
            await message.answer(
                f"❌ **Ошибка получения статуса**\n\n"
                f"Произошла ошибка: {e}",
                parse_mode='Markdown'
            )

    @dp.message(Command("connection_health"))
    async def connection_health_command(message: types.Message):
        """Получить отчет о здоровье соединений (только для администраторов)"""
        user_id = message.from_user.id

        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        try:
            await message.answer("🏥 Анализ здоровья соединений...", parse_mode='Markdown')

            # Получаем отчет о здоровье
            health_report = await telethon_manager.get_connection_health_report()

            # Формируем отчет
            text = f"🏥 **Отчет о здоровье соединений**\n\n"
            text += f"📊 **Сводка здоровья:**\n"
            text += f"• Здоровых: {health_report['health_summary']['healthy']} 💚\n"
            text += f"• Нездоровых: {health_report['health_summary']['unhealthy']} 💔\n"
            text += f"• Неизвестно: {health_report['health_summary']['unknown']} ❓\n"
            text += f"• Активных задач переподключения: {health_report['active_reconnect_tasks']}\n\n"

            if health_report['connection_stats']:
                text += f"📈 **Статистика соединений:**\n"
                for phone, stats in health_report['connection_stats'].items():
                    health_emoji = "💚" if stats['is_healthy'] else "💔"
                    text += f"• `{phone}` {health_emoji}\n"
                    text += f"  └ Подключений: {stats['connects']}, Отключений: {stats['disconnects']}\n"
                    text += f"  └ Ошибок: {stats['errors']}, Попыток переподключения: {stats['reconnect_attempts']}\n"

                    if stats['last_error']:
                        error_short = stats['last_error'][:40]
                        text += f"  └ Последняя ошибка: {error_short}...\n"

            text += f"\n🕐 Отчет создан: {datetime.now().strftime('%H:%M:%S')}"

            await message.answer(text, parse_mode='Markdown')
            logger.info(f"Администратор {user_id} запросил отчет о здоровье соединений")

        except Exception as e:
            logger.error(f"Ошибка при получении отчета о здоровье: {e}")
            await message.answer(
                f"❌ **Ошибка получения отчета**\n\n"
                f"Произошла ошибка: {e}",
                parse_mode='Markdown'
            )

    @dp.message(Command("force_reconnect"))
    async def force_reconnect_command(message: types.Message):
        """Принудительное переподключение всех клиентов (только для администраторов)"""
        user_id = message.from_user.id

        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        try:
            await message.answer("🔄 Запуск принудительного переподключения всех клиентов...", parse_mode='Markdown')

            # Выполняем принудительное переподключение
            result = await telethon_manager.force_reconnect_all()

            # Формируем отчет
            text = f"🔄 **Результат принудительного переподключения**\n\n"
            text += f"📊 **Сводка:**\n"
            text += f"• Всего клиентов: {result['total_clients']}\n"
            text += f"• Успешно запущено: {result['summary']['success']} ✅\n"
            text += f"• Ошибок: {result['summary']['failed']} ❌\n"
            text += f"• Пропущено: {result['summary']['skipped']} ⏭️\n\n"

            if result['reconnect_results']:
                text += f"📱 **Детали:**\n"
                for phone, res in result['reconnect_results'].items():
                    status_emoji = {
                        'initiated': '✅',
                        'failed': '❌',
                        'skipped': '⏭️'
                    }.get(res['status'], '❓')

                    text += f"• `{phone}`: {status_emoji} {res['reason']}\n"

            text += f"\n🕐 Выполнено: {datetime.now().strftime('%H:%M:%S')}"

            await message.answer(text, parse_mode='Markdown')
            logger.info(f"Администратор {user_id} выполнил принудительное переподключение")

        except Exception as e:
            logger.error(f"Ошибка при принудительном переподключении: {e}")
            await message.answer(
                f"❌ **Ошибка переподключения**\n\n"
                f"Произошла ошибка: {e}",
                parse_mode='Markdown'
            )

    @dp.message(Command("error_stats"))
    async def error_stats_command(message: types.Message):
        """Получить статистику ошибок (только для администраторов)"""
        user_id = message.from_user.id

        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        try:
            # Получаем параметр времени из команды (по умолчанию 24 часа)
            command_parts = message.text.split()
            hours = 24
            if len(command_parts) > 1:
                try:
                    hours = int(command_parts[1])
                    hours = max(1, min(hours, 168))  # От 1 часа до недели
                except ValueError:
                    hours = 24

            await message.answer(f"📊 Анализ ошибок за последние {hours} часов...", parse_mode='Markdown')

            # Получаем статистику ошибок
            error_stats = await telethon_manager.get_error_statistics(hours)

            # Формируем отчет
            text = f"📊 **Статистика ошибок за {hours}ч**\n\n"
            text += f"🔢 **Общая статистика:**\n"
            text += f"• Всего ошибок: {error_stats['summary']['total_errors']}\n"
            text += f"• База данных заблокирована: {error_stats['summary']['database_locked_errors']} 🔒\n"
            text += f"• Сетевые ошибки: {error_stats['summary']['network_errors']} 🌐\n"
            text += f"• Ошибки авторизации: {error_stats['summary']['auth_errors']} 🔑\n"
            text += f"• Прочие ошибки: {error_stats['summary']['other_errors']} ❓\n\n"

            if error_stats['clients']:
                text += f"📱 **По клиентам:**\n"
                for phone, client_stats in error_stats['clients'].items():
                    if client_stats['total_errors'] > 0:
                        text += f"• `{phone}`: {client_stats['total_errors']} ошибок\n"

                        # Показываем типы ошибок
                        error_types = []
                        for error_type, count in client_stats['error_types'].items():
                            if count > 0:
                                type_emoji = {
                                    'database_locked': '🔒',
                                    'network': '🌐',
                                    'auth': '🔑',
                                    'other': '❓'
                                }.get(error_type, '❓')
                                error_types.append(f"{type_emoji}{count}")

                        if error_types:
                            text += f"  └ {' '.join(error_types)}\n"

            text += f"\n🕐 Период: {error_stats['cutoff_time'][:16]} - {datetime.now().strftime('%Y-%m-%d %H:%M')}"

            await message.answer(text, parse_mode='Markdown')
            logger.info(f"Администратор {user_id} запросил статистику ошибок за {hours}ч")

        except Exception as e:
            logger.error(f"Ошибка при получении статистики ошибок: {e}")
            await message.answer(
                f"❌ **Ошибка получения статистики**\n\n"
                f"Произошла ошибка: {e}",
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Ошибка команды удаления аккаунта: {e}")
            await message.answer(
                f"❌ **Ошибка**\n\n"
                f"Произошла ошибка при обработке команды: {e}",
                parse_mode='Markdown'
            )

    @dp.message(Command("confirm_delete"))
    async def confirm_delete_command(message: types.Message):
        """Подтверждение удаления аккаунта по команде"""
        user_id = message.from_user.id

        # Только для администратора
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return

        try:
            # Получаем номер телефона из команды
            command_parts = message.text.split()
            if len(command_parts) != 2:
                await message.answer(
                    "❌ **Неверный формат команды**\n\n"
                    "Используйте: `/confirm_delete ************`",
                    parse_mode='Markdown'
                )
                return

            phone = command_parts[1].strip()

            # Показываем прогресс
            progress_msg = await message.answer(
                f"🗑️ **Удаление аккаунта {phone}**\n\n"
                f"⏳ Выполняется удаление...\n"
                f"Пожалуйста, подождите.",
                parse_mode='Markdown'
            )

            # Выполняем удаление
            result = await telethon_manager.delete_account(phone)

            # Формируем отчет
            if result['success']:
                status_emoji = "✅"
                status_text = "успешно удален"
            else:
                status_emoji = "❌"
                status_text = "не удален (ошибки)"

            report_text = (
                f"{status_emoji} **Аккаунт {status_text}**\n\n"
                f"📞 Номер: {result.get('phone', phone)}\n\n"
            )

            if result['steps_completed']:
                report_text += "✅ **Выполненные шаги:**\n"
                for step in result['steps_completed']:
                    report_text += f"• {step}\n"
                report_text += "\n"

            if result['warnings']:
                report_text += "⚠️ **Предупреждения:**\n"
                for warning in result['warnings']:
                    report_text += f"• {warning}\n"
                report_text += "\n"

            if result['errors']:
                report_text += "❌ **Ошибки:**\n"
                for error in result['errors']:
                    report_text += f"• {error}\n"
                report_text += "\n"

            report_text += f"🕐 Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            await progress_msg.edit_text(report_text, parse_mode='Markdown')

            logger.info(f"Администратор {user_id} удалил аккаунт {phone} через команду")

        except Exception as e:
            logger.error(f"Ошибка подтверждения удаления: {e}")
            await message.answer(
                f"❌ **Ошибка**\n\n"
                f"Произошла ошибка при удалении: {e}",
                parse_mode='Markdown'
            )

"""
Обработчики для управления аккаунтами
"""

import asyncio
from datetime import datetime
from aiogram import Dispatcher, types
from aiogram.fsm.context import FSMContext
from ...config.environment import env_config
from ..keyboards import get_keyboard, accounts_management_keyboard, account_deletion_confirmation_keyboard, account_details_keyboard
from ..states import ConnectAccount, AccountManagement
from ...utils.logging import setup_logging

logger = setup_logging(__name__)

def _generate_accounts_list_text_and_keyboard(accounts: list, user_id: int, user_role: str = None, include_timestamp: bool = False):
    """
    Вспомогательная функция для генерации текста и клавиатуры списка аккаунтов

    Args:
        accounts: Список аккаунтов
        user_id: ID пользователя
        user_role: Роль пользователя (admin, teamlead, none)
        include_timestamp: Включать ли временную метку (для обновлений)

    Returns:
        tuple: (text, keyboard)
    """
    current_time = datetime.now().strftime('%H:%M:%S') if include_timestamp else None

    # Определяем роль если не передана
    if user_role is None:
        from src.config.roles_manager import roles_manager
        user_role = roles_manager.get_user_role(user_id)

    if not accounts:
        if user_role == "admin":
            text = "📱 **Управление аккаунтами**\n\n❌ В системе нет подключенных аккаунтов"
        else:  # teamlead
            text = "📱 **Мои аккаунты**\n\n❌ Вам не назначены аккаунты\n\nОбратитесь к администратору для назначения аккаунтов."

        if current_time:
            text += f"\n\n🕐 Обновлено: {current_time}"
        keyboard = accounts_management_keyboard([], user_id, user_role)
    else:
        if user_role == "admin":
            text = f"📱 **Управление аккаунтами**\n\n📊 Всего аккаунтов: {len(accounts)}\n\n"
            text += "Выберите аккаунт для управления:"
        else:  # teamlead
            text = f"📱 **Мои назначенные аккаунты**\n\n📊 Назначено вам: {len(accounts)}\n\n"
            text += "Выберите аккаунт для просмотра:"

        if current_time:
            text += f"\n\n🕐 Обновлено: {current_time}"

        # Преобразуем формат для совместимости с клавиатурой
        accounts_for_keyboard = []
        for acc in accounts:
            accounts_for_keyboard.append({
                'user_id': acc['phone'],  # Используем phone как ID
                'phone': acc['phone'],
                'monitoring': acc['monitoring']
            })
        keyboard = accounts_management_keyboard(accounts_for_keyboard, user_id)

    return text, keyboard

def register_account_handlers(dp: Dispatcher, telethon_manager, alert_manager):
    """Регистрация обработчиков аккаунтов"""
    
    @dp.message(lambda m: m.text == "Подключить аккаунт")
    async def connect_account(message: types.Message, state: FSMContext):
        user_id = message.from_user.id

        # Проверяем доступ пользователя (только администраторы могут подключать аккаунты)
        from src.config.roles_manager import roles_manager
        if not roles_manager.is_admin(user_id):
            await message.answer("❌ Только администраторы могут подключать новые аккаунты")
            return

        # Импортируем клавиатуру для отмены ввода номера
        from ..keyboards import cancel_phone_input_keyboard

        await message.answer(
            "📱 Пожалуйста, введите номер телефона в международном формате:\n"
            "Пример: +***********",
            reply_markup=cancel_phone_input_keyboard()
        )
        await state.set_state(ConnectAccount.waiting_for_phone)

    @dp.message(ConnectAccount.waiting_for_phone)
    async def process_phone(message: types.Message, state: FSMContext):
        phone = message.text.strip()
        user_id = message.from_user.id
        
        # Простая валидация номера
        if not phone.startswith('+') or len(phone) < 10:
            await message.answer("❌ Неверный формат номера. Используйте международный формат: +***********")
            return
        
        await state.update_data(phone=phone)
        
        try:
            await telethon_manager.start_auth(user_id, phone)

            # Импортируем клавиатуру для отмены
            from ..keyboards import cancel_account_connection_keyboard

            await message.answer(
                f"📨 **Код подтверждения отправлен**\n\n"
                f"Номер: {phone}\n"
                f"Введите полученный код:\n\n"
                f"💡 **Если код не приходит:**\n"
                f"• Нажмите \"🔄 Переавторизация\" для повторной отправки\n"
                f"• Или \"❌ Отмена\" для выхода",
                parse_mode='Markdown',
                reply_markup=cancel_account_connection_keyboard()
            )
            await state.set_state(ConnectAccount.waiting_for_code)
            logger.info(f"Пользователь {user_id} запросил код для номера {phone}")
        except Exception as e:
            await message.answer(f"❌ Ошибка при отправке кода: {e}")
            logger.error(f"Ошибка start_auth для {user_id}: {e}")
            await state.clear()

    @dp.message(ConnectAccount.waiting_for_code)
    async def process_code(message: types.Message, state: FSMContext):
        # Проверяем, что сообщение содержит текст
        if not message.text:
            return

        code = message.text.strip()
        data = await state.get_data()
        phone = data.get('phone')
        user_id = message.from_user.id
        
        try:
            # Создание callback для алертов с детальным логированием
            async def alert_callback(alert_text):
                try:
                    stats = await alert_manager.send_alert_with_stats(user_id, alert_text)
                    logger.info(f"Статистика отправки алерта для пользователя {user_id}: "
                              f"пользователь={stats['user_sent']}, "
                              f"админы={stats['admins_sent']}/{stats['total_admins']}, "
                              f"ошибок={stats['admins_failed']}")
                except Exception as e:
                    logger.error(f"Критическая ошибка в alert_callback для пользователя {user_id}: {e}")
                    # Fallback на старый метод
                    try:
                        await alert_manager.send_alert_to_all(user_id, alert_text)
                    except Exception as fallback_error:
                        logger.error(f"Fallback alert также не удался для пользователя {user_id}: {fallback_error}")
            
            await telethon_manager.complete_auth(user_id, phone, code, alert_callback)
            
            success_message = (
                f"✅ Аккаунт {phone} успешно подключён!\n"
                f"🔍 Мониторинг исходящих сообщений запущен\n"
                f"📸 Мониторинг входящих фотографий запущен\n"
                f"⚠️ При обнаружении стоп-слов вы получите уведомление\n"
                f"📷 При получении фотографий они будут пересланы как алерты"
            )
            
            await message.answer(success_message, reply_markup=get_keyboard(user_id))
            logger.info(f"Пользователь {user_id} успешно подключил аккаунт {phone}")
            
            # Уведомление всех администраторов о новом подключении
            admin_notification = (
                f"📱 Новое подключение аккаунта\n"
                f"👤 Пользователь: {user_id}\n"
                f"📞 Номер: {phone}\n"
                f"🕐 Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            await alert_manager.send_admin_notification(admin_notification)
                
        except Exception as e:
            await message.answer(f"❌ Ошибка при подключении: {e}")
            logger.error(f"Ошибка complete_auth для {user_id}: {e}")
        
        await state.clear()

    @dp.callback_query(lambda c: c.data == "cancel_phone_input")
    async def cancel_phone_input(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик отмены ввода номера телефона"""
        await callback.answer()

        try:
            user_id = callback.from_user.id

            # Очищаем состояние FSM
            await state.clear()

            # Возвращаем пользователя в главное меню
            await callback.message.edit_text(
                "❌ **Ввод номера телефона отменен**\n\n"
                "Процесс подключения аккаунта был отменен.\n"
                "Вы можете попробовать подключить аккаунт снова позже.",
                parse_mode='Markdown'
            )

            # Отправляем новое сообщение с главным меню
            await callback.message.answer(
                "🏠 Вы вернулись в главное меню",
                reply_markup=get_keyboard(user_id)
            )

            logger.info(f"Пользователь {user_id} отменил ввод номера телефона")

        except Exception as e:
            logger.error(f"Ошибка при отмене ввода номера телефона: {e}")
            await callback.message.edit_text(
                "❌ Ошибка при отмене ввода номера телефона"
            )

    @dp.callback_query(lambda c: c.data == "cancel_account_connection")
    async def cancel_account_connection(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик отмены подключения аккаунта"""
        await callback.answer()

        try:
            user_id = callback.from_user.id

            # Получаем данные из состояния для логирования
            data = await state.get_data()
            phone = data.get('phone', 'неизвестный')

            # Очищаем состояние FSM
            await state.clear()

            # Пытаемся очистить клиент из TelethonManager если он был создан
            try:
                if user_id in telethon_manager.clients:
                    client_data = telethon_manager.clients[user_id]
                    if 'client' in client_data:
                        # Отключаем с таймаутом
                        await asyncio.wait_for(client_data['client'].disconnect(), timeout=5.0)
                    del telethon_manager.clients[user_id]
                    logger.info(f"Очищен клиент для пользователя {user_id}")
            except asyncio.TimeoutError:
                logger.warning(f"Таймаут при очистке клиента для {user_id}")
                # Удаляем из памяти в любом случае
                if user_id in telethon_manager.clients:
                    del telethon_manager.clients[user_id]
            except Exception as cleanup_error:
                logger.warning(f"Ошибка при очистке клиента для {user_id}: {cleanup_error}")
                # Удаляем из памяти в любом случае
                if user_id in telethon_manager.clients:
                    del telethon_manager.clients[user_id]

            # Возвращаем пользователя в главное меню
            await callback.message.edit_text(
                "❌ **Подключение аккаунта отменено**\n\n"
                f"Процесс подключения номера {phone} был отменен.\n"
                "Вы можете попробовать подключить аккаунт снова позже.",
                parse_mode='Markdown'
            )

            # Отправляем новое сообщение с главным меню
            await callback.message.answer(
                "🏠 Вы вернулись в главное меню",
                reply_markup=get_keyboard(user_id)
            )

            logger.info(f"Пользователь {user_id} отменил подключение аккаунта {phone}")

        except Exception as e:
            logger.error(f"Ошибка при отмене подключения аккаунта: {e}")
            await callback.message.edit_text(
                "❌ Ошибка при отмене подключения аккаунта"
            )

    @dp.callback_query(lambda c: c.data == "force_reauth_account")
    async def force_reauth_account(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик принудительной переавторизации аккаунта"""
        await callback.answer()

        try:
            user_id = callback.from_user.id

            # Получаем данные из состояния
            data = await state.get_data()
            phone = data.get('phone')

            if not phone:
                await callback.message.edit_text(
                    "❌ **Ошибка переавторизации**\n\n"
                    "Номер телефона не найден. Начните процесс подключения заново.",
                    parse_mode='Markdown'
                )
                await state.clear()
                return

            # Очищаем старую сессию
            await callback.message.edit_text(
                "🔄 **Переавторизация аккаунта**\n\n"
                f"Очищаем старую сессию для номера {phone}...",
                parse_mode='Markdown'
            )

            # Очистка сессии в TelethonManager
            clear_result = await telethon_manager.clear_user_session(user_id)

            if not clear_result:
                await callback.message.edit_text(
                    "❌ **Ошибка очистки сессии**\n\n"
                    "Не удалось очистить старую сессию. Попробуйте позже.",
                    parse_mode='Markdown'
                )
                return

            # Запускаем новую авторизацию с принудительной переавторизацией
            try:
                await telethon_manager.start_auth(user_id, phone, force_reauth=True)

                # Импортируем клавиатуру для отмены
                from ..keyboards import cancel_account_connection_keyboard

                await callback.message.edit_text(
                    f"📨 **Новый код отправлен!**\n\n"
                    f"Код подтверждения отправлен на номер {phone}\n"
                    f"Введите полученный код:\n\n"
                    f"💡 Если код снова не приходит, попробуйте еще раз через несколько минут",
                    parse_mode='Markdown',
                    reply_markup=cancel_account_connection_keyboard()
                )

                # Остаемся в том же состоянии ожидания кода
                logger.info(f"Пользователь {user_id} выполнил переавторизацию для номера {phone}")

            except Exception as auth_error:
                await callback.message.edit_text(
                    f"❌ **Ошибка при переавторизации**\n\n"
                    f"Не удалось отправить новый код: {auth_error}\n\n"
                    f"Попробуйте подключить аккаунт заново.",
                    parse_mode='Markdown'
                )
                await state.clear()
                logger.error(f"Ошибка переавторизации для {user_id}: {auth_error}")

        except Exception as e:
            logger.error(f"Ошибка при принудительной переавторизации: {e}")
            await callback.message.edit_text(
                "❌ Ошибка при переавторизации аккаунта"
            )

    @dp.message(lambda m: m.text == "Мои аккаунты")
    async def my_accounts(message: types.Message):
        user_id = message.from_user.id

        # Проверяем доступ пользователя
        from src.config.roles_manager import roles_manager
        user_role = roles_manager.get_user_role(user_id)

        if user_role == "none":
            await message.answer("❌ У вас нет доступа к этой функции")
            return

        accounts = telethon_manager.get_connected_accounts()

        # Для тимлидов показываем только назначенные им аккаунты
        if user_role == "teamlead":
            from src.config.teamleads_manager import teamleads_manager

            # Находим username тимлида по user_id
            teamlead_username = None
            all_teamleads = teamleads_manager.get_all_teamleads()
            for username, data in all_teamleads.items():
                if data.get('user_id') == user_id:
                    teamlead_username = username
                    break

            if teamlead_username:
                # Фильтруем аккаунты - показываем только назначенные
                assigned_phones = teamleads_manager.get_assigned_phones_for_teamlead(teamlead_username)
                accounts = [acc for acc in accounts if acc['phone'] in assigned_phones]
            else:
                accounts = []  # Тимлид не найден в системе

        # Создаем интерактивный интерфейс управления аккаунтами
        text, keyboard = _generate_accounts_list_text_and_keyboard(accounts, user_id, user_role)
        await message.answer(text, reply_markup=keyboard, parse_mode='Markdown')

    @dp.callback_query(lambda c: c.data == "refresh_accounts_list")
    async def refresh_accounts_list(callback: types.CallbackQuery):
        """Обработчик обновления списка аккаунтов"""
        await callback.answer("🔄 Обновление списка...")

        try:
            user_id = callback.from_user.id
            accounts = telethon_manager.get_connected_accounts()

            # Генерируем текст и клавиатуру с временной меткой
            text, keyboard = _generate_accounts_list_text_and_keyboard(accounts, user_id, include_timestamp=True)

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode='Markdown')
            logger.info(f"Пользователь {user_id} обновил список аккаунтов")

        except Exception as e:
            logger.error(f"Ошибка обновления списка аккаунтов: {e}")
            # Если ошибка связана с тем, что сообщение не изменилось, просто уведомляем пользователя
            if "message is not modified" in str(e).lower():
                await callback.answer("✅ Список аккаунтов уже актуален", show_alert=True)
            else:
                await callback.message.edit_text("❌ Ошибка при обновлении списка аккаунтов")

    @dp.callback_query(lambda c: c.data and c.data.startswith("delete_account:"))
    async def delete_account_request(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик запроса на удаление аккаунта"""
        logger.info(f"Получен запрос на удаление аккаунта: callback_data='{callback.data}', user_id={callback.from_user.id}")
        await callback.answer()

        try:
            # Извлекаем номер телефона аккаунта для удаления
            phone = callback.data.split(":")[1]
            current_user_id = callback.from_user.id

            # Проверяем права доступа (все администраторы могут удалять любые аккаунты)
            if not env_config.is_admin(current_user_id):
                await callback.message.edit_text(
                    "❌ **Доступ запрещен**\n\n"
                    "У вас нет прав для удаления аккаунтов.\n"
                    "Удалять аккаунты могут только администраторы.",
                    parse_mode='Markdown'
                )
                return

            # Получаем информацию об аккаунте
            accounts = telethon_manager.get_connected_accounts()
            target_account = None
            for acc in accounts:
                if acc['phone'] == phone:
                    target_account = acc
                    break

            if not target_account:
                await callback.message.edit_text(
                    "❌ **Аккаунт не найден**\n\n"
                    "Указанный аккаунт не найден в системе.",
                    parse_mode='Markdown'
                )
                return

            status = "🟢 Активен" if target_account['monitoring'] else "🔴 Неактивен"

            # Сохраняем данные в состояние
            await state.update_data(
                phone=phone
            )

            # Показываем подтверждение
            text = (
                f"🗑️ **Подтверждение удаления аккаунта**\n\n"
                f"⚠️ **ВНИМАНИЕ!** Это действие необратимо.\n\n"
                f"**Аккаунт для удаления:**\n"
                f"📞 Номер: {phone}\n"
                f"📊 Статус: {status}\n\n"
                f"**Что будет удалено:**\n"
                f"• Остановлен мониторинг\n"
                f"• Отключен Telethon клиент\n"
                f"• Удален файл сессии\n"
                f"• Удалена запись из метаданных\n\n"
                f"Вы уверены, что хотите удалить этот аккаунт?"
            )

            keyboard = account_deletion_confirmation_keyboard(phone, phone)
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode='Markdown')
            await state.set_state(AccountManagement.confirming_account_deletion)

            logger.info(f"Пользователь {current_user_id} запросил удаление аккаунта {phone}")

        except (ValueError, IndexError) as e:
            logger.error(f"Ошибка парсинга callback_data '{callback.data}': {e}")
            await callback.message.edit_text("❌ Некорректные данные аккаунта")
        except Exception as e:
            logger.error(f"Ошибка при запросе удаления аккаунта: {e}")
            await callback.message.edit_text("❌ Ошибка при обработке запроса на удаление")

    @dp.callback_query(lambda c: c.data and c.data.startswith("confirm_delete_account:"))
    async def confirm_delete_account(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик подтверждения удаления аккаунта"""
        logger.info(f"Получено подтверждение удаления аккаунта: callback_data='{callback.data}', user_id={callback.from_user.id}")
        await callback.answer()

        try:
            # Извлекаем номер телефона аккаунта
            phone = callback.data.split(":")[1]
            current_user_id = callback.from_user.id

            # Получаем данные из состояния
            data = await state.get_data()
            stored_phone = data.get('phone', 'Неизвестно')

            # Повторная проверка прав доступа (только администраторы)
            if not env_config.is_admin(current_user_id):
                await callback.message.edit_text("❌ Доступ запрещен")
                await state.clear()
                return

            # Показываем процесс удаления
            await callback.message.edit_text(
                f"🗑️ **Удаление аккаунта**\n\n"
                f"📞 Аккаунт: {phone}\n"
                f"⏳ Выполняется удаление...\n\n"
                f"Пожалуйста, подождите...",
                parse_mode='Markdown'
            )

            await state.set_state(AccountManagement.deleting_account)

            # Выполняем удаление
            result = await telethon_manager.delete_account(phone)

            # Формируем отчет об удалении
            if result['success']:
                status_emoji = "✅"
                status_text = "успешно удален"
            else:
                status_emoji = "⚠️"
                status_text = "удален с ошибками"

            report_text = (
                f"{status_emoji} **Аккаунт {status_text}**\n\n"
                f"📞 Номер: {result.get('phone', phone)}\n\n"
            )

            # Добавляем выполненные шаги
            if result['steps_completed']:
                report_text += "**Выполненные операции:**\n"
                for step in result['steps_completed']:
                    report_text += f"✅ {step}\n"
                report_text += "\n"

            # Добавляем предупреждения
            if result['warnings']:
                report_text += "**Предупреждения:**\n"
                for warning in result['warnings']:
                    report_text += f"⚠️ {warning}\n"
                report_text += "\n"

            # Добавляем ошибки
            if result['errors']:
                report_text += "**Ошибки:**\n"
                for error in result['errors']:
                    report_text += f"❌ {error}\n"
                report_text += "\n"

            # Кнопка возврата к списку
            from ..keyboards import InlineKeyboardMarkup, InlineKeyboardButton
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(
                    text="🔙 Назад к списку аккаунтов",
                    callback_data="back_to_accounts_list"
                )]
            ])

            await callback.message.edit_text(report_text, reply_markup=keyboard, parse_mode='Markdown')

            # Уведомляем администраторов об удалении
            try:
                admin_notification = (
                    f"🗑️ **Удаление аккаунта**\n\n"
                    f"👤 Инициатор: {current_user_id}\n"
                    f"📞 Удаленный аккаунт: {phone}\n"
                    f"📊 Статус: {status_text}\n"
                    f"🕐 Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
                await alert_manager.send_admin_notification(admin_notification)
                logger.info(f"Уведомление администраторов отправлено об удалении аккаунта {phone}")
            except Exception as e:
                logger.error(f"Ошибка отправки уведомления администраторам: {e}")

            logger.info(f"Пользователь {current_user_id} удалил аккаунт {phone}")
            await state.clear()

        except ValueError:
            await callback.message.edit_text("❌ Некорректный ID аккаунта")
            await state.clear()
        except Exception as e:
            logger.error(f"Ошибка при удалении аккаунта: {e}")
            await callback.message.edit_text(
                f"❌ **Ошибка удаления**\n\n"
                f"Произошла ошибка при удалении аккаунта.\n"
                f"Обратитесь к администратору.\n\n"
                f"Ошибка: {str(e)[:100]}...",
                parse_mode='Markdown'
            )
            await state.clear()

    @dp.callback_query(lambda c: c.data == "cancel_delete_account")
    async def cancel_delete_account(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик отмены удаления аккаунта"""
        await callback.answer("❌ Удаление отменено")

        try:
            await state.clear()

            # Возвращаемся к списку аккаунтов
            user_id = callback.from_user.id
            accounts = telethon_manager.get_connected_accounts()

            text, keyboard = _generate_accounts_list_text_and_keyboard(accounts, user_id)

            try:
                await callback.message.edit_text(text, reply_markup=keyboard, parse_mode='Markdown')
            except Exception as edit_error:
                # Если ошибка связана с тем, что сообщение не изменилось, игнорируем её
                if "message is not modified" not in str(edit_error).lower():
                    raise edit_error

            logger.info(f"Пользователь {user_id} отменил удаление аккаунта")

        except Exception as e:
            logger.error(f"Ошибка при отмене удаления аккаунта: {e}")
            await callback.message.edit_text("❌ Ошибка при отмене операции")

    @dp.callback_query(lambda c: c.data == "back_to_accounts_list")
    async def back_to_accounts_list(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик возврата к списку аккаунтов"""
        await callback.answer()

        try:
            await state.clear()

            user_id = callback.from_user.id
            accounts = telethon_manager.get_connected_accounts()

            text, keyboard = _generate_accounts_list_text_and_keyboard(accounts, user_id)

            try:
                await callback.message.edit_text(text, reply_markup=keyboard, parse_mode='Markdown')
            except Exception as edit_error:
                # Если ошибка связана с тем, что сообщение не изменилось, игнорируем её
                if "message is not modified" not in str(edit_error).lower():
                    raise edit_error

        except Exception as e:
            logger.error(f"Ошибка при возврате к списку аккаунтов: {e}")
            await callback.message.edit_text("❌ Ошибка при загрузке списка аккаунтов")

    @dp.callback_query(lambda c: c.data.startswith("account_details:"))
    async def account_details(callback: types.CallbackQuery):
        """Обработчик просмотра деталей аккаунта"""
        await callback.answer()

        try:
            phone = callback.data.split(":")[1]
            current_user_id = callback.from_user.id

            # Получаем информацию об аккаунте
            accounts = telethon_manager.get_connected_accounts()
            target_account = None
            for acc in accounts:
                if acc['phone'] == phone:
                    target_account = acc
                    break

            if not target_account:
                await callback.message.edit_text("❌ Аккаунт не найден")
                return
            monitoring = target_account['monitoring']
            authorized = target_account.get('authorized', True)
            needs_reauth = target_account.get('needs_reauth', False)

            if not authorized or needs_reauth:
                status = "⚠️ Требует авторизации"
                auth_status = "❌ Не авторизован"
            elif monitoring:
                status = "🟢 Активен"
                auth_status = "✅ Авторизован"
            else:
                status = "🔴 Неактивен"
                auth_status = "✅ Авторизован"

            # Формируем детальную информацию
            text = (
                f"📱 **Детали аккаунта**\n\n"
                f"📞 **Номер телефона:** {phone}\n"
                f"🔐 **Статус авторизации:** {auth_status}\n"
                f"📊 **Статус мониторинга:** {status}\n"
            )

            if authorized and not needs_reauth:
                text += (
                    f"📸 **Мониторинг фото:** {'✅ Включен' if monitoring else '❌ Отключен'}\n"
                    f"⚠️ **Мониторинг стоп-слов:** {'✅ Включен' if monitoring else '❌ Отключен'}\n\n"
                )
            else:
                text += (
                    f"📸 **Мониторинг фото:** ❌ Недоступен (требует авторизации)\n"
                    f"⚠️ **Мониторинг стоп-слов:** ❌ Недоступен (требует авторизации)\n\n"
                    f"💡 **Рекомендация:** Удалите аккаунт и подключите заново\n\n"
                )

            # Добавляем информацию о правах доступа
            if env_config.is_admin(current_user_id):
                text += "🔑 **Ваши права:** Администратор (полный доступ)\n"
                text += "ℹ️ **Примечание:** Все аккаунты теперь общие для всех администраторов\n"
            else:
                text += "🔑 **Ваши права:** Только просмотр\n"

            keyboard = account_details_keyboard(phone, current_user_id)
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode='Markdown')

        except (ValueError, IndexError):
            await callback.message.edit_text("❌ Некорректные данные аккаунта")
        except Exception as e:
            logger.error(f"Ошибка при просмотре деталей аккаунта: {e}")
            await callback.message.edit_text("❌ Ошибка при загрузке деталей аккаунта")

    @dp.callback_query(lambda c: c.data.startswith("account_info:"))
    async def account_info(callback: types.CallbackQuery):
        """Обработчик информации об аккаунте (неактивная кнопка)"""
        await callback.answer("ℹ️ Это информационная кнопка", show_alert=False)

    @dp.callback_query(lambda c: c.data == "separator")
    async def separator_handler(callback: types.CallbackQuery):
        """Обработчик разделителя (неактивная кнопка)"""
        await callback.answer()

    @dp.callback_query(lambda c: c.data == "add_new_account")
    async def add_new_account(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик добавления аккаунта"""
        await callback.answer()

        try:
            # Импортируем клавиатуру для отмены ввода номера
            from ..keyboards import cancel_phone_input_keyboard

            await callback.message.edit_text(
                "📱 **Подключение аккаунта**\n\n"
                "Пожалуйста, введите номер телефона в международном формате:\n"
                "Пример: +***********",
                reply_markup=cancel_phone_input_keyboard(),
                parse_mode='Markdown'
            )
            await state.set_state(ConnectAccount.waiting_for_phone)

        except Exception as e:
            logger.error(f"Ошибка при инициации добавления аккаунта: {e}")
            await callback.message.edit_text("❌ Ошибка при инициации подключения аккаунта")



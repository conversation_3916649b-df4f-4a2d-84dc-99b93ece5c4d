#!/usr/bin/env python3
"""
Скрипт для восстановления метаданных аккаунтов из backup файла
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.settings import ACCOUNTS_METADATA_FILE, SESSIONS_DIR, DATA_DIR
from src.config.environment import env_config
from telethon import TelegramClient

async def restore_from_backup():
    """Восстановление метаданных из backup файла"""
    print("🔍 Восстановление метаданных из backup...")
    print("=" * 50)
    
    # Ищем backup файлы
    backup_files = [
        DATA_DIR / 'accounts_metadata.backup',
        DATA_DIR / 'accounts_metadata.json.backup',
        DATA_DIR / 'accounts_metadata.json.backup_restore'
    ]
    
    backup_data = None
    used_backup = None
    
    for backup_file in backup_files:
        if backup_file.exists():
            try:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if data:  # Проверяем что файл не пустой
                    backup_data = data
                    used_backup = backup_file
                    print(f"📁 Используем backup: {backup_file}")
                    break
            except Exception as e:
                print(f"❌ Ошибка чтения {backup_file}: {e}")
    
    if not backup_data:
        print("❌ Не найдено подходящих backup файлов с данными")
        return
    
    print(f"📄 Найдено {len(backup_data)} записей в backup")
    
    # Проверяем существование директории сессий
    if not SESSIONS_DIR.exists():
        print("❌ Директория сессий не найдена")
        return
    
    # Получаем список файлов сессий
    session_files = list(SESSIONS_DIR.glob('*.session'))
    print(f"📁 Найдено {len(session_files)} файлов сессий")
    
    if not session_files:
        print("❌ Файлы сессий не найдены")
        return
    
    # Создаем словарь файлов сессий по user_id и проверяем их содержимое
    session_map = {}
    session_phone_map = {}  # Карта номер телефона -> файл сессии

    for session_file in session_files:
        try:
            stem = session_file.stem
            if stem.startswith('account_'):
                user_id = stem.replace('account_', '')
            else:
                user_id = stem
            session_map[user_id] = session_file.name

            # Пытаемся получить номер телефона из сессии
            try:
                client = TelegramClient(str(session_file), env_config.API_ID, env_config.API_HASH)
                await client.connect()
                if await client.is_user_authorized():
                    me = await client.get_me()
                    if me.phone:
                        session_phone_map[me.phone] = session_file.name
                        print(f"   📞 Сессия {session_file.name}: телефон {me.phone}")
                await client.disconnect()
            except Exception as e:
                print(f"   ⚠️ Не удалось проверить сессию {session_file.name}: {e}")

        except Exception as e:
            print(f"❌ Ошибка обработки файла сессии {session_file.name}: {e}")

    print(f"📋 Обработано файлов сессий: {len(session_map)}")
    print(f"📞 Найдено номеров телефонов в сессиях: {len(session_phone_map)}")
    for user_id, session_file in session_map.items():
        print(f"   - User {user_id}: {session_file}")
    
    # Восстанавливаем метаданные
    new_metadata = {}
    restored_count = 0
    
    for key, account_data in backup_data.items():
        try:
            # Определяем user_id и phone
            if key.startswith('+'):
                # Новый формат: ключ - номер телефона
                phone = key
                # Пытаемся найти user_id из session_file
                session_file = account_data.get('session_file', '')
                if session_file.startswith('account_'):
                    user_id = session_file.replace('account_', '').replace('.session', '')
                else:
                    user_id = session_file.replace('.session', '')
            else:
                # Старый формат: ключ - user_id
                user_id = key
                phone = account_data.get('phone', f"+{user_id}")
            
            print(f"\n🔍 Обработка аккаунта {user_id} ({phone})...")

            # Проверяем наличие файла сессии (сначала по user_id, потом по номеру телефона)
            session_file_name = None
            if user_id in session_map:
                session_file_name = session_map[user_id]
                print(f"📁 Найден файл сессии по user_id: {session_file_name}")
            elif phone in session_phone_map:
                session_file_name = session_phone_map[phone]
                print(f"📁 Найден файл сессии по номеру телефона: {session_file_name}")

            if session_file_name:
                session_path = SESSIONS_DIR / session_file_name
                
                # Проверяем авторизацию
                try:
                    client = TelegramClient(str(session_path), env_config.API_ID, env_config.API_HASH)
                    await client.connect()
                    
                    if await client.is_user_authorized():
                        # Получаем актуальную информацию о пользователе
                        me = await client.get_me()
                        actual_phone = me.phone if me.phone else phone
                        
                        new_metadata[actual_phone] = {
                            'session_file': session_file_name,
                            'monitoring': account_data.get('monitoring', False),
                            'last_updated': datetime.now().isoformat()
                        }
                        
                        print(f"✅ Аккаунт {actual_phone} (ID: {user_id}) восстановлен")
                        restored_count += 1
                    else:
                        print(f"⚠️ Аккаунт {user_id} не авторизован, добавляем с отключенным мониторингом")
                        new_metadata[phone] = {
                            'session_file': session_file_name,
                            'monitoring': False,
                            'last_updated': datetime.now().isoformat(),
                            'needs_reauth': True
                        }
                    
                    await client.disconnect()
                    
                except Exception as e:
                    print(f"❌ Ошибка проверки аккаунта {user_id}: {e}")
                    # Добавляем в метаданные даже если есть ошибка
                    new_metadata[phone] = {
                        'session_file': session_file_name,
                        'monitoring': False,
                        'last_updated': datetime.now().isoformat(),
                        'needs_reauth': True
                    }
            else:
                print(f"❌ Файл сессии для аккаунта {user_id} ({phone}) не найден")
                
        except Exception as e:
            print(f"❌ Ошибка обработки записи {key}: {e}")
    
    # Сохраняем восстановленные метаданные
    if new_metadata:
        # Создаем backup текущего файла
        if ACCOUNTS_METADATA_FILE.exists():
            backup_path = ACCOUNTS_METADATA_FILE.with_suffix('.json.backup_before_restore')
            ACCOUNTS_METADATA_FILE.rename(backup_path)
            print(f"📦 Создан backup текущего файла: {backup_path}")
        
        # Сохраняем новые метаданные
        with open(ACCOUNTS_METADATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(new_metadata, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Восстановлено {restored_count} авторизованных аккаунтов")
        print(f"📄 Всего записей в метаданных: {len(new_metadata)}")
        print(f"💾 Метаданные сохранены в {ACCOUNTS_METADATA_FILE}")
        
        # Показываем восстановленные аккаунты
        print("\n📋 Восстановленные аккаунты:")
        for phone, data in new_metadata.items():
            status = "✅ Авторизован" if not data.get('needs_reauth') else "⚠️ Требует повторной авторизации"
            monitoring = "🟢 Включен" if data['monitoring'] else "🔴 Отключен"
            print(f"   - {phone}: {status}, мониторинг: {monitoring}")
    else:
        print("\n❌ Не удалось восстановить ни одного аккаунта")

if __name__ == "__main__":
    asyncio.run(restore_from_backup())

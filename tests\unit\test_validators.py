"""
Юнит-тесты для валидаторов PM Searcher Bot

Покрывает все методы валидации с проверкой безопасности,
SQL-инъекций, XSS-атак и path traversal.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import patch, mock_open

from src.bot.validators import StopWordValidator, ValidationResult
from tests.fixtures.test_constants import (
    VALID_STOPWORDS, INVALID_STOPWORDS, MALICIOUS_INPUTS,
    VALID_IMPORT_FILES, INVALID_IMPORT_FILES, PERFORMANCE_LIMITS
)
from tests.utils.test_helpers import create_temp_file, create_temp_binary_file


class TestValidationResult:
    """Тесты класса ValidationResult"""
    
    def test_validation_result_creation(self):
        """Тест создания результата валидации"""
        result = ValidationResult(
            is_valid=True,
            errors=["error1"],
            warnings=["warning1"],
            normalized_data="normalized"
        )
        
        assert result.is_valid is True
        assert result.errors == ["error1"]
        assert result.warnings == ["warning1"]
        assert result.normalized_data == "normalized"
    
    def test_validation_result_defaults(self):
        """Тест значений по умолчанию"""
        result = ValidationResult(is_valid=False)
        
        assert result.is_valid is False
        assert result.errors == []
        assert result.warnings == []
        assert result.normalized_data is None
    
    def test_validation_result_string_representation(self):
        """Тест строкового представления"""
        result = ValidationResult(
            is_valid=True,
            errors=["error"],
            warnings=["warning"]
        )
        
        str_repr = str(result)
        assert "ValidationResult" in str_repr
        assert "is_valid=True" in str_repr


class TestStopWordValidatorInit:
    """Тесты инициализации StopWordValidator"""
    
    def test_validator_initialization(self):
        """Тест создания валидатора"""
        validator = StopWordValidator()
        
        assert hasattr(validator, 'MIN_WORD_LENGTH')
        assert hasattr(validator, 'MAX_WORD_LENGTH')
        assert hasattr(validator, 'ALLOWED_CHARACTERS')
        assert hasattr(validator, 'DANGEROUS_PATTERNS')
    
    def test_validator_constants(self):
        """Тест констант валидатора"""
        validator = StopWordValidator()
        
        assert validator.MIN_WORD_LENGTH == 2
        assert validator.MAX_WORD_LENGTH == 50
        assert validator.MAX_IMPORT_FILE_SIZE == 1024 * 1024
        assert validator.MAX_IMPORT_WORDS == 1000
        
        assert len(validator.DANGEROUS_PATTERNS) > 0
        assert validator.ALLOWED_CHARACTERS is not None


class TestValidateStopword:
    """Тесты метода validate_stopword()"""
    
    def test_validate_stopword_valid_words(self):
        """Тест валидации корректных стоп-слов"""
        validator = StopWordValidator()
        
        valid_words = ["спам", "реклама", "test", "тест123", "word-test", "word_test"]
        
        for word in valid_words:
            result = validator.validate_stopword(word)
            
            assert result.is_valid is True
            assert len(result.errors) == 0
            assert result.normalized_data == word.lower().strip()
    
    def test_validate_stopword_empty_string(self):
        """Тест валидации пустой строки"""
        validator = StopWordValidator()
        
        result = validator.validate_stopword("")
        
        assert result.is_valid is False
        assert "пустым" in " ".join(result.errors).lower()
    
    def test_validate_stopword_whitespace_only(self):
        """Тест валидации строки из пробелов"""
        validator = StopWordValidator()
        
        result = validator.validate_stopword("   \n\t   ")
        
        assert result.is_valid is False
        assert "пустым" in " ".join(result.errors).lower()
    
    def test_validate_stopword_too_short(self):
        """Тест валидации слишком короткого слова"""
        validator = StopWordValidator()
        
        result = validator.validate_stopword("a")
        
        assert result.is_valid is False
        assert "короткое" in " ".join(result.errors).lower()
    
    def test_validate_stopword_too_long(self):
        """Тест валидации слишком длинного слова"""
        validator = StopWordValidator()
        
        long_word = "a" * 51  # Больше MAX_WORD_LENGTH
        result = validator.validate_stopword(long_word)
        
        assert result.is_valid is False
        assert "длинное" in " ".join(result.errors).lower()
    
    def test_validate_stopword_invalid_characters(self):
        """Тест валидации недопустимых символов"""
        validator = StopWordValidator()
        
        invalid_words = [
            "слово с пробелами",
            "слово/с/слешами",
            "слово<с>тегами",
            "слово\"с\"кавычками",
            "слово;с;точкой;запятой"
        ]
        
        for word in invalid_words:
            result = validator.validate_stopword(word)
            
            assert result.is_valid is False
            assert "недопустимые символы" in " ".join(result.errors).lower()
    
    def test_validate_stopword_normalization(self):
        """Тест нормализации стоп-слов"""
        validator = StopWordValidator()
        
        test_cases = [
            ("  СПАМ  ", "спам"),
            ("\tРеклама\n", "реклама"),
            ("  Test_Word  ", "test_word")
        ]
        
        for input_word, expected in test_cases:
            result = validator.validate_stopword(input_word)
            
            assert result.is_valid is True
            assert result.normalized_data == expected
    
    def test_validate_stopword_warnings(self):
        """Тест предупреждений при валидации"""
        validator = StopWordValidator()
        
        # Короткое слово (но не слишком короткое)
        result = validator.validate_stopword("abc")
        assert result.is_valid is True
        assert any("короткие" in warning.lower() for warning in result.warnings)
        
        # Числовое слово
        result = validator.validate_stopword("123")
        assert result.is_valid is True
        assert any("числовые" in warning.lower() for warning in result.warnings)
    
    @pytest.mark.parametrize("valid_word", VALID_STOPWORDS)
    def test_validate_stopword_parametrized_valid(self, valid_word):
        """Параметризованный тест валидных стоп-слов"""
        validator = StopWordValidator()
        
        result = validator.validate_stopword(valid_word)
        
        assert result.is_valid is True
        assert result.normalized_data == valid_word.lower().strip()
    
    @pytest.mark.parametrize("invalid_word", INVALID_STOPWORDS)
    def test_validate_stopword_parametrized_invalid(self, invalid_word):
        """Параметризованный тест невалидных стоп-слов"""
        validator = StopWordValidator()
        
        result = validator.validate_stopword(invalid_word)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
    
    @pytest.mark.parametrize("malicious_input", MALICIOUS_INPUTS)
    def test_validate_stopword_security(self, malicious_input):
        """Параметризованный тест безопасности"""
        validator = StopWordValidator()
        
        result = validator.validate_stopword(malicious_input)
        
        # Вредоносные данные должны быть отклонены
        assert result.is_valid is False
        # Должна быть ошибка безопасности
        error_text = " ".join(result.errors).lower()
        assert any(keyword in error_text for keyword in ["опасное", "безопасность", "недопустимые"])


class TestCheckWordSafety:
    """Тесты метода check_word_safety()"""
    
    def test_check_word_safety_safe_words(self):
        """Тест проверки безопасных слов"""
        validator = StopWordValidator()
        
        safe_words = ["спам", "реклама", "test", "безопасное_слово", "word123"]
        
        for word in safe_words:
            result = validator.check_word_safety(word)
            assert result is True
    
    def test_check_word_safety_xss_attacks(self):
        """Тест обнаружения XSS-атак"""
        validator = StopWordValidator()
        
        xss_attacks = [
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')",
            "<svg onload=alert('xss')>"
        ]
        
        for attack in xss_attacks:
            result = validator.check_word_safety(attack)
            assert result is False
    
    def test_check_word_safety_sql_injection(self):
        """Тест обнаружения SQL-инъекций"""
        validator = StopWordValidator()
        
        sql_injections = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--",
            "1' UNION SELECT * FROM users--"
        ]
        
        for injection in sql_injections:
            result = validator.check_word_safety(injection)
            assert result is False
    
    def test_check_word_safety_path_traversal(self):
        """Тест обнаружения path traversal"""
        validator = StopWordValidator()
        
        path_traversals = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc//passwd"
        ]
        
        for traversal in path_traversals:
            result = validator.check_word_safety(traversal)
            assert result is False
    
    def test_check_word_safety_command_injection(self):
        """Тест обнаружения command injection"""
        validator = StopWordValidator()
        
        command_injections = [
            "; rm -rf /",
            "| cat /etc/passwd",
            "$(whoami)",
            "`id`",
            "&& echo vulnerable"
        ]
        
        for injection in command_injections:
            result = validator.check_word_safety(injection)
            assert result is False
    
    def test_check_word_safety_empty_input(self):
        """Тест проверки пустого ввода"""
        validator = StopWordValidator()
        
        result = validator.check_word_safety("")
        assert result is True  # Пустая строка считается безопасной
    
    def test_check_word_safety_none_input(self):
        """Тест проверки None ввода"""
        validator = StopWordValidator()

        result = validator.check_word_safety(None)
        assert result is True  # None считается безопасным


class TestValidateImportFile:
    """Тесты метода validate_import_file()"""

    def test_validate_import_file_valid_content(self):
        """Тест валидации корректного содержимого файла"""
        validator = StopWordValidator()

        valid_content = "# Комментарий\nспам\nреклама\nпродажа"

        result = validator.validate_import_file(valid_content)

        assert result.is_valid is True
        assert len(result.errors) == 0
        assert isinstance(result.normalized_data, list)
        assert "спам" in result.normalized_data
        assert "реклама" in result.normalized_data
        assert "продажа" in result.normalized_data

    def test_validate_import_file_empty_content(self):
        """Тест валидации пустого содержимого"""
        validator = StopWordValidator()

        result = validator.validate_import_file("")

        assert result.is_valid is False
        assert "пустой" in " ".join(result.errors).lower()

    def test_validate_import_file_only_comments(self):
        """Тест валидации файла только с комментариями"""
        validator = StopWordValidator()

        content = "# Только комментарии\n# Еще комментарий\n# И еще один"

        result = validator.validate_import_file(content)

        assert result.is_valid is False
        assert "стоп-слов" in " ".join(result.errors).lower()

    def test_validate_import_file_too_large(self):
        """Тест валидации слишком большого файла"""
        validator = StopWordValidator()

        # Создаем содержимое больше MAX_IMPORT_FILE_SIZE
        large_content = "слово\n" * 100000  # Примерно 600KB

        result = validator.validate_import_file(large_content)

        assert result.is_valid is False
        assert "большой" in " ".join(result.errors).lower()

    def test_validate_import_file_too_many_words(self):
        """Тест валидации файла со слишком большим количеством слов"""
        validator = StopWordValidator()

        # Создаем содержимое с количеством слов больше MAX_IMPORT_WORDS
        many_words = "\n".join([f"слово{i}" for i in range(1001)])

        result = validator.validate_import_file(many_words)

        assert result.is_valid is False
        assert "много" in " ".join(result.errors).lower()

    def test_validate_import_file_invalid_words(self):
        """Тест валидации файла с невалидными словами"""
        validator = StopWordValidator()

        content = "спам\nслово с пробелами\nреклама\nслово/с/слешами"

        result = validator.validate_import_file(content)

        assert result.is_valid is False
        assert "невалидные" in " ".join(result.errors).lower()

    def test_validate_import_file_mixed_valid_invalid(self):
        """Тест валидации файла со смешанным содержимым"""
        validator = StopWordValidator()

        content = "спам\nреклама\nслово с пробелами\nпродажа"

        result = validator.validate_import_file(content)

        assert result.is_valid is False
        # Должны быть указаны невалидные слова
        assert "слово с пробелами" in " ".join(result.errors)

    def test_validate_import_file_ignores_empty_lines(self):
        """Тест игнорирования пустых строк"""
        validator = StopWordValidator()

        content = "спам\n\n\nреклама\n\n\nпродажа\n\n"

        result = validator.validate_import_file(content)

        assert result.is_valid is True
        assert len(result.normalized_data) == 3

    def test_validate_import_file_strips_whitespace(self):
        """Тест удаления пробелов"""
        validator = StopWordValidator()

        content = "  спам  \n\t реклама \t\n   продажа   "

        result = validator.validate_import_file(content)

        assert result.is_valid is True
        assert "спам" in result.normalized_data
        assert "реклама" in result.normalized_data
        assert "продажа" in result.normalized_data

    def test_validate_import_file_removes_duplicates(self):
        """Тест удаления дубликатов"""
        validator = StopWordValidator()

        content = "спам\nреклама\nспам\nпродажа\nреклама"

        result = validator.validate_import_file(content)

        assert result.is_valid is True
        assert len(result.normalized_data) == 3
        assert result.normalized_data.count("спам") == 1
        assert result.normalized_data.count("реклама") == 1

    def test_validate_import_file_case_insensitive_duplicates(self):
        """Тест удаления дубликатов без учета регистра"""
        validator = StopWordValidator()

        content = "спам\nСПАМ\nСпам\nреклама\nРЕКЛАМА"

        result = validator.validate_import_file(content)

        assert result.is_valid is True
        assert len(result.normalized_data) == 2
        assert "спам" in result.normalized_data
        assert "реклама" in result.normalized_data

    @pytest.mark.parametrize("file_name,file_content", VALID_IMPORT_FILES.items())
    def test_validate_import_file_parametrized_valid(self, file_name, file_content):
        """Параметризованный тест валидных файлов"""
        validator = StopWordValidator()

        result = validator.validate_import_file(file_content)

        if file_name == "with_comments":
            # Файл только с комментариями должен быть невалидным
            assert result.is_valid is False
        else:
            assert result.is_valid is True

    @pytest.mark.parametrize("file_name,file_content", INVALID_IMPORT_FILES.items())
    def test_validate_import_file_parametrized_invalid(self, file_name, file_content):
        """Параметризованный тест невалидных файлов"""
        validator = StopWordValidator()

        if isinstance(file_content, bytes):
            # Бинарные данные должны вызвать исключение при декодировании
            with pytest.raises(UnicodeDecodeError):
                validator.validate_import_file(file_content.decode('utf-8'))
        else:
            result = validator.validate_import_file(file_content)
            assert result.is_valid is False

    def test_validate_import_file_security_check(self):
        """Тест проверки безопасности содержимого файла"""
        validator = StopWordValidator()

        malicious_content = """спам
<script>alert('xss')</script>
реклама
'; DROP TABLE users; --
продажа"""

        result = validator.validate_import_file(malicious_content)

        assert result.is_valid is False
        # Должны быть указаны опасные слова
        error_text = " ".join(result.errors)
        assert "<script>" in error_text or "DROP TABLE" in error_text


class TestValidateExportFilename:
    """Тесты метода validate_export_filename()"""

    def test_validate_export_filename_valid_names(self):
        """Тест валидации корректных имен файлов"""
        validator = StopWordValidator()

        valid_names = [
            "stopwords",
            "my_stopwords",
            "stopwords-backup",
            "export_2023",
            "файл_стоп_слов"
        ]

        for name in valid_names:
            result = validator.validate_export_filename(name)

            assert result.is_valid is True
            assert result.normalized_data.endswith('.txt')

    def test_validate_export_filename_empty_name(self):
        """Тест валидации пустого имени файла"""
        validator = StopWordValidator()

        result = validator.validate_export_filename("")

        assert result.is_valid is False
        assert "пустым" in " ".join(result.errors).lower()

    def test_validate_export_filename_too_long(self):
        """Тест валидации слишком длинного имени"""
        validator = StopWordValidator()

        long_name = "a" * 256  # Слишком длинное имя

        result = validator.validate_export_filename(long_name)

        assert result.is_valid is False
        assert "длинное" in " ".join(result.errors).lower()

    def test_validate_export_filename_dangerous_characters(self):
        """Тест валидации опасных символов в имени файла"""
        validator = StopWordValidator()

        dangerous_names = [
            "file<name",
            "file>name",
            "file:name",
            'file"name',
            "file|name",
            "file?name",
            "file*name",
            "file/name",
            "file\\name"
        ]

        for name in dangerous_names:
            result = validator.validate_export_filename(name)

            assert result.is_valid is False
            assert "недопустимый символ" in " ".join(result.errors).lower()

    def test_validate_export_filename_path_traversal(self):
        """Тест обнаружения path traversal в имени файла"""
        validator = StopWordValidator()

        traversal_names = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32",
            "....//....//file"
        ]

        for name in traversal_names:
            result = validator.validate_export_filename(name)

            assert result.is_valid is False
            assert "каталогам" in " ".join(result.errors).lower()

    def test_validate_export_filename_auto_extension(self):
        """Тест автоматического добавления расширения"""
        validator = StopWordValidator()

        result = validator.validate_export_filename("stopwords")

        assert result.is_valid is True
        assert result.normalized_data == "stopwords.txt"
        assert any("автоматически добавлено" in warning.lower() for warning in result.warnings)

    def test_validate_export_filename_existing_extension(self):
        """Тест с уже существующим расширением"""
        validator = StopWordValidator()

        result = validator.validate_export_filename("stopwords.txt")

        assert result.is_valid is True
        assert result.normalized_data == "stopwords.txt"
        # Не должно быть предупреждения о добавлении расширения
        assert not any("автоматически добавлено" in warning.lower() for warning in result.warnings)

    def test_validate_export_filename_hidden_file_warning(self):
        """Тест предупреждения для скрытых файлов"""
        validator = StopWordValidator()

        result = validator.validate_export_filename(".hidden")

        assert result.is_valid is True
        assert any("скрытыми" in warning.lower() for warning in result.warnings)

    def test_validate_export_filename_short_name_warning(self):
        """Тест предупреждения для коротких имен"""
        validator = StopWordValidator()

        result = validator.validate_export_filename("ab")

        assert result.is_valid is True
        assert any("короткие" in warning.lower() for warning in result.warnings)

    def test_validate_export_filename_security_check(self):
        """Тест проверки безопасности имени файла"""
        validator = StopWordValidator()

        malicious_names = [
            "<script>alert('xss')</script>",
            "'; DROP TABLE files; --",
            "$(rm -rf /)"
        ]

        for name in malicious_names:
            result = validator.validate_export_filename(name)

            assert result.is_valid is False
            assert "опасное содержимое" in " ".join(result.errors).lower()


class TestValidatorEdgeCases:
    """Тесты граничных случаев и производительности валидаторов"""

    def test_validate_stopword_unicode_characters(self):
        """Тест валидации Unicode символов"""
        validator = StopWordValidator()

        unicode_words = [
            "тест",  # Кириллица
            "test",  # Латиница
            "тест123",  # Смешанное
            "café",  # С диакритиками
            "naïve",  # С диакритиками
        ]

        for word in unicode_words:
            result = validator.validate_stopword(word)
            # Большинство должно быть валидным, кроме слов с диакритиками
            if word in ["café", "naïve"]:
                # Эти слова могут быть невалидными из-за специальных символов
                assert isinstance(result.is_valid, bool)
            else:
                assert result.is_valid is True

    def test_validate_stopword_boundary_lengths(self):
        """Тест граничных значений длины"""
        validator = StopWordValidator()

        # Минимальная допустимая длина
        min_word = "ab"  # 2 символа
        result = validator.validate_stopword(min_word)
        assert result.is_valid is True

        # Максимальная допустимая длина
        max_word = "a" * 50  # 50 символов
        result = validator.validate_stopword(max_word)
        assert result.is_valid is True

        # Слишком короткое
        too_short = "a"  # 1 символ
        result = validator.validate_stopword(too_short)
        assert result.is_valid is False

        # Слишком длинное
        too_long = "a" * 51  # 51 символ
        result = validator.validate_stopword(too_long)
        assert result.is_valid is False

    def test_validate_import_file_boundary_sizes(self):
        """Тест граничных размеров файлов"""
        validator = StopWordValidator()

        # Максимальный допустимый размер (примерно)
        max_size_content = "слово\n" * (PERFORMANCE_LIMITS['max_import_words'] - 1)
        result = validator.validate_import_file(max_size_content)
        assert result.is_valid is True

        # Превышение лимита слов
        over_limit_content = "слово\n" * (PERFORMANCE_LIMITS['max_import_words'] + 1)
        result = validator.validate_import_file(over_limit_content)
        assert result.is_valid is False

    def test_validate_import_file_different_encodings(self):
        """Тест различных кодировок (симуляция)"""
        validator = StopWordValidator()

        # Симулируем содержимое с различными символами
        content_variants = [
            "спам\nреклама\nпродажа",  # Кириллица
            "spam\nadvertising\nsale",  # Латиница
            "スパム\n広告\n販売",  # Японские символы (могут быть невалидными)
            "垃圾邮件\n广告\n销售"  # Китайские символы (могут быть невалидными)
        ]

        for content in content_variants:
            result = validator.validate_import_file(content)
            # Результат зависит от поддерживаемых символов
            assert isinstance(result.is_valid, bool)

    def test_validator_performance_large_input(self):
        """Тест производительности с большими входными данными"""
        validator = StopWordValidator()

        # Большое стоп-слово (но в пределах лимита)
        large_word = "a" * 50
        result = validator.validate_stopword(large_word)
        assert result.is_valid is True

        # Большой файл импорта (но в пределах лимита)
        large_file_content = "\n".join([f"слово{i}" for i in range(100)])
        result = validator.validate_import_file(large_file_content)
        assert result.is_valid is True

        # Длинное имя файла (но в пределах лимита)
        long_filename = "a" * 100
        result = validator.validate_export_filename(long_filename)
        assert result.is_valid is True

    def test_validator_memory_usage(self):
        """Тест использования памяти"""
        validator = StopWordValidator()

        # Создаем много валидаторов для проверки утечек памяти
        validators = [StopWordValidator() for _ in range(100)]

        # Выполняем валидацию на всех
        for v in validators:
            result = v.validate_stopword("тест")
            assert result.is_valid is True

        # Проверяем что все валидаторы работают независимо
        assert len(validators) == 100

    def test_validator_concurrent_usage_simulation(self):
        """Симуляция конкурентного использования"""
        validator = StopWordValidator()

        # Симулируем одновременную валидацию разных типов данных
        words_to_validate = ["спам", "реклама", "продажа", "купить", "тест"]
        files_to_validate = [
            "спам\nреклама",
            "продажа\nкупить",
            "тест\nпроверка"
        ]
        filenames_to_validate = ["export1", "export2", "export3"]

        # Валидируем все одновременно
        word_results = [validator.validate_stopword(word) for word in words_to_validate]
        file_results = [validator.validate_import_file(content) for content in files_to_validate]
        filename_results = [validator.validate_export_filename(name) for name in filenames_to_validate]

        # Проверяем что все результаты корректны
        assert all(result.is_valid for result in word_results)
        assert all(result.is_valid for result in file_results)
        assert all(result.is_valid for result in filename_results)

    def test_validator_error_accumulation(self):
        """Тест накопления ошибок"""
        validator = StopWordValidator()

        # Слово с множественными проблемами
        problematic_word = ""  # Пустое
        result = validator.validate_stopword(problematic_word)

        assert result.is_valid is False
        assert len(result.errors) >= 1

        # Файл с множественными проблемами
        problematic_file = ""  # Пустой
        result = validator.validate_import_file(problematic_file)

        assert result.is_valid is False
        assert len(result.errors) >= 1

    def test_validator_warning_accumulation(self):
        """Тест накопления предупреждений"""
        validator = StopWordValidator()

        # Слово с потенциальными проблемами
        warning_word = "123"  # Числовое и короткое
        result = validator.validate_stopword(warning_word)

        assert result.is_valid is True
        assert len(result.warnings) >= 1

        # Имя файла с потенциальными проблемами
        warning_filename = ".ab"  # Скрытый и короткий
        result = validator.validate_export_filename(warning_filename)

        assert result.is_valid is True
        assert len(result.warnings) >= 2  # Два предупреждения

    def test_validator_normalization_consistency(self):
        """Тест консистентности нормализации"""
        validator = StopWordValidator()

        # Одинаковые слова в разных форматах
        word_variants = [
            "  СПАМ  ",
            "\tспам\n",
            "Спам",
            "спам"
        ]

        normalized_results = []
        for variant in word_variants:
            result = validator.validate_stopword(variant)
            if result.is_valid:
                normalized_results.append(result.normalized_data)

        # Все нормализованные результаты должны быть одинаковыми
        assert len(set(normalized_results)) == 1
        assert normalized_results[0] == "спам"

    def test_validator_regex_patterns_coverage(self):
        """Тест покрытия регулярных выражений"""
        validator = StopWordValidator()

        # Тестируем каждый опасный паттерн
        test_patterns = [
            "<script>alert('test')</script>",  # XSS
            "SELECT * FROM users",  # SQL
            "; rm -rf /",  # Command injection
            "../etc/passwd"  # Path traversal
        ]

        for pattern in test_patterns:
            safety_result = validator.check_word_safety(pattern)
            assert safety_result is False

            # Также тестируем через валидацию стоп-слова
            word_result = validator.validate_stopword(pattern)
            assert word_result.is_valid is False

    def test_validator_state_isolation(self):
        """Тест изоляции состояния между вызовами"""
        validator = StopWordValidator()

        # Первый вызов
        result1 = validator.validate_stopword("спам")
        assert result1.is_valid is True

        # Второй вызов с другими данными
        result2 = validator.validate_stopword("invalid word with spaces")
        assert result2.is_valid is False

        # Третий вызов должен быть независимым
        result3 = validator.validate_stopword("реклама")
        assert result3.is_valid is True

        # Проверяем что результаты не влияют друг на друга
        assert result1.normalized_data == "спам"
        assert result3.normalized_data == "реклама"
        assert result2.normalized_data is None

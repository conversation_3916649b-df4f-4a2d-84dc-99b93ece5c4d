# 📋 Система мониторинга времени ответа менеджеров

## 🎯 Описание

Система автоматического отслеживания времени ответа менеджеров на сообщения лидов с отправкой алертов при просрочке.

## ✨ Основные возможности

### 🔧 Для администраторов
- **Настройка времени ответа** - глобальные и индивидуальные настройки для аккаунтов
- **Включение/выключение системы** - полный контроль над мониторингом
- **Статистика** - детальная аналитика по времени ответа
- **Алерты** - уведомления о просрочках с маршрутизацией по тимлидам

### 🤖 Автоматическая работа
- **Отслеживание входящих сообщений** - только от внешних пользователей в личных чатах
- **Расчет дедлайнов** - автоматический расчет времени ответа
- **Мониторинг ответов** - отслеживание исходящих сообщений менеджеров
- **Система алертов** - предупреждения и уведомления о просрочках

## 🏗️ Архитектура системы

### 📁 Основные компоненты

```
src/
├── config/
│   └── response_time_manager.py      # Менеджер настроек времени ответа
├── monitoring/
│   ├── response_tracker.py           # Система отслеживания сообщений
│   └── telethon_manager.py          # Интеграция с Telegram (обновлен)
└── bot/
    ├── handlers/
    │   └── response_time_management.py # Обработчики админки
    ├── keyboards.py                   # Клавиатуры управления (обновлен)
    ├── states.py                      # FSM состояния (обновлен)
    └── alerts.py                      # Система алертов (обновлен)

data/
└── response_time_config.json         # Конфигурационный файл
```

### 🔄 Поток данных

```mermaid
graph TD
    A[Входящее сообщение от лида] --> B{Проверка типа чата}
    B -->|Личный чат| C{Проверка отправителя}
    B -->|Групповой чат| Z[Игнорировать]
    C -->|Внешний пользователь| D{Фильтрация ботов}
    C -->|Менеджер| Z
    D -->|Не бот| E[Добавить в отслеживание]
    D -->|Бот + фильтрация| Z
    E --> F[Расчет дедлайна]
    F --> G[Мониторинг дедлайна]
    G --> H{Получен ответ?}
    H -->|Да| I[Обновить статистику]
    H -->|Нет + просрочка| J[Отправить алерт]
```

## ⚙️ Настройки системы

### 🌐 Глобальные настройки

| Параметр | Описание | По умолчанию |
|----------|----------|--------------|
| `enabled` | Включена ли система | `false` |
| `default_response_time_minutes` | Время ответа по умолчанию | `10` минут |
| `check_interval_seconds` | Интервал проверки дедлайнов | `30` секунд |
| `alert_before_deadline_minutes` | Предупреждение до дедлайна | `2` минуты |

### 📱 Индивидуальные настройки аккаунтов

Для каждого аккаунта можно установить индивидуальное время ответа, которое переопределяет глобальное значение.

### 📊 Статистика

- `total_messages_tracked` - Всего отслежено сообщений
- `total_responses_on_time` - Ответов вовремя
- `total_responses_late` - Ответов с опозданием
- `total_missed_responses` - Пропущенных ответов

## 🎮 Использование

### 👨‍💼 Для администраторов

1. **Доступ к системе:**
   ```
   Главное меню → ⏰ Время ответа
   ```

2. **Основные настройки:**
   ```
   ⏰ Время ответа → ⚙️ Основные настройки
   - 🟢/🔴 Включить/Выключить систему
   - ⏰ Установить время ответа по умолчанию
   - 🔔 Настроить порог предупреждения
   ```

3. **Настройки аккаунтов:**
   ```
   ⏰ Время ответа → 📱 Настройки аккаунтов
   - Выбрать аккаунт
   - ⏰ Установить индивидуальное время
   - 🗑️ Сбросить к умолчанию
   ```

4. **Просмотр статистики:**
   ```
   ⏰ Время ответа → 📊 Статистика
   ```

### 🤖 Автоматическая работа

#### ✅ Что отслеживается:
- ✅ Сообщения в **личных чатах**
- ✅ От **внешних пользователей** (не от менеджеров)
- ✅ От **обычных пользователей** (не ботов при включенной фильтрации)
- ✅ При **включенной системе**

#### ❌ Что НЕ отслеживается:
- ❌ Сообщения в **групповых чатах**
- ❌ Сообщения в **супергруппах**
- ❌ Сообщения в **каналах**
- ❌ **Собственные сообщения** менеджеров
- ❌ Сообщения от **ботов** (при включенной фильтрации)
- ❌ При **выключенной системе**

## 🚨 Система алертов

### 📬 Типы алертов

1. **Предупреждение о приближении дедлайна:**
   ```
   ⚠️ СКОРО ДЕДЛАЙН ОТВЕТА!
   
   📱 Аккаунт: +************
   👤 От: Важный Клиент
   💬 Чат: VIP чат
   📝 Сообщение: Срочный вопрос...
   ⏰ Получено: 18:00
   ⚠️ Дедлайн: 18:10
   🟡 Осталось: 2 минуты
   ```

2. **Уведомление о просрочке:**
   ```
   🚨 ПРОСРОЧЕН ОТВЕТ МЕНЕДЖЕРА!
   
   📱 Аккаунт: +************
   👤 От: Важный Клиент
   💬 Чат: VIP чат
   📝 Сообщение: Срочный вопрос...
   ⏰ Получено: 18:00
   ⚠️ Дедлайн: 18:10
   🔴 Просрочено на: 5 минут
   ```

### 📨 Маршрутизация алертов

1. **Если назначены тимлиды** → отправка назначенным тимлидам
2. **Если тимлиды не назначены** → отправка всем администраторам

## 🧪 Тестирование

### 🔍 Комплексные тесты

```bash
# Полное тестирование системы
python scripts/test_response_time_comprehensive.py

# Тест системы алертов
python scripts/test_alert_system.py

# Базовое тестирование
python scripts/test_response_time_system.py
```

### ✅ Покрытие тестами

- ✅ Фильтрация по типам чатов (личные/групповые/каналы)
- ✅ Фильтрация ботов
- ✅ Фильтрация собственных сообщений
- ✅ Включение/выключение системы
- ✅ Полный цикл отслеживания ответа
- ✅ Система алертов о дедлайнах
- ✅ Индивидуальные настройки аккаунтов
- ✅ Статистика и отчетность

## 🔧 Конфигурация

### 📄 Файл конфигурации: `data/response_time_config.json`

```json
{
  "global_settings": {
    "enabled": true,
    "default_response_time_minutes": 10,
    "check_interval_seconds": 30,
    "alert_before_deadline_minutes": 2
  },
  "account_specific_settings": {
    "+************": {
      "response_time_minutes": 5,
      "updated_by": *********,
      "updated_at": "2025-07-29T21:10:06.752240"
    }
  },
  "statistics": {
    "total_messages_tracked": 15,
    "total_responses_on_time": 12,
    "total_responses_late": 2,
    "total_missed_responses": 1
  },
  "last_updated": "2025-07-29T21:10:09.775678",
  "updated_by": *********
}
```

## 🚀 Запуск и мониторинг

### 🔄 Автоматический запуск

Система автоматически запускается при старте бота, если включена в настройках.

### 📊 Мониторинг работы

```python
# Проверка статуса системы
from src.config.response_time_manager import response_time_manager
from src.monitoring.response_tracker import ResponseTracker

# Статус настроек
print(f"Система включена: {response_time_manager.enabled}")
print(f"Время ответа: {response_time_manager.default_response_time_minutes} мин")

# Статус трекера (если доступен)
status = tracker.get_status()
print(f"Активных дедлайнов: {status['pending_messages_count']}")
```

## 🔒 Безопасность и производительность

### 🛡️ Безопасность
- Доступ только для администраторов из `ADMIN_CHAT_ID`
- Валидация всех входных данных
- Логирование всех действий

### ⚡ Производительность
- Асинхронная обработка сообщений
- Оптимизированные интервалы проверки
- Автоматическая очистка старых сообщений
- Минимальное влияние на основную систему мониторинга

## 📝 Логирование

Система ведет подробные логи в файлах:
- `data/logs/response_tracker.log` - работа трекера
- `data/logs/response_time_management.log` - действия администраторов
- `data/logs/telethon_manager.log` - интеграция с Telegram

## 🎉 Заключение

Система мониторинга времени ответа обеспечивает:
- ✅ **Точное отслеживание** только релевантных сообщений (личные чаты)
- ✅ **Гибкие настройки** для разных аккаунтов и сценариев
- ✅ **Надежные алерты** с правильной маршрутизацией
- ✅ **Подробную статистику** для анализа эффективности
- ✅ **Простое управление** через интуитивный интерфейс

Система полностью интегрирована в существующую архитектуру проекта и готова к продуктивному использованию.

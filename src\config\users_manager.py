"""
Менеджер для управления локальной базой пользователей бота
"""

import json
import logging
from typing import Dict, List, Optional
from pathlib import Path
from datetime import datetime
from .settings import DATA_DIR

logger = logging.getLogger(__name__)

# Файл для хранения данных пользователей
USERS_DATABASE_FILE = DATA_DIR / 'users_database.json'

class UsersManager:
    """Класс для управления локальной базой пользователей"""
    
    def __init__(self):
        self.database_file = USERS_DATABASE_FILE
        self._users = self._load_users_database()
    
    def _load_users_database(self) -> Dict:
        """Загрузка базы пользователей из файла"""
        try:
            if self.database_file.exists():
                with open(self.database_file, 'r', encoding='utf-8') as f:
                    users_data = json.load(f)
                logger.info(f"База пользователей загружена: {len(users_data.get('users', {}))} пользователей")
                return users_data
            else:
                # База по умолчанию
                default_database = {
                    "users": {},  # user_id: {username, first_name, last_name, first_interaction, last_interaction}
                    "username_index": {},  # username: user_id (для быстрого поиска)
                    "last_updated": None,
                    "total_users": 0
                }
                self._save_users_database(default_database)
                logger.info("Создана новая база пользователей")
                return default_database
        except Exception as e:
            logger.error(f"Ошибка загрузки базы пользователей: {e}")
            # Возвращаем пустую базу при ошибке
            return {
                "users": {},
                "username_index": {},
                "last_updated": None,
                "total_users": 0
            }
    
    def _save_users_database(self, database: Dict = None) -> bool:
        """Сохранение базы пользователей в файл"""
        try:
            database_to_save = database or self._users
            database_to_save["last_updated"] = datetime.now().isoformat()
            database_to_save["total_users"] = len(database_to_save.get("users", {}))
            
            # Создаем директорию если не существует
            self.database_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.database_file, 'w', encoding='utf-8') as f:
                json.dump(database_to_save, f, indent=2, ensure_ascii=False)
            
            if database:
                self._users = database
            
            logger.debug(f"База пользователей сохранена: {database_to_save['total_users']} пользователей")
            return True
        except Exception as e:
            logger.error(f"Ошибка сохранения базы пользователей: {e}")
            return False
    
    def add_or_update_user(self, user_id: int, username: str = None, first_name: str = None, 
                          last_name: str = None, is_bot: bool = False) -> bool:
        """
        Добавление или обновление пользователя в базе
        
        Args:
            user_id (int): Telegram ID пользователя
            username (str): Никнейм пользователя (без @)
            first_name (str): Имя пользователя
            last_name (str): Фамилия пользователя
            is_bot (bool): Является ли пользователь ботом
            
        Returns:
            bool: True если пользователь успешно добавлен/обновлен
        """
        try:
            user_id_str = str(user_id)
            current_time = datetime.now().isoformat()
            
            # Получаем существующие данные пользователя
            existing_user = self._users["users"].get(user_id_str, {})
            
            # Подготавливаем данные пользователя
            user_data = {
                "user_id": user_id,
                "username": username.lower() if username else existing_user.get("username"),
                "first_name": first_name or existing_user.get("first_name"),
                "last_name": last_name or existing_user.get("last_name"),
                "is_bot": is_bot,
                "first_interaction": existing_user.get("first_interaction", current_time),
                "last_interaction": current_time,
                "interaction_count": existing_user.get("interaction_count", 0) + 1
            }
            
            # Обновляем основную базу
            self._users["users"][user_id_str] = user_data
            
            # Обновляем индекс по никнейму
            old_username = existing_user.get("username")
            new_username = user_data["username"]
            
            # Удаляем старый никнейм из индекса
            if old_username and old_username != new_username:
                self._users["username_index"].pop(old_username, None)
            
            # Добавляем новый никнейм в индекс
            if new_username:
                self._users["username_index"][new_username] = user_id
            
            # Сохраняем изменения
            success = self._save_users_database()
            
            if success:
                action = "обновлен" if user_id_str in existing_user else "добавлен"
                logger.debug(f"Пользователь {user_id} (@{new_username}) {action}")
            
            return success
            
        except Exception as e:
            logger.error(f"Ошибка добавления/обновления пользователя {user_id}: {e}")
            return False
    
    def find_user_by_username(self, username: str) -> Optional[Dict]:
        """
        Поиск пользователя по никнейму
        
        Args:
            username (str): Никнейм пользователя (без @)
            
        Returns:
            Optional[Dict]: Данные пользователя или None если не найден
        """
        try:
            username_clean = username.lstrip('@').lower()
            user_id = self._users["username_index"].get(username_clean)
            
            if user_id:
                user_data = self._users["users"].get(str(user_id))
                if user_data:
                    logger.debug(f"Пользователь найден по никнейму @{username_clean}: ID {user_id}")
                    return user_data
            
            logger.debug(f"Пользователь с никнеймом @{username_clean} не найден в локальной базе")
            return None
            
        except Exception as e:
            logger.error(f"Ошибка поиска пользователя по никнейму {username}: {e}")
            return None
    
    def find_user_by_id(self, user_id: int) -> Optional[Dict]:
        """
        Поиск пользователя по ID
        
        Args:
            user_id (int): Telegram ID пользователя
            
        Returns:
            Optional[Dict]: Данные пользователя или None если не найден
        """
        try:
            user_data = self._users["users"].get(str(user_id))
            if user_data:
                logger.debug(f"Пользователь найден по ID {user_id}")
                return user_data
            
            logger.debug(f"Пользователь с ID {user_id} не найден в локальной базе")
            return None
            
        except Exception as e:
            logger.error(f"Ошибка поиска пользователя по ID {user_id}: {e}")
            return None
    
    def get_all_users(self) -> Dict[str, Dict]:
        """
        Получение всех пользователей
        
        Returns:
            Dict[str, Dict]: Словарь всех пользователей
        """
        return self._users["users"].copy()
    
    def get_users_with_username(self) -> Dict[str, Dict]:
        """
        Получение пользователей, у которых есть никнейм
        
        Returns:
            Dict[str, Dict]: Словарь пользователей с никнеймами
        """
        users_with_username = {}
        for user_id, user_data in self._users["users"].items():
            if user_data.get("username"):
                users_with_username[user_id] = user_data
        return users_with_username
    
    def get_statistics(self) -> Dict:
        """
        Получение статистики базы пользователей
        
        Returns:
            Dict: Статистика базы пользователей
        """
        try:
            total_users = len(self._users["users"])
            users_with_username = len([u for u in self._users["users"].values() if u.get("username")])
            users_with_first_name = len([u for u in self._users["users"].values() if u.get("first_name")])
            
            # Находим самых активных пользователей
            most_active = sorted(
                self._users["users"].values(),
                key=lambda x: x.get("interaction_count", 0),
                reverse=True
            )[:5]
            
            return {
                "total_users": total_users,
                "users_with_username": users_with_username,
                "users_with_first_name": users_with_first_name,
                "most_active_users": most_active,
                "last_updated": self._users.get("last_updated"),
                "database_file": str(self.database_file)
            }
            
        except Exception as e:
            logger.error(f"Ошибка получения статистики: {e}")
            return {}
    
    def search_users(self, query: str) -> List[Dict]:
        """
        Поиск пользователей по запросу (никнейм, имя, фамилия)
        
        Args:
            query (str): Поисковый запрос
            
        Returns:
            List[Dict]: Список найденных пользователей
        """
        try:
            query_lower = query.lower().strip()
            found_users = []
            
            for user_data in self._users["users"].values():
                # Поиск по никнейму
                if user_data.get("username") and query_lower in user_data["username"].lower():
                    found_users.append(user_data)
                    continue
                
                # Поиск по имени
                if user_data.get("first_name") and query_lower in user_data["first_name"].lower():
                    found_users.append(user_data)
                    continue
                
                # Поиск по фамилии
                if user_data.get("last_name") and query_lower in user_data["last_name"].lower():
                    found_users.append(user_data)
                    continue
            
            logger.debug(f"Найдено {len(found_users)} пользователей по запросу '{query}'")
            return found_users
            
        except Exception as e:
            logger.error(f"Ошибка поиска пользователей по запросу '{query}': {e}")
            return []

# Глобальный экземпляр менеджера пользователей
users_manager = UsersManager()

"""
Тесты для улучшений TelethonManager
"""

import pytest
import asyncio
import sqlite3
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
from datetime import datetime, timedelta

from src.monitoring.telethon_manager import (
    TelethonManager, SessionLockManager, ConnectionHealthMonitor, NetworkErrorHandler
)
from src.config.environment import env_config


class TestSessionLockManager:
    """Тесты для SessionLockManager"""
    
    def test_get_lock_creates_new_lock(self):
        """Тест создания новой блокировки"""
        manager = SessionLockManager()
        session_path = "/test/path/session.session"
        
        lock1 = manager.get_lock(session_path)
        lock2 = manager.get_lock(session_path)
        
        # Должна возвращаться одна и та же блокировка
        assert lock1 is lock2
    
    def test_cleanup_lock(self):
        """Тест очистки блокировки"""
        manager = SessionLockManager()
        session_path = "/test/path/session.session"
        
        # Создаем блокировку
        lock = manager.get_lock(session_path)
        assert session_path in manager._locks
        
        # Очищаем блокировку
        manager.cleanup_lock(session_path)
        assert session_path not in manager._locks


class TestNetworkErrorHandler:
    """Тесты для NetworkErrorHandler"""
    
    def test_classify_database_error(self):
        """Тест классификации ошибок базы данных"""
        handler = NetworkErrorHandler()
        
        db_error = sqlite3.OperationalError("database is locked")
        assert handler.classify_error(db_error) == 'database'
    
    def test_classify_network_error(self):
        """Тест классификации сетевых ошибок"""
        handler = NetworkErrorHandler()
        
        conn_error = ConnectionError("Connection failed")
        assert handler.classify_error(conn_error) == 'network'
        
        timeout_error = TimeoutError("Timeout")
        assert handler.classify_error(timeout_error) == 'network'
    
    def test_should_retry_logic(self):
        """Тест логики повторов"""
        handler = NetworkErrorHandler()
        
        # Сетевые ошибки должны повторяться
        network_error = ConnectionError("Connection failed")
        assert handler.should_retry(network_error, 0) == True
        assert handler.should_retry(network_error, 4) == True
        assert handler.should_retry(network_error, 5) == False
        
        # Ошибки авторизации не должны повторяться
        # Создаем мок ошибки авторизации, используя ValueError как пример
        auth_error = ValueError("auth error")
        # Переопределяем классификацию для теста
        handler.auth_errors = (ValueError,)
        assert handler.should_retry(auth_error, 0) == False
    
    def test_get_retry_delay(self):
        """Тест расчета задержки"""
        handler = NetworkErrorHandler()
        
        network_error = ConnectionError("Connection failed")
        
        # Задержка должна увеличиваться экспоненциально
        delay1 = handler.get_retry_delay(network_error, 0)
        delay2 = handler.get_retry_delay(network_error, 1)
        delay3 = handler.get_retry_delay(network_error, 2)
        
        assert delay1 < delay2 < delay3
        assert delay1 >= 1.0  # Минимальная задержка
        assert delay3 <= 60.0  # Максимальная задержка для сетевых ошибок


class TestConnectionHealthMonitor:
    """Тесты для ConnectionHealthMonitor"""
    
    def test_record_connection_event(self):
        """Тест записи событий соединения"""
        monitor = ConnectionHealthMonitor()
        phone = "+**********"
        
        # Записываем событие подключения
        monitor.record_connection_event(phone, 'connect')
        
        assert phone in monitor.connection_stats
        assert monitor.connection_stats[phone]['connects'] == 1
        assert monitor.reconnect_attempts[phone] == 0
    
    def test_record_error_event(self):
        """Тест записи ошибок"""
        monitor = ConnectionHealthMonitor()
        phone = "+**********"
        
        # Записываем ошибку
        monitor.record_connection_event(phone, 'error', 'Test error')
        
        assert phone in monitor.connection_stats
        assert monitor.connection_stats[phone]['errors'] == 1
        assert monitor.connection_stats[phone]['last_error'] == 'Test error'
        assert monitor.reconnect_attempts[phone] == 1
    
    def test_should_attempt_reconnect(self):
        """Тест логики переподключения"""
        monitor = ConnectionHealthMonitor()
        phone = "+**********"
        
        # Изначально должно разрешать переподключение
        assert monitor.should_attempt_reconnect(phone) == True
        
        # После максимального количества попыток - не должно
        monitor.reconnect_attempts[phone] = monitor.max_reconnect_attempts
        assert monitor.should_attempt_reconnect(phone) == False
    
    def test_get_reconnect_delay(self):
        """Тест расчета задержки переподключения"""
        monitor = ConnectionHealthMonitor()
        phone = "+**********"
        
        # Задержка должна увеличиваться
        monitor.reconnect_attempts[phone] = 0
        delay1 = monitor.get_reconnect_delay(phone)
        
        monitor.reconnect_attempts[phone] = 2
        delay2 = monitor.get_reconnect_delay(phone)
        
        assert delay1 < delay2
        assert delay2 <= 60  # Максимальная задержка


class TestTelethonManagerImprovements:
    """Тесты для улучшений TelethonManager"""
    
    @pytest.fixture
    def telethon_manager(self):
        """Фикстура для TelethonManager"""
        with patch('src.monitoring.telethon_manager.env_config'):
            manager = TelethonManager()
            return manager
    
    def test_initialization_creates_components(self, telethon_manager):
        """Тест создания новых компонентов при инициализации"""
        assert isinstance(telethon_manager.session_lock_manager, SessionLockManager)
        assert isinstance(telethon_manager.health_monitor, ConnectionHealthMonitor)
        assert isinstance(telethon_manager._reconnect_tasks, dict)
    
    @pytest.mark.asyncio
    async def test_safe_session_operation_with_retry(self, telethon_manager):
        """Тест безопасной операции с сессией с повторами"""
        session_path = Path("/test/session.session")
        
        # Мокаем операцию, которая сначала падает, потом работает
        call_count = 0
        async def test_operation():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise sqlite3.OperationalError("database is locked")
            return "success"
        
        # Операция должна успешно выполниться после повтора
        result = await telethon_manager._safe_session_operation(session_path, test_operation)
        assert result == "success"
        assert call_count == 2
    
    @pytest.mark.asyncio
    async def test_get_connection_health_report(self, telethon_manager):
        """Тест получения отчета о здоровье соединений"""
        # Добавляем тестовый клиент
        phone = "+**********"
        telethon_manager.clients[phone] = {
            'client': Mock(),
            'monitoring': True,
            'session_file': 'test.session'
        }
        
        # Добавляем статистику
        telethon_manager.health_monitor.connection_stats[phone] = {
            'connects': 5,
            'disconnects': 2,
            'errors': 1,
            'last_error': 'Test error'
        }
        
        report = await telethon_manager.get_connection_health_report()
        
        assert report['total_clients'] == 1
        assert phone in report['connection_stats']
        assert report['connection_stats'][phone]['connects'] == 5
        assert report['connection_stats'][phone]['errors'] == 1
    
    @pytest.mark.asyncio
    async def test_get_error_statistics(self, telethon_manager):
        """Тест получения статистики ошибок"""
        # Добавляем статистику ошибок
        phone = "+**********"
        telethon_manager.health_monitor.connection_stats[phone] = {
            'errors': 3,
            'last_error': 'database is locked',
            'last_disconnect': datetime.now()
        }
        
        stats = await telethon_manager.get_error_statistics(24)
        
        assert stats['period_hours'] == 24
        assert phone in stats['clients']
        assert stats['clients'][phone]['total_errors'] == 3
        assert stats['summary']['database_locked_errors'] == 1


if __name__ == "__main__":
    pytest.main([__file__])

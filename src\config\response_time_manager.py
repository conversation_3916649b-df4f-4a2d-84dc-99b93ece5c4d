"""
Менеджер для управления настройками мониторинга времени ответа
"""

import json
import logging
from typing import Dict, Optional, Any
from pathlib import Path
from datetime import datetime
from .settings import RESPONSE_TIME_CONFIG_FILE

logger = logging.getLogger(__name__)

class ResponseTimeManager:
    """Класс для управления настройками мониторинга времени ответа"""
    
    def __init__(self):
        self.config_file = RESPONSE_TIME_CONFIG_FILE
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Загрузка конфигурации из файла"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"Конфигурация времени ответа загружена из {self.config_file}")
                return config
            else:
                # Конфигурация по умолчанию
                default_config = {
                    "global_settings": {
                        "enabled": False,
                        "default_response_time_minutes": 10,
                        "check_interval_seconds": 30
                    },
                    "account_specific_settings": {},
                    "statistics": {
                        "total_messages_tracked": 0,
                        "total_responses_on_time": 0,
                        "total_responses_late": 0,
                        "total_missed_responses": 0
                    },
                    "last_updated": None,
                    "updated_by": None
                }
                self._save_config(default_config)
                logger.info("Создана конфигурация времени ответа по умолчанию")
                return default_config
        except Exception as e:
            logger.error(f"Ошибка загрузки конфигурации времени ответа: {e}")
            # Возвращаем конфигурацию по умолчанию при ошибке
            return {
                "global_settings": {
                    "enabled": False,
                    "default_response_time_minutes": 10,
                    "check_interval_seconds": 30
                },
                "account_specific_settings": {},
                "statistics": {
                    "total_messages_tracked": 0,
                    "total_responses_on_time": 0,
                    "total_responses_late": 0,
                    "total_missed_responses": 0
                },
                "last_updated": None,
                "updated_by": None
            }
    
    def _save_config(self, config: Dict = None) -> bool:
        """Сохранение конфигурации в файл"""
        try:
            config_to_save = config or self._config
            config_to_save["last_updated"] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            
            if config:
                self._config = config
            
            logger.info(f"Конфигурация времени ответа сохранена в {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Ошибка сохранения конфигурации времени ответа: {e}")
            return False
    
    @property
    def enabled(self) -> bool:
        """Включен ли мониторинг времени ответа"""
        return self._config.get("global_settings", {}).get("enabled", False)
    
    @property
    def default_response_time_minutes(self) -> int:
        """Время ответа по умолчанию в минутах"""
        return self._config.get("global_settings", {}).get("default_response_time_minutes", 10)
    
    @property
    def check_interval_seconds(self) -> int:
        """Интервал проверки дедлайнов в секундах"""
        return self._config.get("global_settings", {}).get("check_interval_seconds", 30)
    
    def set_enabled(self, enabled: bool, updated_by: int) -> bool:
        """Включить/выключить мониторинг времени ответа"""
        try:
            self._config["global_settings"]["enabled"] = enabled
            self._config["updated_by"] = updated_by
            
            success = self._save_config()
            if success:
                status = "включен" if enabled else "выключен"
                logger.info(f"Мониторинг времени ответа {status} администратором {updated_by}")
            
            return success
        except Exception as e:
            logger.error(f"Ошибка изменения статуса мониторинга: {e}")
            return False
    
    def set_default_response_time(self, minutes: int, updated_by: int) -> bool:
        """Установить время ответа по умолчанию"""
        try:
            if not self._validate_response_time(minutes):
                return False
            
            self._config["global_settings"]["default_response_time_minutes"] = minutes
            self._config["updated_by"] = updated_by
            
            success = self._save_config()
            if success:
                logger.info(f"Время ответа по умолчанию установлено на {minutes} минут администратором {updated_by}")
            
            return success
        except Exception as e:
            logger.error(f"Ошибка установки времени ответа по умолчанию: {e}")
            return False

    def set_check_interval(self, seconds: int, updated_by: int) -> bool:
        """Установить интервал проверки в секундах"""
        try:
            if seconds < 10 or seconds > 300:
                logger.error("Интервал проверки должен быть от 10 до 300 секунд")
                return False

            self._config["global_settings"]["check_interval_seconds"] = seconds
            self._config["updated_by"] = updated_by

            success = self._save_config()
            if success:
                logger.info(f"Интервал проверки установлен на {seconds} секунд администратором {updated_by}")

            return success
        except Exception as e:
            logger.error(f"Ошибка установки интервала проверки: {e}")
            return False

    def set_account_response_time(self, phone: str, minutes: int, updated_by: int) -> bool:
        """Установить индивидуальное время ответа для аккаунта"""
        try:
            if not self._validate_response_time(minutes):
                return False
            
            if "account_specific_settings" not in self._config:
                self._config["account_specific_settings"] = {}
            
            self._config["account_specific_settings"][phone] = {
                "response_time_minutes": minutes,
                "updated_by": updated_by,
                "updated_at": datetime.now().isoformat()
            }
            self._config["updated_by"] = updated_by
            
            success = self._save_config()
            if success:
                logger.info(f"Время ответа для аккаунта {phone} установлено на {minutes} минут администратором {updated_by}")
            
            return success
        except Exception as e:
            logger.error(f"Ошибка установки времени ответа для аккаунта {phone}: {e}")
            return False
    
    def get_response_time_for_account(self, phone: str) -> int:
        """Получить время ответа для конкретного аккаунта"""
        account_settings = self._config.get("account_specific_settings", {}).get(phone)
        if account_settings:
            return account_settings.get("response_time_minutes", self.default_response_time_minutes)
        return self.default_response_time_minutes
    
    def remove_account_settings(self, phone: str, updated_by: int) -> bool:
        """Удалить индивидуальные настройки для аккаунта"""
        try:
            if phone in self._config.get("account_specific_settings", {}):
                del self._config["account_specific_settings"][phone]
                self._config["updated_by"] = updated_by
                
                success = self._save_config()
                if success:
                    logger.info(f"Индивидуальные настройки для аккаунта {phone} удалены администратором {updated_by}")
                
                return success
            return True  # Настройки уже отсутствуют
        except Exception as e:
            logger.error(f"Ошибка удаления настроек для аккаунта {phone}: {e}")
            return False
    
    def _validate_response_time(self, minutes: int) -> bool:
        """Валидация времени ответа"""
        if not isinstance(minutes, int):
            logger.error("Время ответа должно быть целым числом")
            return False
        
        if minutes < 1:
            logger.error("Время ответа не может быть меньше 1 минуты")
            return False
        
        if minutes > 1440:  # 24 часа
            logger.error("Время ответа не может быть больше 24 часов")
            return False
        
        return True
    
    def get_all_settings(self) -> Dict[str, Any]:
        """Получить все настройки"""
        return self._config.copy()
    
    def get_statistics(self) -> Dict[str, int]:
        """Получить статистику"""
        return self._config.get("statistics", {}).copy()
    
    def update_statistics(self, stat_type: str, increment: int = 1) -> bool:
        """Обновить статистику"""
        try:
            valid_stats = ["total_messages_tracked", "total_responses_on_time",
                          "total_responses_late", "total_missed_responses"]

            if stat_type not in valid_stats:
                logger.error(f"Неизвестный тип статистики: {stat_type}")
                return False

            if "statistics" not in self._config:
                self._config["statistics"] = {}

            current_value = self._config["statistics"].get(stat_type, 0)
            self._config["statistics"][stat_type] = current_value + increment

            return self._save_config()
        except Exception as e:
            logger.error(f"Ошибка обновления статистики {stat_type}: {e}")
            return False

    def set_response_time_for_all_accounts(self, minutes: int, account_phones: list, updated_by: int) -> dict:
        """
        Установить время ответа для всех указанных аккаунтов

        Args:
            minutes (int): Время ответа в минутах
            account_phones (list): Список номеров телефонов аккаунтов
            updated_by (int): ID администратора

        Returns:
            dict: Результат операции с количеством обработанных аккаунтов
        """
        try:
            if not self._validate_response_time(minutes):
                return {"success": False, "error": "Некорректное время ответа"}

            if not account_phones:
                return {"success": False, "error": "Список аккаунтов пуст"}

            if "account_specific_settings" not in self._config:
                self._config["account_specific_settings"] = {}

            success_count = 0
            failed_phones = []

            for phone in account_phones:
                try:
                    self._config["account_specific_settings"][phone] = {
                        "response_time_minutes": minutes,
                        "updated_by": updated_by,
                        "updated_at": datetime.now().isoformat()
                    }
                    success_count += 1
                except Exception as e:
                    logger.error(f"Ошибка установки времени для аккаунта {phone}: {e}")
                    failed_phones.append(phone)

            self._config["updated_by"] = updated_by

            if self._save_config():
                logger.info(f"Массовая установка времени ответа {minutes} мин для {success_count} аккаунтов администратором {updated_by}")
                return {
                    "success": True,
                    "processed_count": success_count,
                    "failed_phones": failed_phones,
                    "total_accounts": len(account_phones)
                }
            else:
                return {"success": False, "error": "Ошибка сохранения конфигурации"}

        except Exception as e:
            logger.error(f"Ошибка массовой установки времени ответа: {e}")
            return {"success": False, "error": str(e)}

    def clear_all_account_settings(self, account_phones: list, updated_by: int) -> dict:
        """
        Очистить индивидуальные настройки для всех указанных аккаунтов
        (вернуть к настройкам по умолчанию)

        Args:
            account_phones (list): Список номеров телефонов аккаунтов
            updated_by (int): ID администратора

        Returns:
            dict: Результат операции
        """
        try:
            if not account_phones:
                return {"success": False, "error": "Список аккаунтов пуст"}

            if "account_specific_settings" not in self._config:
                self._config["account_specific_settings"] = {}

            cleared_count = 0
            not_found_phones = []

            for phone in account_phones:
                if phone in self._config["account_specific_settings"]:
                    del self._config["account_specific_settings"][phone]
                    cleared_count += 1
                else:
                    not_found_phones.append(phone)

            self._config["updated_by"] = updated_by

            if self._save_config():
                logger.info(f"Очищены индивидуальные настройки для {cleared_count} аккаунтов администратором {updated_by}")
                return {
                    "success": True,
                    "cleared_count": cleared_count,
                    "not_found_phones": not_found_phones,
                    "total_accounts": len(account_phones)
                }
            else:
                return {"success": False, "error": "Ошибка сохранения конфигурации"}

        except Exception as e:
            logger.error(f"Ошибка очистки индивидуальных настроек: {e}")
            return {"success": False, "error": str(e)}

# Глобальный экземпляр менеджера
response_time_manager = ResponseTimeManager()

#!/usr/bin/env python3
"""
Скрипт для отладки проблемы с системой мониторинга времени ответа
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.monitoring.response_tracker import ResponseTracker, PendingMessage
from src.config.response_time_manager import response_time_manager
from src.config.teamleads_manager import teamleads_manager

async def debug_response_time_issue():
    """Отладка проблемы с системой мониторинга времени ответа"""
    print("🔍 ОТЛАДКА ПРОБЛЕМЫ С СИСТЕМОЙ МОНИТОРИНГА ВРЕМЕНИ ОТВЕТА")
    print("=" * 70)
    
    # 1. Проверяем конфигурацию
    print("\n1️⃣ ПРОВЕРКА КОНФИГУРАЦИИ:")
    print("-" * 40)
    
    print(f"✅ Мониторинг включен: {response_time_manager.enabled}")
    print(f"✅ Время ответа по умолчанию: {response_time_manager.default_response_time_minutes} мин")
    print(f"✅ Интервал проверки: {response_time_manager.check_interval_seconds} сек")
    print(f"✅ Предупреждение за: {response_time_manager.alert_before_deadline_minutes} мин")
    
    # Проверяем настройки для конкретного номера
    phone = "+***********"
    response_time = response_time_manager.get_response_time_for_account(phone)
    print(f"✅ Время ответа для {phone}: {response_time} мин")
    
    # 2. Проверяем назначения тимлидов
    print("\n2️⃣ ПРОВЕРКА НАЗНАЧЕНИЙ ТИМЛИДОВ:")
    print("-" * 40)
    
    assigned_teamleads = teamleads_manager.get_teamleads_for_phone(phone)
    if assigned_teamleads:
        for teamlead in assigned_teamleads:
            print(f"✅ Тимлид: {teamlead.get('username')} (ID: {teamlead.get('user_id')})")
    else:
        print("❌ Тимлиды не назначены")
    
    # 3. Тестируем систему алертов
    print("\n3️⃣ ТЕСТИРОВАНИЕ СИСТЕМЫ АЛЕРТОВ:")
    print("-" * 40)
    
    alerts_received = []
    
    async def test_alert_callback(phone: str, alert_text: str):
        alert = {
            'phone': phone,
            'alert': alert_text,
            'timestamp': datetime.now(),
            'is_expired': "🚨 ПРОСРОЧЕН ОТВЕТ МЕНЕДЖЕРА!" in alert_text
        }
        alerts_received.append(alert)
        alert_type = "ПРОСРОЧКА" if alert['is_expired'] else "ПРЕДУПРЕЖДЕНИЕ"
        print(f"   📨 {alert_type}: {phone}")
        print(f"      {alert_text[:100]}...")
    
    # Создаем трекер с тестовым callback
    tracker = ResponseTracker(test_alert_callback)
    
    # 4. Симулируем проблемную ситуацию
    print("\n4️⃣ СИМУЛЯЦИЯ ПРОБЛЕМНОЙ СИТУАЦИИ:")
    print("-" * 40)
    
    # Создаем сообщение с коротким дедлайном для теста
    current_time = datetime.now()
    test_message_data = {
        'account_phone': phone,
        'message_id': 12345,
        'sender_id': *********,
        'sender_name': 'Mossad',
        'chat_id': *********,
        'chat_title': 'Test Chat',
        'text': 'Тестовое сообщение для отладки',
        'is_from_external': True,
        'chat_type': 'private',
        'is_bot': False
    }
    
    # Временно изменяем настройки для быстрого теста
    original_response_time = response_time_manager._config["account_specific_settings"].get(phone, {}).get("response_time_minutes", response_time_manager.default_response_time_minutes)
    original_check_interval = response_time_manager.check_interval_seconds
    original_alert_threshold = response_time_manager.alert_before_deadline_minutes
    
    # Устанавливаем короткие интервалы для теста
    response_time_manager._config["global_settings"]["check_interval_seconds"] = 2  # 2 секунды
    response_time_manager._config["global_settings"]["alert_before_deadline_minutes"] = 0.05  # 3 секунды
    
    if phone not in response_time_manager._config["account_specific_settings"]:
        response_time_manager._config["account_specific_settings"][phone] = {}
    response_time_manager._config["account_specific_settings"][phone]["response_time_minutes"] = 0.1  # 6 секунд
    
    print(f"⏰ Установлены тестовые настройки:")
    print(f"   - Время ответа: 6 секунд")
    print(f"   - Предупреждение за: 3 секунды")
    print(f"   - Интервал проверки: 2 секунды")
    
    # Запускаем мониторинг
    await tracker.start_monitoring()
    print("✅ Мониторинг запущен")
    
    # Добавляем сообщение для отслеживания
    success = tracker.track_incoming_message(test_message_data)
    if success:
        print("✅ Сообщение добавлено для отслеживания")
        
        # Получаем информацию о добавленном сообщении
        pending_messages = tracker.get_pending_messages_for_account(phone)
        if pending_messages:
            msg = pending_messages[0]
            # deadline уже datetime объект, не строка
            if isinstance(msg['deadline'], str):
                deadline = datetime.fromisoformat(msg['deadline'])
            else:
                deadline = msg['deadline']
            print(f"   📅 Дедлайн: {deadline.strftime('%H:%M:%S')}")
            print(f"   ⏱️  Осталось: ~6 секунд")
    else:
        print("❌ Не удалось добавить сообщение для отслеживания")
        return
    
    # Ждем и наблюдаем за алертами
    print("\n⏳ Ожидание алертов...")
    start_time = datetime.now()
    
    for i in range(15):  # Ждем 15 секунд
        await asyncio.sleep(1)
        elapsed = (datetime.now() - start_time).total_seconds()
        print(f"   {elapsed:.0f}с - Алертов получено: {len(alerts_received)}")
        
        # Показываем новые алерты
        if len(alerts_received) > i:
            for j in range(i, len(alerts_received)):
                alert = alerts_received[j]
                alert_type = "🚨 ПРОСРОЧКА" if alert['is_expired'] else "⚠️ ПРЕДУПРЕЖДЕНИЕ"
                print(f"      {alert_type} в {alert['timestamp'].strftime('%H:%M:%S')}")
    
    # Останавливаем мониторинг
    await tracker.stop_monitoring()
    print("✅ Мониторинг остановлен")
    
    # Восстанавливаем оригинальные настройки
    response_time_manager._config["global_settings"]["check_interval_seconds"] = original_check_interval
    response_time_manager._config["global_settings"]["alert_before_deadline_minutes"] = original_alert_threshold
    if phone in response_time_manager._config["account_specific_settings"]:
        response_time_manager._config["account_specific_settings"][phone]["response_time_minutes"] = original_response_time
    
    # 5. Анализ результатов
    print("\n5️⃣ АНАЛИЗ РЕЗУЛЬТАТОВ:")
    print("-" * 40)
    
    warning_alerts = [a for a in alerts_received if not a['is_expired']]
    expired_alerts = [a for a in alerts_received if a['is_expired']]
    
    print(f"📊 Всего алертов: {len(alerts_received)}")
    print(f"   ⚠️  Предупреждений: {len(warning_alerts)}")
    print(f"   🚨 Просрочек: {len(expired_alerts)}")
    
    if len(expired_alerts) == 0:
        print("\n❌ ПРОБЛЕМА НАЙДЕНА: Алерты о просрочке не отправляются!")
        print("   Возможные причины:")
        print("   1. Мониторинг дедлайнов не работает")
        print("   2. Логика проверки дедлайнов неправильная")
        print("   3. Callback не вызывается для просроченных сообщений")
    else:
        print("\n✅ Система алертов работает корректно")
    
    print("\n" + "=" * 70)
    print("ОТЛАДКА ЗАВЕРШЕНА")

if __name__ == "__main__":
    asyncio.run(debug_response_time_issue())

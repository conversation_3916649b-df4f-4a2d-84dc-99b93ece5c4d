"""
Обработчики управления ролями пользователей
"""

import logging
from datetime import datetime
from aiogram import Dispatcher, types
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command

from ..states import RolesManagement
from src.config.roles_manager import roles_manager
from src.config.environment import env_config

logger = logging.getLogger(__name__)

def register_roles_handlers(dp: Dispatcher):
    """Регистрация обработчиков управления ролями"""
    
    @dp.message(lambda m: m.text == "👤 Управление ролями")
    async def roles_management_menu(message: types.Message):
        """Главное меню управления ролями"""
        user_id = message.from_user.id

        # Только для администраторов (используем env_config для проверки)
        from src.config.environment import env_config
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен. Только администраторы могут управлять ролями.")
            return
        
        # Получаем статистику
        stats = roles_manager.get_statistics()
        
        text = (
            f"👤 **Управление ролями пользователей**\n\n"
            f"📊 **Статистика:**\n"
            f"• Администраторов: {stats.get('total_admins', 0)}\n"
            f"• Тимлидов: {stats.get('total_teamleads', 0)}\n"
            f"• Всего пользователей с доступом: {stats.get('total_users_with_access', 0)}\n\n"
            f"🔧 **Доступные действия:**\n"
            f"• Назначить роль тимлида пользователю\n"
            f"• Удалить роль у пользователя\n"
            f"• Просмотреть всех пользователей с ролями"
        )
        
        from ..keyboards import roles_management_keyboard
        await message.answer(text, reply_markup=roles_management_keyboard(), parse_mode='Markdown')
    
    @dp.callback_query(lambda c: c.data == "assign_teamlead_role")
    async def assign_teamlead_role_start(callback: types.CallbackQuery, state: FSMContext):
        """Начало процесса назначения роли тимлида"""
        await callback.answer()
        
        user_id = callback.from_user.id
        from src.config.environment import env_config
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return
        
        text = (
            f"👤 **Назначение роли тимлида**\n\n"
            f"Введите Telegram ID пользователя, которому хотите назначить роль тимлида.\n\n"
            f"💡 **Как получить ID:**\n"
            f"• Попросите пользователя написать @userinfobot\n"
            f"• Или найдите ID в логах бота\n\n"
            f"📝 Введите ID пользователя:"
        )
        
        await callback.message.edit_text(text, parse_mode='Markdown')
        await state.set_state(RolesManagement.waiting_for_user_id_to_assign)
    
    @dp.message(RolesManagement.waiting_for_user_id_to_assign)
    async def process_user_id_for_assignment(message: types.Message, state: FSMContext):
        """Обработка ввода ID пользователя для назначения роли"""
        try:
            target_user_id = int(message.text.strip())
            admin_user_id = message.from_user.id
            
            # Проверяем, что пользователь не назначает роль самому себе
            if target_user_id == admin_user_id:
                await message.answer("❌ Нельзя изменить свою собственную роль")
                return
            
            # Проверяем текущую роль пользователя
            current_role = roles_manager.get_user_role(target_user_id)
            
            if current_role == "admin":
                await message.answer("❌ Нельзя изменить роль администратора")
                return
            
            if current_role == "teamlead":
                await message.answer(
                    f"⚠️ Пользователь {target_user_id} уже является тимлидом.\n"
                    f"Для изменения роли сначала удалите текущую роль."
                )
                return
            
            # Назначаем роль тимлида
            success = roles_manager.set_user_role(target_user_id, "teamlead", admin_user_id)
            
            if success:
                text = (
                    f"✅ **Роль тимлида назначена!**\n\n"
                    f"👤 **Пользователь:** {target_user_id}\n"
                    f"🎭 **Роль:** Тимлид\n"
                    f"👨‍💼 **Назначил:** {admin_user_id}\n"
                    f"📅 **Время:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                    f"💡 **Что дальше:**\n"
                    f"• Пользователь получит доступ к боту\n"
                    f"• Назначьте ему аккаунты через \"👥 Управление тимлидами\"\n"
                    f"• Он будет получать уведомления от назначенных аккаунтов"
                )
                
                # Пытаемся отправить уведомление новому тимлиду
                try:
                    notification_text = (
                        f"🎉 **Вам назначена роль тимлида!**\n\n"
                        f"Теперь у вас есть доступ к системе мониторинга сообщений.\n\n"
                        f"Используйте команду /start для начала работы."
                    )
                    await message.bot.send_message(target_user_id, notification_text, parse_mode='Markdown')
                    text += f"\n\n📨 Уведомление отправлено пользователю"
                except Exception as e:
                    logger.warning(f"Не удалось отправить уведомление пользователю {target_user_id}: {e}")
                    text += f"\n\n⚠️ Не удалось отправить уведомление пользователю"
                
                await message.answer(text, parse_mode='Markdown')
                logger.info(f"Администратор {admin_user_id} назначил роль тимлида пользователю {target_user_id}")
            else:
                await message.answer("❌ Ошибка при назначении роли")
            
            await state.clear()
            
        except ValueError:
            await message.answer("❌ Введите корректный числовой ID пользователя")
        except Exception as e:
            logger.error(f"Ошибка назначения роли: {e}")
            await message.answer("❌ Произошла ошибка при назначении роли")
            await state.clear()
    
    @dp.callback_query(lambda c: c.data == "remove_user_role")
    async def remove_user_role_start(callback: types.CallbackQuery, state: FSMContext):
        """Начало процесса удаления роли"""
        await callback.answer()
        
        user_id = callback.from_user.id
        from src.config.environment import env_config
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return
        
        # Получаем список пользователей с ролями (кроме администраторов)
        teamleads = roles_manager.get_users_by_role("teamlead")
        
        if not teamleads:
            await callback.message.edit_text(
                "ℹ️ **Нет пользователей с ролями для удаления**\n\n"
                "Все пользователи либо администраторы (их роли нельзя удалить), либо не имеют ролей.",
                parse_mode='Markdown'
            )
            return
        
        text = (
            f"🗑️ **Удаление роли пользователя**\n\n"
            f"Введите Telegram ID пользователя, у которого хотите удалить роль.\n\n"
            f"👥 **Пользователи с ролью тимлида:**\n"
        )
        
        for teamlead_id in teamleads[:10]:  # Показываем первых 10
            text += f"• {teamlead_id}\n"
        
        if len(teamleads) > 10:
            text += f"• ... и еще {len(teamleads) - 10} пользователей\n"
        
        text += f"\n📝 Введите ID пользователя:"
        
        await callback.message.edit_text(text, parse_mode='Markdown')
        await state.set_state(RolesManagement.waiting_for_user_id_to_remove)
    
    @dp.message(RolesManagement.waiting_for_user_id_to_remove)
    async def process_user_id_for_removal(message: types.Message, state: FSMContext):
        """Обработка ввода ID пользователя для удаления роли"""
        try:
            target_user_id = int(message.text.strip())
            admin_user_id = message.from_user.id
            
            # Проверяем, что пользователь не удаляет роль самому себе
            if target_user_id == admin_user_id:
                await message.answer("❌ Нельзя удалить свою собственную роль")
                return
            
            # Проверяем текущую роль пользователя
            current_role = roles_manager.get_user_role(target_user_id)
            
            if current_role == "admin":
                await message.answer("❌ Нельзя удалить роль администратора")
                return
            
            if current_role == "none":
                await message.answer(f"ℹ️ Пользователь {target_user_id} уже не имеет роли")
                return
            
            # Удаляем роль
            success = roles_manager.remove_user_role(target_user_id, admin_user_id)
            
            if success:
                text = (
                    f"✅ **Роль удалена!**\n\n"
                    f"👤 **Пользователь:** {target_user_id}\n"
                    f"🎭 **Предыдущая роль:** {current_role}\n"
                    f"👨‍💼 **Удалил:** {admin_user_id}\n"
                    f"📅 **Время:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                    f"⚠️ **Результат:**\n"
                    f"• Пользователь потерял доступ к боту\n"
                    f"• Все назначения аккаунтов остаются (если были)"
                )
                
                # Пытаемся отправить уведомление пользователю
                try:
                    notification_text = (
                        f"⚠️ **Ваша роль в системе мониторинга была удалена**\n\n"
                        f"У вас больше нет доступа к боту.\n\n"
                        f"Для восстановления доступа обратитесь к администратору."
                    )
                    await message.bot.send_message(target_user_id, notification_text, parse_mode='Markdown')
                    text += f"\n\n📨 Уведомление отправлено пользователю"
                except Exception as e:
                    logger.warning(f"Не удалось отправить уведомление пользователю {target_user_id}: {e}")
                    text += f"\n\n⚠️ Не удалось отправить уведомление пользователю"
                
                await message.answer(text, parse_mode='Markdown')
                logger.info(f"Администратор {admin_user_id} удалил роль у пользователя {target_user_id}")
            else:
                await message.answer("❌ Ошибка при удалении роли")
            
            await state.clear()
            
        except ValueError:
            await message.answer("❌ Введите корректный числовой ID пользователя")
        except Exception as e:
            logger.error(f"Ошибка удаления роли: {e}")
            await message.answer("❌ Произошла ошибка при удалении роли")
            await state.clear()
    
    @dp.callback_query(lambda c: c.data == "view_all_roles")
    async def view_all_roles(callback: types.CallbackQuery):
        """Просмотр всех пользователей с ролями"""
        await callback.answer()
        
        user_id = callback.from_user.id
        from src.config.environment import env_config
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return
        
        all_users = roles_manager.get_all_users_with_roles()
        
        if not all_users:
            await callback.message.edit_text(
                "ℹ️ **Нет пользователей с ролями**",
                parse_mode='Markdown'
            )
            return
        
        text = f"👥 **Все пользователи с ролями**\n\n"
        
        # Группируем по ролям
        admins = []
        teamleads = []
        
        for user_id_str, user_data in all_users.items():
            role = user_data.get('role')
            source = user_data.get('source', 'config')
            
            if role == "admin":
                admins.append((user_id_str, user_data, source))
            elif role == "teamlead":
                teamleads.append((user_id_str, user_data, source))
        
        # Показываем администраторов
        if admins:
            text += f"👑 **Администраторы ({len(admins)}):**\n"
            for user_id_str, user_data, source in admins:
                source_text = "из .env" if source == "env" else "из конфига"
                text += f"• {user_id_str} ({source_text})\n"
            text += "\n"
        
        # Показываем тимлидов
        if teamleads:
            text += f"👤 **Тимлиды ({len(teamleads)}):**\n"
            for user_id_str, user_data, source in teamleads:
                granted_by = user_data.get('granted_by', 'Неизвестно')
                granted_at = user_data.get('granted_at', 'Неизвестно')
                if granted_at != 'Неизвестно' and granted_at != 'system':
                    try:
                        granted_at = datetime.fromisoformat(granted_at).strftime('%Y-%m-%d %H:%M')
                    except:
                        pass
                text += f"• {user_id_str} (назначил: {granted_by}, {granted_at})\n"
        
        # Статистика
        stats = roles_manager.get_statistics()
        text += f"\n📊 **Итого:** {stats.get('total_users_with_access', 0)} пользователей с доступом"
        
        from ..keyboards import back_to_roles_management_keyboard
        await callback.message.edit_text(text, reply_markup=back_to_roles_management_keyboard(), parse_mode='Markdown')
    
    @dp.callback_query(lambda c: c.data == "back_to_roles_management")
    async def back_to_roles_management(callback: types.CallbackQuery):
        """Возврат к главному меню управления ролями"""
        await callback.answer()
        await roles_management_menu(callback.message)

    logger.info("Обработчики управления ролями зарегистрированы")

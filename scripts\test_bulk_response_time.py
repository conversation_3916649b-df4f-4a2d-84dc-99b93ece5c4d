#!/usr/bin/env python3
"""
Тест массовых настроек времени ответа
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.response_time_manager import response_time_manager

async def test_bulk_response_time_settings():
    """Тест массовых настроек времени ответа"""
    print("🧪 Тест массовых настроек времени ответа")
    print("=" * 60)
    
    # Тестовые данные
    test_admin_id = *********
    test_accounts = [
        "+************",
        "+************", 
        "+************",
        "+************",
        "+************"
    ]
    
    print(f"👨‍💼 Администратор: {test_admin_id}")
    print(f"📱 Тестовых аккаунтов: {len(test_accounts)}")
    
    # Показываем начальное состояние
    print(f"\n📊 Начальное состояние:")
    settings = response_time_manager.get_all_settings()
    account_settings = settings.get("account_specific_settings", {})
    default_time = response_time_manager.default_response_time_minutes
    
    print(f"   ⏰ Время по умолчанию: {default_time} минут")
    print(f"   📱 Аккаунтов с индивидуальными настройками: {len(account_settings)}")
    
    for phone in test_accounts:
        current_time = response_time_manager.get_response_time_for_account(phone)
        has_custom = phone in account_settings
        status = "индивидуальное" if has_custom else "по умолчанию"
        print(f"   • {phone}: {current_time} мин ({status})")
    
    # Тест 1: Массовая установка времени ответа
    print(f"\n1️⃣ Тест массовой установки времени ответа...")
    
    new_time = 15  # 15 минут
    print(f"   Устанавливаем {new_time} минут для всех аккаунтов...")
    
    result = response_time_manager.set_response_time_for_all_accounts(
        minutes=new_time,
        account_phones=test_accounts,
        updated_by=test_admin_id
    )
    
    print(f"   Результат операции:")
    print(f"   • Успех: {'✅ Да' if result['success'] else '❌ Нет'}")
    
    if result['success']:
        print(f"   • Обработано: {result['processed_count']}/{result['total_accounts']}")
        if result.get('failed_phones'):
            print(f"   • Ошибки: {len(result['failed_phones'])} аккаунтов")
            for phone in result['failed_phones']:
                print(f"     - {phone}")
    else:
        print(f"   • Ошибка: {result.get('error', 'Неизвестная ошибка')}")
    
    # Проверяем результат
    print(f"\n   Проверка результата:")
    updated_settings = response_time_manager.get_all_settings()
    updated_account_settings = updated_settings.get("account_specific_settings", {})
    
    success_count = 0
    for phone in test_accounts:
        current_time = response_time_manager.get_response_time_for_account(phone)
        is_correct = current_time == new_time
        has_setting = phone in updated_account_settings
        
        status = "✅" if is_correct else "❌"
        print(f"   • {phone}: {current_time} мин {status} (настройка: {'есть' if has_setting else 'нет'})")
        
        if is_correct:
            success_count += 1
    
    print(f"   📊 Успешно установлено: {success_count}/{len(test_accounts)}")
    
    # Тест 2: Проверка валидации
    print(f"\n2️⃣ Тест валидации времени ответа...")
    
    invalid_times = [-5, 0, 1441, 9999]
    
    for invalid_time in invalid_times:
        print(f"   Тестируем некорректное время: {invalid_time} минут...")
        
        result = response_time_manager.set_response_time_for_all_accounts(
            minutes=invalid_time,
            account_phones=test_accounts[:2],  # Только первые 2 аккаунта
            updated_by=test_admin_id
        )
        
        should_fail = invalid_time < 1 or invalid_time > 1440
        actually_failed = not result['success']
        
        validation_correct = should_fail == actually_failed
        status = "✅" if validation_correct else "❌"
        
        print(f"     Результат: {'отклонено' if actually_failed else 'принято'} {status}")
        
        if actually_failed and 'error' in result:
            print(f"     Ошибка: {result['error']}")
    
    # Тест 3: Массовая очистка настроек
    print(f"\n3️⃣ Тест массовой очистки настроек...")
    
    # Сначала убеждаемся, что у нас есть индивидуальные настройки
    current_settings = response_time_manager.get_all_settings()
    current_account_settings = current_settings.get("account_specific_settings", {})
    accounts_with_settings = [phone for phone in test_accounts if phone in current_account_settings]
    
    print(f"   Аккаунтов с индивидуальными настройками: {len(accounts_with_settings)}")
    
    if accounts_with_settings:
        print(f"   Очищаем настройки для {len(accounts_with_settings)} аккаунтов...")
        
        clear_result = response_time_manager.clear_all_account_settings(
            account_phones=accounts_with_settings,
            updated_by=test_admin_id
        )
        
        print(f"   Результат очистки:")
        print(f"   • Успех: {'✅ Да' if clear_result['success'] else '❌ Нет'}")
        
        if clear_result['success']:
            print(f"   • Очищено: {clear_result['cleared_count']}/{clear_result['total_accounts']}")
            if clear_result.get('not_found_phones'):
                print(f"   • Не найдено: {len(clear_result['not_found_phones'])} аккаунтов")
        else:
            print(f"   • Ошибка: {clear_result.get('error', 'Неизвестная ошибка')}")
        
        # Проверяем результат очистки
        print(f"\n   Проверка результата очистки:")
        cleared_settings = response_time_manager.get_all_settings()
        cleared_account_settings = cleared_settings.get("account_specific_settings", {})
        default_time_after_clear = response_time_manager.default_response_time_minutes
        
        clear_success_count = 0
        for phone in accounts_with_settings:
            current_time = response_time_manager.get_response_time_for_account(phone)
            has_setting = phone in cleared_account_settings
            uses_default = current_time == default_time_after_clear and not has_setting
            
            status = "✅" if uses_default else "❌"
            print(f"   • {phone}: {current_time} мин {status} (настройка: {'есть' if has_setting else 'удалена'})")
            
            if uses_default:
                clear_success_count += 1
        
        print(f"   📊 Успешно очищено: {clear_success_count}/{len(accounts_with_settings)}")
    else:
        print(f"   ⚠️ Нет аккаунтов с индивидуальными настройками для очистки")
    
    # Тест 4: Обработка пустого списка аккаунтов
    print(f"\n4️⃣ Тест обработки пустого списка...")
    
    empty_result = response_time_manager.set_response_time_for_all_accounts(
        minutes=10,
        account_phones=[],
        updated_by=test_admin_id
    )
    
    empty_handled_correctly = not empty_result['success'] and 'пуст' in empty_result.get('error', '')
    print(f"   Пустой список обработан корректно: {'✅ Да' if empty_handled_correctly else '❌ Нет'}")
    
    if not empty_handled_correctly:
        print(f"   Результат: {empty_result}")
    
    # Тест 5: Проверка логирования
    print(f"\n5️⃣ Проверка метаданных...")
    
    final_settings = response_time_manager.get_all_settings()
    last_updated = final_settings.get('last_updated')
    updated_by = final_settings.get('updated_by')
    
    print(f"   Последнее обновление: {last_updated}")
    print(f"   Обновлено пользователем: {updated_by}")
    
    metadata_correct = updated_by == test_admin_id and last_updated is not None
    print(f"   Метаданные корректны: {'✅ Да' if metadata_correct else '❌ Нет'}")
    
    # Итоговая статистика
    print(f"\n" + "=" * 60)
    print(f"📊 ИТОГОВАЯ СТАТИСТИКА")
    print(f"=" * 60)
    
    final_settings = response_time_manager.get_all_settings()
    final_account_settings = final_settings.get("account_specific_settings", {})
    final_default_time = response_time_manager.default_response_time_minutes
    
    print(f"⏰ Время по умолчанию: {final_default_time} минут")
    print(f"📱 Всего тестовых аккаунтов: {len(test_accounts)}")
    print(f"🔧 Аккаунтов с индивидуальными настройками: {len(final_account_settings)}")
    
    print(f"\n📋 Финальные настройки:")
    for phone in test_accounts:
        current_time = response_time_manager.get_response_time_for_account(phone)
        has_custom = phone in final_account_settings
        status = "индивидуальное" if has_custom else "по умолчанию"
        print(f"   • {phone}: {current_time} мин ({status})")
    
    print(f"\n✅ Тестирование массовых настроек завершено!")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_bulk_response_time_settings())
    sys.exit(0 if success else 1)

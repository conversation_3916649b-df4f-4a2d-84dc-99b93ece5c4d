"""
Обработчики callback-кнопок для управления тимлидами
"""

import logging
from datetime import datetime
from aiogram import Dispatcher, types
from aiogram.fsm.context import FSMContext
from aiogram.filters.callback_data import CallbackData

from ..states import TeamleadManagement
from ..keyboards import (
    teamlead_management_keyboard, 
    teamlead_selection_keyboard,
    phone_selection_keyboard,
    teamlead_confirmation_keyboard,
    get_keyboard
)
from src.config.environment import env_config
from src.config.teamleads_manager import teamleads_manager
from src.utils.logging import setup_logging

logger = setup_logging(__name__, 'teamlead_callbacks.log')


def register_teamlead_callback_handlers(dp: Dispatcher, telethon_manager):
    """Регистрация обработчиков callback-кнопок для управления тимлидами"""
    
    @dp.callback_query(lambda c: c.data == "teamlead_management")
    async def show_teamlead_management(callback: types.CallbackQuery):
        """Показать главное меню управления тимлидами"""
        user_id = callback.from_user.id
        
        # Только для главных администраторов
        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return
        
        # Получаем статистику
        all_teamleads = teamleads_manager.get_all_teamleads()
        all_assignments = teamleads_manager.get_all_assignments()
        
        # Получаем доступные номера телефонов
        connected_accounts = telethon_manager.get_connected_accounts()
        available_phones = [acc['phone'] for acc in connected_accounts]
        
        text = (
            f"👥 **Управление тимлидами**\n\n"
            f"📊 **Статистика:**\n"
            f"• Тимлидов: {len(all_teamleads)}\n"
            f"• Назначений: {len(all_assignments)}\n"
            f"• Доступных номеров: {len(available_phones)}\n\n"
            f"Выберите действие:"
        )
        
        await callback.message.edit_text(
            text, 
            parse_mode='Markdown',
            reply_markup=teamlead_management_keyboard()
        )
        await callback.answer()

    @dp.callback_query(lambda c: c.data == "teamlead_add")
    async def start_add_teamlead(callback: types.CallbackQuery, state: FSMContext):
        """Начать процесс добавления тимлида через inline-кнопку"""
        user_id = callback.from_user.id

        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return

        text = (
            "👤 Добавление нового тимлида\n\n"
            "Введите никнейм тимлида (без @):\n"
            "Пример: тимлид1\n\n"
            "💡 Система автоматически найдет пользователя по никнейму"
        )

        await callback.message.edit_text(text)
        await state.set_state(TeamleadManagement.waiting_for_teamlead_username)
        await callback.answer()
    
    @dp.callback_query(lambda c: c.data == "teamlead_list")
    async def show_teamlead_list(callback: types.CallbackQuery):
        """Показать список всех тимлидов"""
        user_id = callback.from_user.id
        
        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return
        
        all_teamleads = teamleads_manager.get_all_teamleads()
        
        if not all_teamleads:
            text = "📝 Список тимлидов пуст.\n\nИспользуйте кнопку \"➕ Добавить тимлида\" для добавления."
        else:
            text = "👥 **Список всех тимлидов:**\n\n"
            
            for i, (username, data) in enumerate(all_teamleads.items(), 1):
                assigned_phones = data.get('assigned_phones', [])
                user_id_info = data.get('user_id', 'Неизвестно')
                
                text += f"**{i}. {username}**\n"
                text += f"🆔 ID: {user_id_info}\n"
                text += f"📱 Назначено номеров: {len(assigned_phones)}\n"
                
                if assigned_phones:
                    text += f"📞 Номера: {', '.join(assigned_phones[:3])}"
                    if len(assigned_phones) > 3:
                        text += f" и еще {len(assigned_phones) - 3}"
                    text += "\n"
                
                text += "\n"
        
        await callback.message.edit_text(
            text, 
            parse_mode='Markdown',
            reply_markup=teamlead_management_keyboard()
        )
        await callback.answer()
    
    @dp.callback_query(lambda c: c.data == "teamlead_view_assignments")
    async def show_assignments(callback: types.CallbackQuery):
        """Показать все назначения номеров"""
        user_id = callback.from_user.id
        
        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return
        
        all_assignments = teamleads_manager.get_all_assignments()
        
        if not all_assignments:
            text = "📝 Назначений пока нет.\n\nИспользуйте \"📱 Назначить номера\" для создания назначений."
        else:
            text = "📱 **Назначения номеров тимлидам:**\n\n"
            
            for phone, teamleads in all_assignments.items():
                text += f"**📞 {phone}**\n"
                text += f"👥 Тимлиды: {', '.join(teamleads)}\n\n"
            
            # Добавляем статистику
            total_phones = len(all_assignments)
            total_assignments = sum(len(teamleads) for teamleads in all_assignments.values())
            
            text += f"📊 **Статистика:**\n"
            text += f"• Номеров с назначениями: {total_phones}\n"
            text += f"• Всего назначений: {total_assignments}"
        
        await callback.message.edit_text(
            text, 
            parse_mode='Markdown',
            reply_markup=teamlead_management_keyboard()
        )
        await callback.answer()
    
    @dp.callback_query(lambda c: c.data == "teamlead_assign_phones")
    async def start_phone_assignment(callback: types.CallbackQuery):
        """Начать процесс назначения номеров"""
        user_id = callback.from_user.id
        
        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return
        
        all_teamleads = teamleads_manager.get_all_teamleads()
        
        if not all_teamleads:
            await callback.answer(
                "❌ Нет доступных тимлидов. Сначала добавьте тимлида.", 
                show_alert=True
            )
            return
        
        text = "👤 **Выберите тимлида для назначения номеров:**"
        
        await callback.message.edit_text(
            text, 
            parse_mode='Markdown',
            reply_markup=teamlead_selection_keyboard(all_teamleads, "assign")
        )
        await callback.answer()
    
    @dp.callback_query(lambda c: c.data.startswith("teamlead_assign:"))
    async def select_phones_for_teamlead(callback: types.CallbackQuery):
        """Выбор номеров для назначения тимлиду"""
        user_id = callback.from_user.id
        
        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return
        
        username = callback.data.split(":", 1)[1]
        
        # Получаем доступные номера телефонов
        connected_accounts = telethon_manager.get_connected_accounts()
        available_phones = [acc['phone'] for acc in connected_accounts]
        
        if not available_phones:
            await callback.answer(
                "❌ Нет доступных номеров телефонов.", 
                show_alert=True
            )
            return
        
        # Получаем уже назначенные номера этому тимлиду
        assigned_phones = teamleads_manager.get_phones_for_teamlead(username)
        
        text = (
            f"📱 **Выберите номера для тимлида {username}:**\n\n"
            f"✅ - назначен\n"
            f"⭕ - не назначен\n\n"
            f"Нажмите на номера для переключения состояния:"
        )
        
        await callback.message.edit_text(
            text, 
            parse_mode='Markdown',
            reply_markup=phone_selection_keyboard(available_phones, assigned_phones)
        )
        await callback.answer()
    
    @dp.callback_query(lambda c: c.data == "teamlead_remove")
    async def start_teamlead_removal(callback: types.CallbackQuery):
        """Начать процесс удаления тимлида"""
        user_id = callback.from_user.id
        
        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return
        
        all_teamleads = teamleads_manager.get_all_teamleads()
        
        if not all_teamleads:
            await callback.answer(
                "❌ Нет тимлидов для удаления.", 
                show_alert=True
            )
            return
        
        text = "⚠️ **Выберите тимлида для удаления:**\n\n❗ При удалении все назначения будут отменены!"
        
        await callback.message.edit_text(
            text, 
            parse_mode='Markdown',
            reply_markup=teamlead_selection_keyboard(all_teamleads, "remove")
        )
        await callback.answer()
    
    @dp.callback_query(lambda c: c.data.startswith("teamlead_remove:"))
    async def confirm_teamlead_removal(callback: types.CallbackQuery):
        """Подтверждение удаления тимлида"""
        user_id = callback.from_user.id
        
        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return
        
        username = callback.data.split(":", 1)[1]
        
        # Получаем информацию о тимлиде
        all_teamleads = teamleads_manager.get_all_teamleads()
        teamlead_data = all_teamleads.get(username)
        
        if not teamlead_data:
            await callback.answer("❌ Тимлид не найден", show_alert=True)
            return
        
        assigned_phones = teamlead_data.get('assigned_phones', [])
        
        text = (
            f"⚠️ **Подтверждение удаления**\n\n"
            f"👤 Тимлид: **{username}**\n"
            f"🆔 ID: {teamlead_data.get('user_id', 'Неизвестно')}\n"
            f"📱 Назначенных номеров: {len(assigned_phones)}\n"
        )
        
        if assigned_phones:
            text += f"📞 Номера: {', '.join(assigned_phones[:3])}"
            if len(assigned_phones) > 3:
                text += f" и еще {len(assigned_phones) - 3}"
            text += "\n"
        
        text += "\n❗ **Все назначения этого тимлида будут отменены!**"
        
        await callback.message.edit_text(
            text, 
            parse_mode='Markdown',
            reply_markup=teamlead_confirmation_keyboard("remove", username)
        )
        await callback.answer()
    
    @dp.callback_query(lambda c: c.data.startswith("teamlead_confirm_remove:"))
    async def execute_teamlead_removal(callback: types.CallbackQuery):
        """Выполнение удаления тимлида"""
        user_id = callback.from_user.id
        
        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return
        
        username = callback.data.split(":", 1)[1]
        
        # Удаляем тимлида
        success = teamleads_manager.remove_teamlead(username, user_id)
        
        if success:
            text = (
                f"✅ **Тимлид удален!**\n\n"
                f"👤 Тимлид: {username}\n"
                f"📅 Удален: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"🔄 Все назначения отменены"
            )
            logger.info(f"Администратор {user_id} удалил тимлида {username}")
        else:
            text = "❌ Ошибка при удалении тимлида."
        
        await callback.message.edit_text(
            text, 
            parse_mode='Markdown',
            reply_markup=teamlead_management_keyboard()
        )
        await callback.answer()
    
    @dp.callback_query(lambda c: c.data in ["teamlead_cancel", "teamlead_cancel_action", "teamlead_back_to_admin"])
    async def handle_teamlead_cancel(callback: types.CallbackQuery):
        """Обработка отмены действий с тимлидами"""
        user_id = callback.from_user.id
        
        if callback.data == "teamlead_back_to_admin":
            # Возврат в админку
            await callback.message.delete()
            await callback.message.answer(
                "👑 Административная панель",
                reply_markup=get_keyboard(user_id)
            )
        else:
            # Возврат в меню управления тимлидами
            await show_teamlead_management(callback)
        
        await callback.answer()

    @dp.callback_query(lambda c: c.data.startswith("phone_toggle:"))
    async def toggle_phone_assignment(callback: types.CallbackQuery, state: FSMContext):
        """Переключение назначения номера телефона"""
        user_id = callback.from_user.id

        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return

        phone = callback.data.split(":", 1)[1]

        # Получаем данные из состояния (если есть) или из сообщения
        data = await state.get_data()
        current_username = data.get('current_username')

        if not current_username:
            # Пытаемся извлечь username из текста сообщения
            message_text = callback.message.text
            if "тимлида " in message_text:
                try:
                    current_username = message_text.split("тимлида ")[1].split(":")[0].strip()
                except:
                    await callback.answer("❌ Ошибка определения тимлида", show_alert=True)
                    return
            else:
                await callback.answer("❌ Ошибка определения тимлида", show_alert=True)
                return

        # Получаем текущие назначения
        assigned_phones = teamleads_manager.get_phones_for_teamlead(current_username)

        # Переключаем состояние номера
        if phone in assigned_phones:
            # Отменяем назначение
            success = teamleads_manager.unassign_phone_from_teamlead(phone, current_username, user_id)
            action = "отменено"
        else:
            # Назначаем номер
            success = teamleads_manager.assign_phone_to_teamlead(phone, current_username, user_id)
            action = "назначено"

        if success:
            # Обновляем клавиатуру
            connected_accounts = telethon_manager.get_connected_accounts()
            available_phones = [acc['phone'] for acc in connected_accounts]
            updated_assigned_phones = teamleads_manager.get_phones_for_teamlead(current_username)

            text = (
                f"📱 **Выберите номера для тимлида {current_username}:**\n\n"
                f"✅ - назначен\n"
                f"⭕ - не назначен\n\n"
                f"Нажмите на номера для переключения состояния:"
            )

            await callback.message.edit_text(
                text,
                parse_mode='Markdown',
                reply_markup=phone_selection_keyboard(available_phones, updated_assigned_phones)
            )

            # Сохраняем username в состояние для следующих операций
            await state.update_data(current_username=current_username)

            await callback.answer(f"✅ Назначение {action}")
        else:
            await callback.answer(f"❌ Ошибка: назначение не {action}", show_alert=True)

    @dp.callback_query(lambda c: c.data == "phone_select_all")
    async def select_all_phones(callback: types.CallbackQuery, state: FSMContext):
        """Назначить все номера тимлиду"""
        user_id = callback.from_user.id

        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return

        # Получаем данные из состояния
        data = await state.get_data()
        current_username = data.get('current_username')

        if not current_username:
            # Пытаемся извлечь username из текста сообщения
            message_text = callback.message.text
            if "тимлида " in message_text:
                try:
                    current_username = message_text.split("тимлида ")[1].split(":")[0].strip()
                except:
                    await callback.answer("❌ Ошибка определения тимлида", show_alert=True)
                    return
            else:
                await callback.answer("❌ Ошибка определения тимлида", show_alert=True)
                return

        # Получаем все доступные номера
        connected_accounts = telethon_manager.get_connected_accounts()
        available_phones = [acc['phone'] for acc in connected_accounts]

        # Назначаем все номера
        success_count = 0
        for phone in available_phones:
            if teamleads_manager.assign_phone_to_teamlead(phone, current_username, user_id):
                success_count += 1

        # Обновляем клавиатуру
        updated_assigned_phones = teamleads_manager.get_phones_for_teamlead(current_username)

        text = (
            f"📱 **Выберите номера для тимлида {current_username}:**\n\n"
            f"✅ - назначен\n"
            f"⭕ - не назначен\n\n"
            f"Нажмите на номера для переключения состояния:"
        )

        await callback.message.edit_text(
            text,
            parse_mode='Markdown',
            reply_markup=phone_selection_keyboard(available_phones, updated_assigned_phones)
        )

        # Сохраняем username в состояние
        await state.update_data(current_username=current_username)

        await callback.answer(f"✅ Назначено {success_count} номеров")

    @dp.callback_query(lambda c: c.data == "phone_save_selection")
    async def save_phone_selection(callback: types.CallbackQuery, state: FSMContext):
        """Сохранение выбранных номеров"""
        user_id = callback.from_user.id

        if not env_config.is_admin(user_id):
            await callback.answer("❌ Доступ запрещен", show_alert=True)
            return

        # Получаем данные из состояния
        data = await state.get_data()
        current_username = data.get('current_username')

        if not current_username:
            # Пытаемся извлечь username из текста сообщения
            message_text = callback.message.text
            if "тимлида " in message_text:
                try:
                    current_username = message_text.split("тимлида ")[1].split(":")[0].strip()
                except:
                    await callback.answer("❌ Ошибка определения тимлида", show_alert=True)
                    return
            else:
                await callback.answer("❌ Ошибка определения тимлида", show_alert=True)
                return

        # Получаем текущие назначения
        assigned_phones = teamleads_manager.get_phones_for_teamlead(current_username)

        # Показываем результат сохранения
        text = (
            f"✅ **Номера успешно назначены!**\n\n"
            f"👤 **Тимлид:** {current_username}\n"
            f"📱 **Назначено номеров:** {len(assigned_phones)}\n"
        )

        if assigned_phones:
            text += f"📞 **Номера:** {', '.join(assigned_phones)}\n"

        text += f"\n📅 **Время:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        await callback.message.edit_text(
            text,
            parse_mode='Markdown',
            reply_markup=teamlead_management_keyboard()
        )

        await state.clear()
        await callback.answer("✅ Номера успешно назначены!")
        logger.info(f"Администратор {user_id} сохранил назначения для тимлида {current_username}: {assigned_phones}")

    @dp.callback_query(lambda c: c.data == "phone_cancel")
    async def cancel_phone_selection(callback: types.CallbackQuery, state: FSMContext):
        """Отмена выбора номеров"""
        await state.clear()
        await show_teamlead_management(callback)

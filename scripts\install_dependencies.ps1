# PM Searcher - Установка зависимостей (PowerShell)

Write-Host "🔍 PM Searcher - Установка зависимостей" -ForegroundColor Cyan
Write-Host "=" * 50

# Проверка Python
Write-Host "`n🐍 Проверка Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python найден: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python не найден! Установите Python 3.8+ с https://python.org" -ForegroundColor Red
    exit 1
}

# Проверка pip
Write-Host "`n📦 Проверка pip..." -ForegroundColor Yellow
try {
    $pipVersion = pip --version 2>&1
    Write-Host "✅ pip найден: $pipVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ pip не найден!" -ForegroundColor Red
    exit 1
}

# Обновление pip
Write-Host "`n📈 Обновление pip..." -ForegroundColor Yellow
try {
    python -m pip install --upgrade pip
    Write-Host "✅ pip обновлен" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Не удалось обновить pip, продолжаем..." -ForegroundColor Yellow
}

# Установка зависимостей
Write-Host "`n📦 Установка зависимостей..." -ForegroundColor Yellow

$packages = @(
    "aiogram>=3.4.0",
    "telethon>=1.30.0", 
    "python-dotenv>=1.0.0"
)

$successCount = 0

foreach ($package in $packages) {
    Write-Host "`n📦 Устанавливаем $package..." -ForegroundColor Cyan
    try {
        pip install $package
        Write-Host "✅ $package установлен успешно" -ForegroundColor Green
        $successCount++
    } catch {
        Write-Host "❌ Ошибка установки $package" -ForegroundColor Red
    }
}

# Результат
Write-Host "`n🏁 Установка завершена!" -ForegroundColor Cyan
Write-Host "✅ Успешно установлено: $successCount/$($packages.Count)" -ForegroundColor Green

if ($successCount -eq $packages.Count) {
    Write-Host "`n🎉 Все зависимости установлены успешно!" -ForegroundColor Green
    Write-Host "📋 Следующие шаги:" -ForegroundColor Yellow
    Write-Host "   1. Настройте файл data/.env"
    Write-Host "   2. Запустите тест: python scripts/test_system.py"
    Write-Host "   3. Запустите систему: python main.py"
} else {
    Write-Host "`n⚠️ Установлено только $successCount из $($packages.Count) пакетов" -ForegroundColor Yellow
    Write-Host "Проверьте ошибки выше и попробуйте установить недостающие пакеты вручную"
}

Write-Host "`nНажмите любую клавишу для выхода..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

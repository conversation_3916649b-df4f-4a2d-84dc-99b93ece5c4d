#!/usr/bin/env python3
"""
Автоматическая установка всех зависимостей для PM Searcher
"""

import subprocess
import sys
import os

def install_package(package):
    """Установка пакета через pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} установлен успешно")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Ошибка установки {package}: {e}")
        return False

def main():
    """Главная функция установки"""
    print("🔍 PM Searcher - Установка зависимостей")
    print("=" * 50)
    
    # Список пакетов для установки
    packages = [
        "aiogram>=3.4.0",
        "telethon>=1.30.0", 
        "python-dotenv>=1.0.0"
    ]
    
    print(f"📦 Будет установлено {len(packages)} пакетов:")
    for package in packages:
        print(f"   - {package}")
    
    print("\n🚀 Начинаем установку...")
    
    # Обновление pip
    print("\n📈 Обновление pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip обновлен")
    except subprocess.CalledProcessError:
        print("⚠️ Не удалось обновить pip, продолжаем...")
    
    # Установка пакетов
    success_count = 0
    for package in packages:
        print(f"\n📦 Устанавливаем {package}...")
        if install_package(package):
            success_count += 1
    
    print(f"\n🏁 Установка завершена!")
    print(f"✅ Успешно установлено: {success_count}/{len(packages)}")
    
    if success_count == len(packages):
        print("\n🎉 Все зависимости установлены успешно!")
        print("📋 Следующие шаги:")
        print("   1. Настройте файл data/.env")
        print("   2. Запустите тест: python scripts/test_system.py")
        print("   3. Запустите систему: python main.py")
    else:
        print(f"\n⚠️ Установлено только {success_count} из {len(packages)} пакетов")
        print("Проверьте ошибки выше и попробуйте установить недостающие пакеты вручную")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Тест для проверки унификации кнопки подключения аккаунта
"""

import sys
import os

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.bot.keyboards import accounts_management_keyboard


def test_empty_accounts_keyboard():
    """Тест клавиатуры при отсутствии аккаунтов"""
    print("🧪 Тестирование клавиатуры при отсутствии аккаунтов...")
    
    accounts = []
    user_id = *********
    
    keyboard = accounts_management_keyboard(accounts, user_id)
    
    # Проверяем, что клавиатура создана
    assert keyboard is not None
    assert hasattr(keyboard, 'inline_keyboard')
    
    # Получаем все кнопки
    all_buttons = []
    for row in keyboard.inline_keyboard:
        for button in row:
            all_buttons.append(button.text)
    
    print(f"   Кнопки в клавиатуре: {all_buttons}")
    
    # Проверяем, что есть кнопка "Подключить новый аккаунт"
    add_buttons = [btn for btn in all_buttons if "Подключить" in btn and "новый" in btn]
    assert len(add_buttons) == 1, f"Должна быть одна кнопка 'Подключить новый аккаунт', найдено: {add_buttons}"
    
    # Проверяем, что НЕТ кнопки "Подключить первый аккаунт"
    first_buttons = [btn for btn in all_buttons if "первый" in btn]
    assert len(first_buttons) == 0, f"Не должно быть кнопки 'Подключить первый аккаунт', найдено: {first_buttons}"
    
    print("   ✅ Тест пройден: только одна кнопка 'Подключить новый аккаунт'")


def test_with_accounts_keyboard():
    """Тест клавиатуры при наличии аккаунтов"""
    print("\n🧪 Тестирование клавиатуры при наличии аккаунтов...")
    
    accounts = [
        {
            'phone': '+***********',
            'monitoring': True
        },
        {
            'phone': '+***********',
            'monitoring': False
        }
    ]
    user_id = *********
    
    keyboard = accounts_management_keyboard(accounts, user_id)
    
    # Проверяем, что клавиатура создана
    assert keyboard is not None
    assert hasattr(keyboard, 'inline_keyboard')
    
    # Получаем все кнопки
    all_buttons = []
    for row in keyboard.inline_keyboard:
        for button in row:
            all_buttons.append(button.text)
    
    print(f"   Кнопки в клавиатуре: {all_buttons}")
    
    # Проверяем, что есть кнопки аккаунтов
    account_buttons = [btn for btn in all_buttons if "+7999" in btn]
    assert len(account_buttons) == 2, f"Должно быть 2 кнопки аккаунтов, найдено: {len(account_buttons)}"
    
    # Проверяем, что есть кнопка "Подключить новый аккаунт"
    add_buttons = [btn for btn in all_buttons if "Подключить" in btn and "новый" in btn]
    assert len(add_buttons) == 1, f"Должна быть одна кнопка 'Подключить новый аккаунт', найдено: {add_buttons}"
    
    # Проверяем, что НЕТ кнопки "Подключить первый аккаунт"
    first_buttons = [btn for btn in all_buttons if "первый" in btn]
    assert len(first_buttons) == 0, f"Не должно быть кнопки 'Подключить первый аккаунт', найдено: {first_buttons}"
    
    print("   ✅ Тест пройден: есть кнопки аккаунтов и одна кнопка 'Подключить новый аккаунт'")


def test_callback_data_consistency():
    """Тест консистентности callback_data"""
    print("\n🧪 Тестирование консистентности callback_data...")
    
    # Тест с пустым списком
    empty_keyboard = accounts_management_keyboard([], *********)
    
    # Тест с аккаунтами
    accounts_keyboard = accounts_management_keyboard([
        {'phone': '+***********', 'monitoring': True}
    ], *********)
    
    # Получаем все callback_data для кнопок добавления
    empty_add_callbacks = []
    accounts_add_callbacks = []
    
    for row in empty_keyboard.inline_keyboard:
        for button in row:
            if "Подключить" in button.text:
                empty_add_callbacks.append(button.callback_data)
    
    for row in accounts_keyboard.inline_keyboard:
        for button in row:
            if "Подключить" in button.text:
                accounts_add_callbacks.append(button.callback_data)
    
    print(f"   Callback_data в пустой клавиатуре: {empty_add_callbacks}")
    print(f"   Callback_data в клавиатуре с аккаунтами: {accounts_add_callbacks}")
    
    # Проверяем, что используется только add_new_account
    assert all("add_new_account" in cb for cb in empty_add_callbacks), "Все кнопки должны использовать add_new_account"
    assert all("add_new_account" in cb for cb in accounts_add_callbacks), "Все кнопки должны использовать add_new_account"
    
    # Проверяем, что нет add_first_account
    all_callbacks = empty_add_callbacks + accounts_add_callbacks
    assert not any("add_first_account" in cb for cb in all_callbacks), "Не должно быть callback_data add_first_account"
    
    print("   ✅ Тест пройден: используется только callback_data 'add_new_account'")


def main():
    """Основная функция тестирования"""
    print("🚀 Запуск тестов унификации кнопки подключения аккаунта")
    print("=" * 60)
    
    try:
        # Тест с пустым списком аккаунтов
        test_empty_accounts_keyboard()
        
        # Тест с аккаунтами
        test_with_accounts_keyboard()
        
        # Тест консистентности callback_data
        test_callback_data_consistency()
        
        print("\n" + "=" * 60)
        print("📊 Результаты тестирования:")
        print("✅ Клавиатура с пустым списком аккаунтов работает корректно")
        print("✅ Клавиатура с аккаунтами работает корректно")
        print("✅ Callback_data консистентны")
        
        print("\n🎉 Все тесты пройдены! Унификация кнопки работает корректно.")
        print("\n💡 Теперь всегда показывается только одна кнопка:")
        print("   '➕ Подключить новый аккаунт' независимо от количества аккаунтов")
            
    except Exception as e:
        print(f"\n❌ Ошибка при выполнении тестов: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

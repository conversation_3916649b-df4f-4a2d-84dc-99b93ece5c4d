#!/usr/bin/env python3
"""
Утилита для миграции существующих аккаунтов в новую систему персистентности
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.settings import ACCOUNTS_METADATA_FILE, SESSIONS_DIR
from src.config.environment import env_config
from telethon import TelegramClient

async def migrate_existing_accounts():
    """Миграция существующих аккаунтов"""
    print("🔄 Миграция существующих аккаунтов в новую систему")
    print("=" * 60)
    
    if not SESSIONS_DIR.exists():
        print("❌ Директория сессий не найдена")
        return
    
    session_files = list(SESSIONS_DIR.glob('*.session'))
    print(f"📁 Найдено файлов сессий: {len(session_files)}")
    
    if len(session_files) == 0:
        print("ℹ️  Нет файлов сессий для миграции")
        return
    
    metadata = {}
    migrated_count = 0
    
    for session_file in session_files:
        try:
            # Извлекаем user_id из имени файла (поддерживаем форматы: user_id.session и account_user_id.session)
            stem = session_file.stem
            if stem.startswith('account_'):
                user_id = int(stem.replace('account_', ''))
            else:
                user_id = int(stem)
            print(f"\n🔍 Обработка аккаунта {user_id}...")

            # Создаем временный клиент для получения информации
            client = TelegramClient(str(session_file), env_config.API_ID, env_config.API_HASH)
            
            await client.connect()
            
            if await client.is_user_authorized():
                # Получаем информацию о пользователе
                me = await client.get_me()
                phone = me.phone if me.phone else f"+{user_id}"  # Fallback если номер недоступен
                
                metadata[str(user_id)] = {
                    "phone": phone,
                    "monitoring": True,  # По умолчанию включаем мониторинг
                    "last_updated": datetime.now().isoformat(),
                    "migrated": True
                }
                
                print(f"✅ Аккаунт {phone} (ID: {user_id}) готов к миграции")
                migrated_count += 1
                
            else:
                print(f"⚠️  Сессия для пользователя {user_id} не авторизована")
                
                # Все равно добавляем в метаданные с неизвестным номером
                metadata[str(user_id)] = {
                    "phone": f"unknown_{user_id}",
                    "monitoring": False,
                    "last_updated": datetime.now().isoformat(),
                    "migrated": True,
                    "needs_reauth": True
                }
            
            await client.disconnect()
            
        except ValueError:
            print(f"❌ Некорректное имя файла сессии: {session_file.name}")
        except Exception as e:
            print(f"❌ Ошибка обработки {session_file.name}: {e}")
    
    # Сохраняем метаданные
    if metadata:
        print(f"\n💾 Сохранение метаданных {len(metadata)} аккаунтов...")
        
        # Создаем резервную копию если файл уже существует
        if ACCOUNTS_METADATA_FILE.exists():
            backup_file = ACCOUNTS_METADATA_FILE.with_suffix('.backup')
            ACCOUNTS_METADATA_FILE.rename(backup_file)
            print(f"📋 Создана резервная копия: {backup_file}")
        
        with open(ACCOUNTS_METADATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Метаданные сохранены в {ACCOUNTS_METADATA_FILE}")
        
        # Показываем содержимое
        print("\n📄 Содержимое файла метаданных:")
        with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    
    print("\n" + "=" * 60)
    print(f"🎉 Миграция завершена!")
    print(f"📊 Обработано аккаунтов: {migrated_count}")
    print(f"📁 Файл метаданных: {ACCOUNTS_METADATA_FILE}")
    print("\n💡 Теперь можно запускать бота - аккаунты восстановятся автоматически!")

if __name__ == "__main__":
    asyncio.run(migrate_existing_accounts())

"""
Основные настройки проекта
"""

from pathlib import Path

# Корневая папка проекта
PROJECT_ROOT = Path(__file__).parent.parent.parent

# Папки данных
DATA_DIR = PROJECT_ROOT / 'data'
SESSIONS_DIR = DATA_DIR / 'sessions'
LOGS_DIR = DATA_DIR / 'logs'

# Файлы конфигурации
STOPWORDS_FILE = DATA_DIR / 'stopwords.txt'
ACCOUNTS_METADATA_FILE = DATA_DIR / 'accounts_metadata.json'  # Метаданные подключенных аккаунтов
MONITORING_CONFIG_FILE = DATA_DIR / 'monitoring_config.json'  # Настройки мониторинга
TEAMLEADS_CONFIG_FILE = DATA_DIR / 'teamleads_config.json'  # Конфигурация тимлидов и их назначений
RESPONSE_TIME_CONFIG_FILE = DATA_DIR / 'response_time_config.json'  # Настройки мониторинга времени ответа
ENV_FILE = DATA_DIR / '.env'

# Настройки логирования
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_LEVEL = 'INFO'

# Настройки мониторинга
RECONNECT_ATTEMPTS = 3
RECONNECT_DELAY = 30  # секунд

# Создание необходимых папок
def ensure_directories():
    """Создание необходимых папок если их нет"""
    for directory in [DATA_DIR, SESSIONS_DIR, LOGS_DIR]:
        directory.mkdir(parents=True, exist_ok=True)

# Автоматическое создание папок при импорте
ensure_directories()

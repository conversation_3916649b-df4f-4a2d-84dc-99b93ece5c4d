#!/usr/bin/env python3
"""
Тест системы мониторинга без предупреждений - только алерты о просрочке
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.monitoring.response_tracker import ResponseTracker
from src.config.response_time_manager import response_time_manager

async def test_no_warnings_system():
    """Тест системы без предупреждений"""
    print("🧪 ТЕСТ СИСТЕМЫ БЕЗ ПРЕДУПРЕЖДЕНИЙ")
    print("=" * 50)
    
    # 1. Проверяем конфигурацию
    print("\n1️⃣ ПРОВЕРКА КОНФИГУРАЦИИ:")
    print("-" * 30)
    
    print(f"✅ Мониторинг включен: {response_time_manager.enabled}")
    print(f"✅ Время ответа по умолчанию: {response_time_manager.default_response_time_minutes} мин")
    print(f"✅ Интервал проверки: {response_time_manager.check_interval_seconds} сек")
    
    # Проверяем, что alert_before_deadline_minutes больше не существует
    try:
        threshold = response_time_manager.alert_before_deadline_minutes
        print(f"❌ ОШИБКА: alert_before_deadline_minutes все еще существует: {threshold}")
    except AttributeError:
        print("✅ Порог предупреждения успешно удален из системы")
    
    # 2. Тестируем систему алертов
    print("\n2️⃣ ТЕСТИРОВАНИЕ СИСТЕМЫ АЛЕРТОВ:")
    print("-" * 30)
    
    alerts_received = []
    
    async def test_alert_callback(phone: str, alert_text: str):
        alert = {
            'phone': phone,
            'alert': alert_text,
            'timestamp': datetime.now(),
            'is_expired': "🚨 ПРОСРОЧЕН ОТВЕТ МЕНЕДЖЕРА!" in alert_text
        }
        alerts_received.append(alert)
        print(f"   📨 АЛЕРТ: {phone}")
        print(f"      {alert_text[:80]}...")
    
    # Создаем трекер с тестовым callback
    tracker = ResponseTracker(test_alert_callback)
    
    # 3. Симулируем ситуацию с коротким дедлайном
    print("\n3️⃣ СИМУЛЯЦИЯ ПРОСРОЧКИ:")
    print("-" * 30)
    
    phone = "+***********"
    test_message_data = {
        'account_phone': phone,
        'message_id': 12345,
        'sender_id': *********,
        'sender_name': 'TestUser',
        'chat_id': *********,
        'chat_title': 'Test Chat',
        'text': 'Тестовое сообщение',
        'is_from_external': True,
        'chat_type': 'private',
        'is_bot': False
    }
    
    # Временно изменяем настройки для быстрого теста
    original_response_time = response_time_manager._config["account_specific_settings"].get(phone, {}).get("response_time_minutes", response_time_manager.default_response_time_minutes)
    original_check_interval = response_time_manager.check_interval_seconds
    
    # Устанавливаем короткие интервалы для теста
    response_time_manager._config["global_settings"]["check_interval_seconds"] = 2  # 2 секунды
    
    if phone not in response_time_manager._config["account_specific_settings"]:
        response_time_manager._config["account_specific_settings"][phone] = {}
    response_time_manager._config["account_specific_settings"][phone]["response_time_minutes"] = 0.1  # 6 секунд
    
    print(f"⏰ Установлены тестовые настройки:")
    print(f"   - Время ответа: 6 секунд")
    print(f"   - Интервал проверки: 2 секунды")
    print(f"   - БЕЗ предупреждений!")
    
    # Запускаем мониторинг
    await tracker.start_monitoring()
    print("✅ Мониторинг запущен")
    
    # Добавляем сообщение для отслеживания
    success = tracker.track_incoming_message(test_message_data)
    if success:
        print("✅ Сообщение добавлено для отслеживания")
        
        # Получаем информацию о добавленном сообщении
        pending_messages = tracker.get_pending_messages_for_account(phone)
        if pending_messages:
            msg = pending_messages[0]
            deadline = msg['deadline']
            if isinstance(deadline, str):
                deadline = datetime.fromisoformat(deadline)
            print(f"   📅 Дедлайн: {deadline.strftime('%H:%M:%S')}")
            print(f"   ⏱️  Осталось: ~6 секунд")
    else:
        print("❌ Не удалось добавить сообщение для отслеживания")
        return
    
    # Ждем и наблюдаем за алертами
    print("\n⏳ Ожидание алертов...")
    start_time = datetime.now()
    
    for i in range(10):  # Ждем 10 секунд
        await asyncio.sleep(1)
        elapsed = (datetime.now() - start_time).total_seconds()
        print(f"   {elapsed:.0f}с - Алертов получено: {len(alerts_received)}")
        
        # Показываем новые алерты
        if len(alerts_received) > 0:
            for alert in alerts_received:
                alert_type = "🚨 ПРОСРОЧКА" if alert['is_expired'] else "⚠️ ПРЕДУПРЕЖДЕНИЕ"
                print(f"      {alert_type} в {alert['timestamp'].strftime('%H:%M:%S')}")
            break
    
    # Останавливаем мониторинг
    await tracker.stop_monitoring()
    print("✅ Мониторинг остановлен")
    
    # Восстанавливаем оригинальные настройки
    response_time_manager._config["global_settings"]["check_interval_seconds"] = original_check_interval
    if phone in response_time_manager._config["account_specific_settings"]:
        response_time_manager._config["account_specific_settings"][phone]["response_time_minutes"] = original_response_time
    
    # 4. Анализ результатов
    print("\n4️⃣ АНАЛИЗ РЕЗУЛЬТАТОВ:")
    print("-" * 30)
    
    warning_alerts = [a for a in alerts_received if not a['is_expired']]
    expired_alerts = [a for a in alerts_received if a['is_expired']]
    
    print(f"📊 Всего алертов: {len(alerts_received)}")
    print(f"   ⚠️  Предупреждений: {len(warning_alerts)}")
    print(f"   🚨 Просрочек: {len(expired_alerts)}")
    
    if len(warning_alerts) > 0:
        print("\n❌ ОШИБКА: Система все еще отправляет предупреждения!")
        print("   Предупреждения должны быть полностью удалены")
    else:
        print("\n✅ Предупреждения успешно удалены из системы")
    
    if len(expired_alerts) > 0:
        print("✅ Алерты о просрочке работают корректно")
    else:
        print("❌ ПРОБЛЕМА: Алерты о просрочке не отправляются")
    
    # Итоговый результат
    print("\n" + "=" * 50)
    if len(warning_alerts) == 0 and len(expired_alerts) > 0:
        print("🎉 УСПЕХ: Система работает без предупреждений!")
        print("   ✅ Предупреждения удалены")
        print("   ✅ Алерты о просрочке работают")
    else:
        print("❌ ТРЕБУЕТСЯ ДОРАБОТКА")
        if len(warning_alerts) > 0:
            print("   - Удалить остатки системы предупреждений")
        if len(expired_alerts) == 0:
            print("   - Исправить алерты о просрочке")
    
    print("ТЕСТ ЗАВЕРШЕН")

if __name__ == "__main__":
    asyncio.run(test_no_warnings_system())

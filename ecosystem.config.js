module.exports = {
  apps: [
    {
      // Основная конфигурация приложения
      name: 'pm-searcher-bot',
      script: 'main.py',
      interpreter: 'python3',
      
      // Рабочая директория
      cwd: '/path/to/your/pm-searcher',
      
      // Переменные окружения
      env: {
        NODE_ENV: 'production',
        PYTHONPATH: '/path/to/your/pm-searcher',
        PYTHONUNBUFFERED: '1',
        // Telegram Bot настройки (можно задать здесь или использовать data/.env)
        // BOT_TOKEN: 'your_bot_token_here',
        // API_ID: 'your_api_id_here', 
        // API_HASH: 'your_api_hash_here',
        // ADMIN_CHAT_IDS: 'admin_id1,admin_id2'
      },
      
      // Настройки логирования
      log_file: './data/logs/pm2-combined.log',
      out_file: './data/logs/pm2-out.log',
      error_file: './data/logs/pm2-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Настройки перезапуска
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      restart_delay: 5000,
      
      // Настройки для обработки ошибок
      min_uptime: '10s',
      max_restarts: 10,
      
      // Настройки кластера (для Python обычно 1 инстанс)
      instances: 1,
      exec_mode: 'fork',
      
      // Дополнительные настройки
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Настройки мониторинга
      pmx: false,
      
      // Игнорировать определенные сигналы
      ignore_watch: [
        'node_modules',
        'data/logs',
        'data/sessions',
        '__pycache__',
        '*.pyc',
        '.git'
      ],
      
      // Дополнительные аргументы для интерпретатора
      interpreter_args: '-u',
      
      // Настройки для graceful shutdown
      kill_timeout: 10000,
      wait_ready: true,
      listen_timeout: 10000,
      
      // Merge logs from different instances
      merge_logs: true,
      
      // Настройки для cron restart (опционально)
      // cron_restart: '0 2 * * *', // Перезапуск каждый день в 2:00
      
      // Переменные для разработки
      env_development: {
        NODE_ENV: 'development',
        PYTHONPATH: '/path/to/your/pm-searcher',
        PYTHONUNBUFFERED: '1'
      },
      
      // Переменные для продакшена
      env_production: {
        NODE_ENV: 'production',
        PYTHONPATH: '/path/to/your/pm-searcher',
        PYTHONUNBUFFERED: '1'
      }
    }
  ],
  
  // Настройки деплоя (опционально)
  deploy: {
    production: {
      user: 'your_username',
      host: 'your_server_ip',
      ref: 'origin/main',
      repo: 'your_git_repository',
      path: '/path/to/deployment',
      'pre-deploy-local': '',
      'post-deploy': 'pip install -r requirements.txt && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};

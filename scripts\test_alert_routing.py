#!/usr/bin/env python3
"""
Тест маршрутизации алертов: предупреждения тимлидам, просрочки администраторам
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.bot.alerts import AlertManager
from src.config.teamleads_manager import teamleads_manager

class MockBot:
    """Mock бот для тестирования"""
    def __init__(self):
        self.sent_messages = []
    
    async def send_message(self, user_id: int, text: str):
        """Имитация отправки сообщения"""
        self.sent_messages.append({
            'user_id': user_id,
            'text': text,
            'timestamp': datetime.now(),
            'is_expired_alert': "🚨 ПРОСРОЧЕН ОТВЕТ МЕНЕДЖЕРА!" in text
        })
        print(f"   📨 Сообщение отправлено пользователю {user_id}")
        if "🚨 ПРОСРОЧЕН ОТВЕТ МЕНЕДЖЕРА!" in text:
            print(f"      🚨 АЛЕРТ О ПРОСРОЧКЕ")
        elif "⚠️ СКОРО ДЕДЛАЙН ОТВЕТА!" in text:
            print(f"      ⚠️ ПРЕДУПРЕЖДЕНИЕ")

async def test_alert_routing():
    """Тест маршрутизации алертов"""
    print("🧪 ТЕСТ МАРШРУТИЗАЦИИ АЛЕРТОВ")
    print("=" * 50)
    
    # Создаем mock бот и AlertManager
    mock_bot = MockBot()
    alert_manager = AlertManager(mock_bot)
    
    # Тестовый номер телефона
    phone = "+***********"
    
    # Проверяем назначения тимлидов
    print(f"\n1️⃣ ПРОВЕРКА НАЗНАЧЕНИЙ ДЛЯ {phone}:")
    print("-" * 40)
    
    assigned_teamleads = teamleads_manager.get_teamleads_for_phone(phone)
    if assigned_teamleads:
        for teamlead in assigned_teamleads:
            print(f"✅ Тимлид: {teamlead.get('username')} (ID: {teamlead.get('user_id')})")
    else:
        print("❌ Тимлиды не назначены")
    
    # Тест 1: Предупреждение (должно идти тимлидам)
    print(f"\n2️⃣ ТЕСТ ПРЕДУПРЕЖДЕНИЯ:")
    print("-" * 40)
    
    warning_text = (
        f"⚠️ СКОРО ДЕДЛАЙН ОТВЕТА!\n\n"
        f"📱 Аккаунт: {phone}\n"
        f"👤 От: Mossad\n"
        f"💬 Чат: Test Chat\n"
        f"📝 Сообщение: Тестовое сообщение\n"
        f"⏰ Получено: 23:43:42\n"
        f"⚠️ Дедлайн: 23:48:42\n"
        f"🟡 Осталось: 0:01:30\n"
        f"📅 Дата: 2025-07-29"
    )
    
    mock_bot.sent_messages.clear()
    await alert_manager.send_response_time_alert(phone, warning_text)
    
    warning_messages = [msg for msg in mock_bot.sent_messages if not msg['is_expired_alert']]
    print(f"📊 Предупреждений отправлено: {len(warning_messages)}")
    
    for msg in warning_messages:
        print(f"   → Пользователю {msg['user_id']}")
    
    # Тест 2: Алерт о просрочке (должен идти всем администраторам)
    print(f"\n3️⃣ ТЕСТ АЛЕРТА О ПРОСРОЧКЕ:")
    print("-" * 40)
    
    expired_text = (
        f"🚨 ПРОСРОЧЕН ОТВЕТ МЕНЕДЖЕРА!\n\n"
        f"📱 Аккаунт: {phone}\n"
        f"👤 От: Mossad\n"
        f"💬 Чат: Test Chat\n"
        f"📝 Сообщение: Тестовое сообщение\n"
        f"⏰ Получено: 23:43:42\n"
        f"⚠️ Дедлайн: 23:48:42\n"
        f"🔴 Просрочено на: 0:05:30\n"
        f"📅 Дата: 2025-07-29"
    )
    
    mock_bot.sent_messages.clear()
    await alert_manager.send_response_time_alert(phone, expired_text)
    
    expired_messages = [msg for msg in mock_bot.sent_messages if msg['is_expired_alert']]
    print(f"📊 Алертов о просрочке отправлено: {len(expired_messages)}")
    
    for msg in expired_messages:
        print(f"   → Администратору {msg['user_id']}")
    
    # Анализ результатов
    print(f"\n4️⃣ АНАЛИЗ РЕЗУЛЬТАТОВ:")
    print("-" * 40)
    
    # Получаем список администраторов из конфигурации
    from src.config.environment import env_config
    admin_ids = [aid for aid in env_config.ADMIN_CHAT_IDS if aid > 0]
    
    print(f"📋 Администраторов в системе: {len(admin_ids)}")
    print(f"📋 Тимлидов назначено: {len(assigned_teamleads)}")
    
    if len(warning_messages) == len(assigned_teamleads):
        print("✅ Предупреждения корректно отправлены тимлидам")
    else:
        print("❌ Ошибка в отправке предупреждений тимлидам")
    
    if len(expired_messages) == len(admin_ids):
        print("✅ Алерты о просрочке корректно отправлены всем администраторам")
    else:
        print(f"❌ Ошибка в отправке алертов администраторам: отправлено {len(expired_messages)}, ожидалось {len(admin_ids)}")
    
    print(f"\n🎯 ИТОГ:")
    if (len(warning_messages) == len(assigned_teamleads) and 
        len(expired_messages) == len(admin_ids)):
        print("✅ Маршрутизация алертов работает корректно!")
        print("   - Предупреждения → Тимлиды")
        print("   - Просрочки → Все администраторы")
    else:
        print("❌ Обнаружены проблемы в маршрутизации алертов")
    
    print("\n" + "=" * 50)
    print("ТЕСТ ЗАВЕРШЕН")

if __name__ == "__main__":
    asyncio.run(test_alert_routing())

#!/usr/bin/env python3
"""
Скрипт для восстановления метаданных аккаунта из существующей сессии
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.settings import ACCOUNTS_METADATA_FILE, SESSIONS_DIR
from src.config.environment import env_config
from telethon import TelegramClient

async def restore_metadata():
    """Восстановление метаданных из существующих сессий"""
    print("🔍 Восстановление метаданных аккаунтов...")
    print("=" * 50)
    
    # Проверяем существование директории сессий
    if not SESSIONS_DIR.exists():
        print("❌ Директория сессий не найдена")
        return
    
    # Получаем список файлов сессий
    session_files = list(SESSIONS_DIR.glob('*.session'))
    print(f"📁 Найдено {len(session_files)} файлов сессий")
    
    if not session_files:
        print("❌ Файлы сессий не найдены")
        return
    
    metadata = {}
    restored_count = 0
    
    for session_file in session_files:
        try:
            # Извлекаем user_id из имени файла (поддерживаем форматы: user_id.session и account_user_id.session)
            stem = session_file.stem
            if stem.startswith('account_'):
                user_id = int(stem.replace('account_', ''))
            else:
                user_id = int(stem)
            print(f"\n🔍 Обработка аккаунта {user_id}...")

            # Создаем временный клиент для получения информации
            client = TelegramClient(str(session_file), env_config.API_ID, env_config.API_HASH)
            
            await client.connect()
            
            if await client.is_user_authorized():
                # Получаем информацию о пользователе
                me = await client.get_me()
                phone = me.phone if me.phone else f"+{user_id}"  # Fallback если номер недоступен
                
                metadata[str(user_id)] = {
                    "phone": phone,
                    "monitoring": True,  # По умолчанию включаем мониторинг
                    "last_updated": datetime.now().isoformat()
                }
                
                print(f"✅ Аккаунт {phone} (ID: {user_id}) восстановлен")
                restored_count += 1
                
            else:
                print(f"❌ Аккаунт {user_id} не авторизован")
            
            await client.disconnect()
            
        except ValueError:
            print(f"❌ Некорректное имя файла сессии: {session_file.name}")
        except Exception as e:
            print(f"❌ Ошибка обработки {session_file.name}: {e}")
    
    # Сохраняем метаданные
    if metadata:
        # Создаем backup существующего файла
        if ACCOUNTS_METADATA_FILE.exists():
            backup_path = ACCOUNTS_METADATA_FILE.with_suffix('.json.backup_restore')
            ACCOUNTS_METADATA_FILE.rename(backup_path)
            print(f"📦 Создан backup: {backup_path}")
        
        # Сохраняем новые метаданные
        with open(ACCOUNTS_METADATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Восстановлено {restored_count} аккаунтов")
        print(f"💾 Метаданные сохранены в {ACCOUNTS_METADATA_FILE}")
        
        # Показываем восстановленные аккаунты
        print("\n📋 Восстановленные аккаунты:")
        for user_id, data in metadata.items():
            print(f"   - User {user_id}: {data['phone']} (monitoring: {data['monitoring']})")
    else:
        print("\n❌ Не удалось восстановить ни одного аккаунта")

if __name__ == "__main__":
    asyncio.run(restore_metadata())

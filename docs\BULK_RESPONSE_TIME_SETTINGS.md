# 🔧 Массовые настройки времени ответа

## 🎯 Описание

Функциональность массовой настройки времени ответа позволяет администраторам быстро устанавливать одинаковое время ответа для всех подключенных аккаунтов или очищать индивидуальные настройки одной операцией.

## ✨ Возможности

### 🔧 Массовые операции
- **Установка времени для всех аккаунтов** - применение одинакового времени ответа ко всем подключенным аккаунтам
- **Сброс к настройкам по умолчанию** - удаление всех индивидуальных настроек
- **Подтверждение операций** - защита от случайных изменений
- **Детальная отчетность** - показ результатов операции

### 🛡️ Безопасность
- **Доступ только администраторам** - функция доступна только пользователям из ADMIN_CHAT_IDS
- **Подтверждение операций** - обязательное подтверждение перед выполнением
- **Логирование действий** - все операции записываются в логи
- **Уведомления администраторов** - автоматические уведомления о массовых изменениях

## 🎮 Использование

### 📱 Доступ к функции

1. **Откройте главное меню бота**
2. **Нажмите "⏰ Время ответа"**
3. **Выберите "📱 Настройки аккаунтов"**
4. **Нажмите "🔧 Массовые настройки"**

### ⏰ Массовая установка времени

1. **В меню массовых настроек нажмите "⏰ Установить время для всех"**
2. **Система покажет:**
   ```
   ⏰ Массовая установка времени ответа
   
   📊 Будет применено к 5 аккаунтам:
   • +************ (сейчас: 5 мин)
   • +************ (сейчас: 10 мин)
   • +************ (сейчас: 10 мин)
   • ... и еще 2 аккаунтов
   
   📝 Введите новое время ответа в минутах (от 1 до 1440):
   ```

3. **Введите желаемое время** (например, `15`)
4. **Подтвердите операцию:**
   ```
   ⚠️ Подтверждение массовой операции
   
   ⏰ Время ответа: 15 минут
   📱 Количество аккаунтов: 5
   
   ❗ Внимание: Эта операция перезапишет индивидуальные 
   настройки времени ответа для всех подключенных аккаунтов.
   
   Вы уверены, что хотите продолжить?
   ```

5. **Нажмите "✅ Да, установить для X аккаунтов"**

### 🗑️ Сброс к настройкам по умолчанию

1. **В меню массовых настроек нажмите "🗑️ Сбросить все к умолчанию"**
2. **Система покажет аккаунты с индивидуальными настройками:**
   ```
   🗑️ Сброс к настройкам по умолчанию
   
   📊 Аккаунтов с индивидуальными настройками: 3
   ⏰ Время по умолчанию: 10 минут
   
   📱 Аккаунты для сброса:
   • +************ (15 мин → 10 мин)
   • +************ (5 мин → 10 мин)
   • +************ (20 мин → 10 мин)
   ```

3. **Подтвердите операцию нажатием "✅ Да, сбросить X аккаунтов"**

## 🔧 Техническая реализация

### 📁 Новые методы в ResponseTimeManager

#### `set_response_time_for_all_accounts(minutes, account_phones, updated_by)`
```python
# Установка времени ответа для всех указанных аккаунтов
result = response_time_manager.set_response_time_for_all_accounts(
    minutes=15,
    account_phones=["+************", "+************"],
    updated_by=*********
)

# Возвращает:
{
    "success": True,
    "processed_count": 2,
    "failed_phones": [],
    "total_accounts": 2
}
```

#### `clear_all_account_settings(account_phones, updated_by)`
```python
# Очистка индивидуальных настроек для указанных аккаунтов
result = response_time_manager.clear_all_account_settings(
    account_phones=["+************", "+************"],
    updated_by=*********
)

# Возвращает:
{
    "success": True,
    "cleared_count": 2,
    "not_found_phones": [],
    "total_accounts": 2
}
```

### 🎨 Новые клавиатуры

#### `response_time_bulk_settings_keyboard()`
- Кнопка "⏰ Установить время для всех"
- Кнопка "🗑️ Сбросить все к умолчанию"
- Кнопка "◀️ Назад"

#### `bulk_operation_confirmation_keyboard(operation_type, accounts_count)`
- Кнопка подтверждения с указанием количества аккаунтов
- Кнопка "❌ Отмена"

### 🔄 Новые состояния FSM

#### `ResponseTimeManagement.waiting_for_bulk_response_time`
- Ожидание ввода времени для массовой установки

### 📝 Новые обработчики

1. **`response_time_bulk_settings`** - показ меню массовых настроек
2. **`bulk_set_response_time`** - начало процесса массовой установки
3. **`process_bulk_response_time`** - обработка ввода времени
4. **`confirm_bulk_set_response_time`** - подтверждение массовой установки
5. **`bulk_clear_response_time`** - начало процесса массовой очистки
6. **`confirm_bulk_clear_response_time`** - подтверждение массовой очистки

## 🚨 Система уведомлений

### 📢 Уведомления администраторов

При выполнении массовых операций все администраторы получают уведомления:

#### Массовая установка времени:
```
🔧 МАССОВАЯ НАСТРОЙКА ВРЕМЕНИ ОТВЕТА

👤 Администратор: *********
⏰ Установлено время: 15 минут
📱 Обработано аккаунтов: 5
🕐 Время: 2025-07-29 21:30:00
```

#### Массовая очистка настроек:
```
🗑️ МАССОВАЯ ОЧИСТКА НАСТРОЕК ВРЕМЕНИ ОТВЕТА

👤 Администратор: *********
🗑️ Очищено настроек: 3
⏰ Время по умолчанию: 10 минут
🕐 Время: 2025-07-29 21:30:00
```

### 📊 Результаты операций

После выполнения операции пользователь получает детальный отчет:

#### Успешная установка:
```
✅ Массовая установка завершена!

⏰ Установлено время: 15 минут
📱 Обработано аккаунтов: 5/5

🕐 Время операции: 21:30:15
```

#### Успешная очистка:
```
✅ Массовая очистка завершена!

🗑️ Очищено настроек: 3/3
⏰ Все аккаунты используют: 10 минут

🕐 Время операции: 21:30:15
```

## 🔍 Валидация и обработка ошибок

### ✅ Валидация входных данных
- **Время ответа:** от 1 до 1440 минут (24 часа)
- **Список аккаунтов:** не должен быть пустым
- **Права доступа:** только администраторы

### ❌ Обработка ошибок
- **Некорректное время:** отклонение с сообщением об ошибке
- **Пустой список:** информирование пользователя
- **Ошибки сохранения:** откат операции и уведомление
- **Частичные ошибки:** отчет о проблемных аккаунтах

## 📈 Логирование

### 📝 Записи в логах

Все массовые операции записываются в логи с детальной информацией:

```
2025-07-29 21:30:15 - INFO - Массовая установка времени ответа 15 мин для 5 аккаунтов администратором *********
2025-07-29 21:35:20 - INFO - Массовая очистка настроек времени ответа для 3 аккаунтов администратором *********
```

### 🔍 Отслеживание изменений

В конфигурационном файле сохраняются метаданные:
- `last_updated` - время последнего изменения
- `updated_by` - ID администратора, выполнившего операцию

## 🧪 Тестирование

### 🔬 Автоматические тесты

Система включает комплексные тесты:

```bash
# Тест массовых настроек
python scripts/test_bulk_response_time.py

# Тест системы алертов тимлидов
python scripts/test_teamlead_alerts.py
```

### ✅ Покрытие тестами

- ✅ Массовая установка времени ответа
- ✅ Массовая очистка настроек
- ✅ Валидация входных данных
- ✅ Обработка ошибок
- ✅ Логирование операций
- ✅ Система уведомлений
- ✅ Маршрутизация алертов по тимлидам

## 🎉 Заключение

Функциональность массовых настроек времени ответа обеспечивает:

- ✅ **Эффективное управление** - быстрая настройка множества аккаунтов
- ✅ **Безопасность операций** - подтверждения и валидация
- ✅ **Полная прозрачность** - детальные отчеты и логирование
- ✅ **Интеграция с системой тимлидов** - корректная маршрутизация алертов
- ✅ **Простота использования** - интуитивный интерфейс

Система готова к продуктивному использованию и полностью интегрирована в существующую архитектуру проекта.

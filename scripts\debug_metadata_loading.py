#!/usr/bin/env python3
"""
Отладочный скрипт для проверки загрузки метаданных
"""

import asyncio
import json
import sys
from pathlib import Path

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.settings import ACCOUNTS_METADATA_FILE, SESSIONS_DIR
from src.monitoring.telethon_manager import TelethonManager

async def debug_metadata():
    """Отладка загрузки метаданных"""
    print("🔍 Отладка загрузки метаданных...")
    print("=" * 50)
    
    # Проверяем файл метаданных
    print(f"📁 Файл метаданных: {ACCOUNTS_METADATA_FILE}")
    print(f"📁 Существует: {ACCOUNTS_METADATA_FILE.exists()}")
    
    if ACCOUNTS_METADATA_FILE.exists():
        with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"📄 Содержимое файла ({len(content)} символов):")
            print(content)
            print("---")
        
        try:
            with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            print(f"✅ JSON парсится успешно: {len(metadata)} записей")
            for key, value in metadata.items():
                print(f"   - {key}: {value}")
        except Exception as e:
            print(f"❌ Ошибка парсинга JSON: {e}")
    
    # Проверяем директорию сессий
    print(f"\n📁 Директория сессий: {SESSIONS_DIR}")
    print(f"📁 Существует: {SESSIONS_DIR.exists()}")
    
    if SESSIONS_DIR.exists():
        session_files = list(SESSIONS_DIR.glob('*.session'))
        print(f"📄 Найдено файлов сессий: {len(session_files)}")
        for session_file in session_files:
            print(f"   - {session_file.name}")
    
    # Создаем менеджер и тестируем загрузку
    print(f"\n🔧 Тестирование TelethonManager...")
    manager = TelethonManager()
    
    # Тестируем метод _load_accounts_metadata напрямую
    print("📥 Вызов _load_accounts_metadata()...")
    metadata = manager._load_accounts_metadata()
    print(f"✅ Результат: {len(metadata)} записей")
    for key, value in metadata.items():
        print(f"   - {key}: {value}")
    
    # Проверяем состояние clients до инициализации
    print(f"\n👥 Состояние clients до инициализации: {len(manager.clients)} записей")
    
    # Тестируем инициализацию
    print("\n🚀 Вызов initialize()...")
    await manager.initialize()
    
    # Проверяем состояние clients после инициализации
    print(f"👥 Состояние clients после инициализации: {len(manager.clients)} записей")
    for user_id, data in manager.clients.items():
        print(f"   - {user_id}: {data['phone']} (monitoring: {data['monitoring']})")
    
    # Проверяем файл метаданных после инициализации
    print(f"\n📄 Проверка файла метаданных после инициализации...")
    if ACCOUNTS_METADATA_FILE.exists():
        with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"📄 Содержимое файла ({len(content)} символов):")
            print(content)

if __name__ == "__main__":
    asyncio.run(debug_metadata())

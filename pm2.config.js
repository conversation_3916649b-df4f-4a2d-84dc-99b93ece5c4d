// Упрощенная конфигурация PM2 для PM Searcher Bot
module.exports = {
  apps: [
    {
      name: 'pm-searcher',
      script: 'main.py',
      interpreter: 'python3',
      
      // Автоматически определяет текущую директорию
      cwd: process.cwd(),
      
      // Переменные окружения
      env: {
        PYTHONUNBUFFERED: '1',
        PYTHONPATH: process.cwd()
      },
      
      // Логи в папку data/logs
      log_file: './data/logs/pm2.log',
      out_file: './data/logs/pm2-out.log',
      error_file: './data/logs/pm2-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss',
      
      // Настройки перезапуска
      autorestart: true,
      watch: false,
      max_memory_restart: '300M',
      restart_delay: 3000,
      
      // Обработка ошибок
      min_uptime: '5s',
      max_restarts: 5,
      
      // Один процесс
      instances: 1,
      exec_mode: 'fork',
      
      // Таймауты
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Объединить логи
      merge_logs: true,
      
      // Игнорировать файлы при watch
      ignore_watch: [
        'data/logs/*',
        'data/sessions/*',
        '__pycache__/*',
        '*.pyc',
        '.git/*'
      ]
    }
  ]
};

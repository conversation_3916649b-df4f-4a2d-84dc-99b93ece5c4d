#!/usr/bin/env python3
"""
Главный файл запуска системы мониторинга сообщений PM Searcher
"""

import sys
import asyncio
import platform
from pathlib import Path

# Добавляем src в путь для импорта
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.environment import env_config
from src.bot.main import run_bot
from src.utils.logging import setup_logging

logger = setup_logging(__name__, 'main.log')

def check_environment():
    """Проверка переменных окружения"""
    is_valid, missing_vars = env_config.validate()
    
    if not is_valid:
        print("❌ Ошибка: Не заданы следующие переменные окружения:")
        for var in missing_vars:
            print(f"   - {var}")
        print(f"\nОтредактируйте файл data/.env и укажите все необходимые значения.")
        return False
    
    return True

def main():
    """Главная функция запуска"""
    print("🔍 PM Searcher - Система мониторинга сообщений")
    print("=" * 50)

    # Проверка окружения
    if not check_environment():
        sys.exit(1)

    print("✅ Переменные окружения настроены")
    print(f"👑 Администраторов: {len(env_config.ADMIN_CHAT_IDS)}")
    print("🚀 Запуск бота...")

    # Исправление для Windows
    if platform.system() == 'Windows':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    try:
        # Запуск бота
        asyncio.run(run_bot())
    except KeyboardInterrupt:
        print("\n🛑 Остановка по запросу пользователя")
        logger.info("Остановка по запросу пользователя")
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        logger.error(f"Критическая ошибка запуска: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

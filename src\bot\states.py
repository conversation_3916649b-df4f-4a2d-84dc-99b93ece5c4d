"""
Состояния FSM для бота
"""

from aiogram.fsm.state import State, StatesGroup

class ConnectAccount(StatesGroup):
    """Состояния для подключения аккаунта"""
    waiting_for_phone = State()
    waiting_for_code = State()

class ManageStopwords(StatesGroup):
    """Состояния для управления стоп-словами"""
    waiting_for_word_to_add = State()
    waiting_for_word_to_remove = State()


class StopWordsManagement(StatesGroup):
    """Расширенные состояния FSM для интерактивного управления стоп-словами через бота"""
    
    # Основные состояния ввода данных
    waiting_for_new_stopword = State()          # Ожидание ввода нового стоп-слова для добавления
    waiting_for_delete_confirmation = State()   # Ожидание подтверждения удаления стоп-слова
    waiting_for_search_query = State()          # Ожидание запроса для поиска стоп-слов
    waiting_for_import_file = State()           # Ожидание файла для импорта стоп-слов
    
    # Состояния просмотра и навигации
    browsing_stopwords = State()                # Состояние просмотра списка стоп-слов с пагинацией
    viewing_search_results = State()           # Состояние просмотра результатов поиска
    waiting_for_stopword_selection = State()   # Ожидание выбора стоп-слова по номеру для действий
    
    # Расширенные состояния для дополнительных операций
    waiting_for_export_filename = State()      # Ожидание имени файла для экспорта
    confirming_import = State()                 # Подтверждение импорта с предварительным просмотром


class AccountManagement(StatesGroup):
    """Состояния FSM для управления аккаунтами"""

    # Состояния для удаления аккаунтов
    confirming_account_deletion = State()      # Подтверждение удаления аккаунта
    deleting_account = State()                 # Процесс удаления аккаунта


class TeamleadManagement(StatesGroup):
    """Состояния FSM для управления тимлидами"""

    # Добавление тимлида
    waiting_for_teamlead_username = State()    # Ожидание ввода никнейма тимлида
    waiting_for_teamlead_user_id = State()     # Ожидание ввода Telegram ID тимлида

    # Назначение номеров тимлиду
    selecting_teamlead_for_assignment = State()  # Выбор тимлида для назначения
    selecting_phones_for_teamlead = State()      # Выбор номеров для назначения тимлиду

    # Удаление тимлида
    confirming_teamlead_deletion = State()     # Подтверждение удаления тимлида

    # Управление назначениями
    managing_phone_assignments = State()       # Управление назначениями номеров
    viewing_assignments = State()              # Просмотр текущих назначений


class ResponseTimeManagement(StatesGroup):
    """Состояния FSM для управления настройками времени ответа"""

    # Основные настройки
    waiting_for_default_response_time = State()    # Ожидание ввода времени ответа по умолчанию
    waiting_for_check_interval = State()           # Ожидание ввода интервала проверки

    # Настройки для конкретных аккаунтов
    selecting_account_for_settings = State()      # Выбор аккаунта для настройки
    waiting_for_account_response_time = State()   # Ожидание ввода времени ответа для аккаунта

    # Массовые настройки
    waiting_for_bulk_response_time = State()      # Ожидание ввода времени для массовой установки

    # Просмотр статистики и настроек
    viewing_response_time_settings = State()      # Просмотр текущих настроек
    viewing_response_time_statistics = State()    # Просмотр статистики


class RolesManagement(StatesGroup):
    """Состояния для управления ролями пользователей"""
    waiting_for_user_id_to_assign = State()       # Ожидание ID пользователя для назначения роли
    waiting_for_user_id_to_remove = State()       # Ожидание ID пользователя для удаления роли

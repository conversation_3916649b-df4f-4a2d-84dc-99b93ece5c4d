"""
Вспомогательные функции для тестирования PM Searcher Bot
"""

import asyncio
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional
from unittest.mock import Mock, AsyncMock

from aiogram.types import Message, CallbackQuery, User, Chat
from telethon.events import NewMessage


def create_temp_file(content: str, suffix: str = ".txt", encoding: str = "utf-8") -> Path:
    """
    Создание временного файла с содержимым
    
    Args:
        content: Содержимое файла
        suffix: Расширение файла
        encoding: Кодировка файла
        
    Returns:
        Path: Путь к созданному файлу
    """
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False, encoding=encoding)
    temp_file.write(content)
    temp_file.close()
    return Path(temp_file.name)


def create_temp_binary_file(content: bytes, suffix: str = ".bin") -> Path:
    """
    Создание временного бинарного файла
    
    Args:
        content: Бинарное содержимое
        suffix: Расширение файла
        
    Returns:
        Path: Путь к созданному файлу
    """
    temp_file = tempfile.NamedTemporaryFile(mode='wb', suffix=suffix, delete=False)
    temp_file.write(content)
    temp_file.close()
    return Path(temp_file.name)


def create_mock_message(
    user_id: int = 123456789,
    text: str = "Test message",
    username: str = "testuser",
    first_name: str = "Test",
    is_admin: bool = False
) -> Mock:
    """
    Создание мока сообщения aiogram
    
    Args:
        user_id: ID пользователя
        text: Текст сообщения
        username: Имя пользователя
        first_name: Имя
        is_admin: Является ли администратором
        
    Returns:
        Mock: Мок объекта Message
    """
    user = User(
        id=user_id,
        is_bot=False,
        first_name=first_name,
        username=username,
        language_code="ru"
    )
    
    chat = Chat(id=user_id, type="private")
    
    message = Mock(spec=Message)
    message.from_user = user
    message.chat = chat
    message.text = text
    message.message_id = 1
    message.answer = AsyncMock()
    message.reply = AsyncMock()
    message.edit_text = AsyncMock()
    
    return message


def create_mock_callback_query(
    user_id: int = 123456789,
    data: str = "test_callback",
    username: str = "testuser"
) -> Mock:
    """
    Создание мока callback query
    
    Args:
        user_id: ID пользователя
        data: Данные callback
        username: Имя пользователя
        
    Returns:
        Mock: Мок объекта CallbackQuery
    """
    user = User(
        id=user_id,
        is_bot=False,
        first_name="Test",
        username=username,
        language_code="ru"
    )
    
    message = create_mock_message(user_id, "Original message", username)
    
    callback = Mock(spec=CallbackQuery)
    callback.from_user = user
    callback.message = message
    callback.data = data
    callback.answer = AsyncMock()
    callback.edit_message_text = AsyncMock()
    callback.edit_message_reply_markup = AsyncMock()
    
    return callback


def create_mock_telethon_event(
    text: str = "Test message",
    is_outgoing: bool = True,
    user_id: int = 123456789
) -> Mock:
    """
    Создание мока события Telethon
    
    Args:
        text: Текст сообщения
        is_outgoing: Исходящее ли сообщение
        user_id: ID пользователя
        
    Returns:
        Mock: Мок объекта NewMessage.Event
    """
    event = Mock(spec=NewMessage.Event)
    event.message = Mock()
    event.message.text = text
    event.message.out = is_outgoing
    event.message.sender_id = user_id
    event.message.date = None
    
    return event


async def run_async_test(coro):
    """
    Запуск асинхронного теста
    
    Args:
        coro: Корутина для выполнения
        
    Returns:
        Результат выполнения корутины
    """
    loop = asyncio.get_event_loop()
    return await coro


def assert_called_with_text(mock_method, expected_text: str):
    """
    Проверка что метод был вызван с определенным текстом
    
    Args:
        mock_method: Мок метода
        expected_text: Ожидаемый текст
    """
    mock_method.assert_called()
    call_args = mock_method.call_args
    
    if call_args and len(call_args[0]) > 0:
        actual_text = call_args[0][0]
        assert expected_text in actual_text, f"Expected '{expected_text}' in '{actual_text}'"


def assert_not_called_with_text(mock_method, forbidden_text: str):
    """
    Проверка что метод НЕ был вызван с определенным текстом
    
    Args:
        mock_method: Мок метода
        forbidden_text: Запрещенный текст
    """
    if mock_method.called:
        call_args = mock_method.call_args
        if call_args and len(call_args[0]) > 0:
            actual_text = call_args[0][0]
            assert forbidden_text not in actual_text, f"Forbidden '{forbidden_text}' found in '{actual_text}'"


def create_large_text(size: int, word: str = "test") -> str:
    """
    Создание большого текста для тестов производительности
    
    Args:
        size: Размер в символах
        word: Слово для повторения
        
    Returns:
        str: Большой текст
    """
    words_needed = size // (len(word) + 1)  # +1 для пробела
    return " ".join([word] * words_needed)


def create_stopwords_file_content(words: List[str], with_comments: bool = True) -> str:
    """
    Создание содержимого файла стоп-слов
    
    Args:
        words: Список стоп-слов
        with_comments: Добавлять ли комментарии
        
    Returns:
        str: Содержимое файла
    """
    content = ""
    
    if with_comments:
        content += "# Тестовые стоп-слова\n"
        content += "# Каждое слово на новой строке\n"
        content += "# Строки начинающиеся с # игнорируются\n\n"
    
    for word in words:
        content += f"{word}\n"
    
    return content


def extract_callback_data(keyboard_markup) -> List[str]:
    """
    Извлечение callback_data из клавиатуры
    
    Args:
        keyboard_markup: Разметка клавиатуры
        
    Returns:
        List[str]: Список callback_data
    """
    callback_data = []
    
    if hasattr(keyboard_markup, 'inline_keyboard'):
        for row in keyboard_markup.inline_keyboard:
            for button in row:
                if hasattr(button, 'callback_data') and button.callback_data:
                    callback_data.append(button.callback_data)
    
    return callback_data


def count_keyboard_buttons(keyboard_markup) -> int:
    """
    Подсчет количества кнопок в клавиатуре
    
    Args:
        keyboard_markup: Разметка клавиатуры
        
    Returns:
        int: Количество кнопок
    """
    count = 0
    
    if hasattr(keyboard_markup, 'inline_keyboard'):
        for row in keyboard_markup.inline_keyboard:
            count += len(row)
    elif hasattr(keyboard_markup, 'keyboard'):
        for row in keyboard_markup.keyboard:
            count += len(row)
    
    return count


def simulate_file_error(error_type: str = "permission"):
    """
    Симуляция ошибок файловых операций
    
    Args:
        error_type: Тип ошибки (permission, not_found, io)
        
    Returns:
        Exception: Соответствующее исключение
    """
    if error_type == "permission":
        return PermissionError("Permission denied")
    elif error_type == "not_found":
        return FileNotFoundError("File not found")
    elif error_type == "io":
        return IOError("I/O operation failed")
    elif error_type == "unicode":
        return UnicodeDecodeError("utf-8", b"", 0, 1, "invalid start byte")
    else:
        return Exception("Unknown error")


def validate_security_response(response_text: str) -> bool:
    """
    Проверка что ответ содержит информацию о безопасности
    
    Args:
        response_text: Текст ответа
        
    Returns:
        bool: True если ответ корректный
    """
    security_keywords = [
        "ошибка", "недопустим", "запрещен", "безопасность",
        "некорректн", "неверн", "отклонен"
    ]
    
    response_lower = response_text.lower()
    return any(keyword in response_lower for keyword in security_keywords)

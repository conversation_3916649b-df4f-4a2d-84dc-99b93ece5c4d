# 🔍 PM Searcher - Система мониторинга сообщений

Автоматизированная система для мониторинга исходящих сообщений сотрудников в Telegram с проверкой на стоп-слова и мгновенными алертами.

## 🎯 Основные функции

-   **📱 Подключение аккаунтов** - Простое подключение Telegram аккаунтов через бота
-   **🔍 Мониторинг сообщений** - Отслеживание всех исходящих сообщений в реальном времени
-   **⚠️ Проверка стоп-слов** - Автоматическое обнаружение запрещенных слов и фраз
-   **🚨 Мгновенные алерты** - Уведомления администратору при обнаружении нарушений
-   **📊 Подробное логирование** - Полная история всех действий и событий
-   **👑 Административная панель** - Управление системой и просмотр статистики
-   **📝 Управление стоп-словами** - Полноценная система управления с пагинацией, поиском, импортом/экспортом

## 🛠 Технологии

-   **Python 3.8+** - Основной язык разработки
-   **aiogram 3.4+** - Фреймворк для Telegram бота
-   **Telethon 1.30+** - Библиотека для работы с Telegram API
-   **asyncio** - Асинхронное программирование
-   **python-dotenv** - Управление переменными окружения

## 🚀 Быстрый старт

### Автоматическая установка

1. **Клонирование проекта:**

    ```bash
    git clone <repository>
    cd pm-searcher
    ```

2. **Установка зависимостей:**

    ```bash
    # Windows PowerShell
    .\scripts\install_dependencies.ps1

    # Linux/macOS или Windows с Python
    python scripts/install_dependencies.py
    ```

3. **Настройка окружения:**

    - Создайте файл `data/.env` на основе примера ниже
    - Настройте `data/stopwords.txt` под ваши требования

4. **Тестирование системы:**

    ```bash
    python scripts/test_system.py
    ```

5. **Запуск:**
    ```bash
    python main.py
    ```

### Ручная установка

1. **Установка зависимостей:**

    ```bash
    pip install -r requirements.txt
    ```

2. **Настройка переменных окружения** - создайте файл `data/.env`:

    ```env
    # Токен Telegram бота (получить у @BotFather)
    BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz

    # API данные Telegram (получить на https://my.telegram.org)
    API_ID=12345678
    API_HASH=abcdef1234567890abcdef1234567890

    # ID администраторов (через запятую)
    ADMIN_CHAT_IDS=123456789,987654321
    ```

3. **Настройка стоп-слов** - отредактируйте файл `data/stopwords.txt`:
    ```
    # Комментарии начинаются с #
    спам
    реклама
    продажа
    # Добавьте свои стоп-слова
    ```

## 🔧 Получение необходимых данных

### Telegram Bot Token

1. Найдите @BotFather в Telegram
2. Отправьте `/newbot`
3. Следуйте инструкциям для создания бота
4. Скопируйте полученный токен в `BOT_TOKEN`

### API ID и API Hash

1. Перейдите на https://my.telegram.org
2. Войдите в свой Telegram аккаунт
3. Перейдите в "API development tools"
4. Создайте новое приложение
5. Скопируйте API ID и API Hash

### Admin Chat ID

1. Найдите @userinfobot в Telegram
2. Отправьте любое сообщение
3. Скопируйте ваш ID из ответа

## 📋 Архитектура системы

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Telegram Bot  │    │ Telethon Manager │    │   Monitoring    │
│                 │    │                  │    │                 │
│ • Подключение   │◄──►│ • Авторизация    │◄──►│ • Отслеживание  │
│ • Управление    │    │ • Сессии         │    │ • Проверка      │
│ • Алерты        │    │ • Переподключение│    │ • Логирование   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  │
                         ┌─────────────────┐
                         │   Data & Config │
                         │                 │
                         │ • data/.env     │
                         │ • data/stopwords│
                         │ • data/sessions/│
                         │ • data/logs/    │
                         └─────────────────┘
```

### Структура проекта

```
pm-searcher/
├── main.py                 # Главный файл запуска
├── requirements.txt        # Зависимости Python
├── pytest.ini            # Конфигурация тестов
│
├── src/                   # Основной код приложения
│   ├── bot/              # Модули Telegram бота
│   │   ├── main.py       # Основной класс бота
│   │   ├── alerts.py     # Система алертов
│   │   ├── keyboards.py  # Клавиатуры бота
│   │   ├── states.py     # FSM состояния
│   │   ├── validators.py # Валидация данных
│   │   └── handlers/     # Обработчики команд
│   │       ├── basic.py  # Основные команды
│   │       ├── account.py # Управление аккаунтами
│   │       ├── admin.py  # Административные команды
│   │       └── stopwords_management.py # Управление стоп-словами
│   │
│   ├── monitoring/       # Модули мониторинга Telethon
│   │   └── telethon_manager.py # Менеджер Telethon клиентов
│   │
│   ├── config/          # Конфигурационные модули
│   │   ├── settings.py  # Основные настройки
│   │   └── environment.py # Переменные окружения
│   │
│   └── utils/           # Вспомогательные утилиты
│       ├── logging.py   # Настройка логирования
│       └── stopwords.py # Работа со стоп-словами
│
├── data/                # Данные проекта
│   ├── .env            # Переменные окружения
│   ├── stopwords.txt   # Список стоп-слов
│   ├── sessions/       # Сессии Telethon
│   └── logs/           # Файлы логов
│
├── scripts/            # Скрипты установки и тестирования
│   ├── install_dependencies.py  # Установка зависимостей
│   ├── install_dependencies.ps1 # PowerShell скрипт
│   └── test_system.py          # Тестирование системы
│
└── tests/              # Система тестирования
    ├── unit/           # Юнит-тесты
    ├── handlers/       # Тесты обработчиков
    ├── integration/    # Интеграционные тесты
    └── security/       # Тесты безопасности
```

## 🔐 Безопасность

-   ✅ Безопасное хранение сессий Telethon
-   ✅ Шифрование авторизационных данных
-   ✅ Ограничение доступа к административным функциям
-   ✅ Подробное логирование всех действий
-   ✅ Защита от несанкционированного доступа
-   ✅ Валидация всех пользовательских вводов
-   ✅ Защита от SQL-инъекций, XSS и path traversal атак

## 📊 Возможности мониторинга

-   **Реальное время** - Мгновенное обнаружение стоп-слов
-   **Полное покрытие** - Все исходящие сообщения всех подключенных аккаунтов
-   **Контекстная информация** - Данные о чате, времени, отправителе
-   **Гибкие фильтры** - Настраиваемый список стоп-слов
-   **Автоматические алерты** - Уведомления администратору и пользователю

## 📝 Управление стоп-словами

### Основные возможности:

-   **📋 Просмотр списка** - Пагинация по 10 слов на страницу
-   **➕ Добавление слов** - С валидацией и проверкой дубликатов
-   **🗑️ Удаление слов** - С подтверждением операции
-   **🔍 Поиск** - Быстрый поиск по подстроке
-   **📤 Экспорт** - Сохранение в .txt файл с метаданными
-   **📥 Импорт** - Загрузка из файла с предварительным просмотром
-   **📊 Статистика** - Детальная информация о файле стоп-слов

### Интерфейс управления:

-   **Reply-клавиатуры** - Основная навигация
-   **Inline-кнопки** - Операции и пагинация
-   **FSM состояния** - Пошаговые операции
-   **Подтверждения** - Для критических действий

## 👥 Роли пользователей

### 👤 Обычный пользователь:

-   Подключение своих аккаунтов
-   Просмотр статуса подключенных аккаунтов
-   Получение уведомлений о нарушениях

### 👑 Администратор:

-   Все функции обычного пользователя
-   Просмотр всех подключенных аккаунтов
-   Полное управление стоп-словами
-   Просмотр статистики системы
-   Получение всех алертов

## 🧪 Тестирование

Проект включает комплексную систему тестирования с покрытием 90%+:

### Запуск тестов:

```bash
# Все тесты
pytest

# По категориям
pytest tests/unit/          # Юнит-тесты
pytest tests/handlers/      # Тесты обработчиков
pytest tests/integration/   # Интеграционные тесты
pytest tests/security/      # Тесты безопасности

# С покрытием кода
pytest --cov=src --cov-report=html
```

### Типы тестов:

-   **Юнит-тесты** - Тестирование отдельных компонентов
-   **Интеграционные тесты** - Взаимодействие компонентов
-   **Тесты обработчиков** - aiogram обработчики и FSM
-   **Тесты безопасности** - Защита от атак и уязвимостей

## 📈 Статистика и отчеты

-   Количество подключенных аккаунтов
-   Статус мониторинга (активен/неактивен)
-   Количество загруженных стоп-слов
-   История подключений и отключений
-   Статистика обнаруженных нарушений
-   Детальная аналитика стоп-слов (длина, язык, частота)

## 🔧 Устранение неполадок

### Частые проблемы:

**Проблема: "Client not found"**

-   Решение: Перезапустите процесс подключения аккаунта

**Проблема: "Invalid phone number"**

-   Решение: Используйте международный формат (+79991234567)

**Проблема: "Flood wait"**

-   Решение: Подождите указанное время и повторите

**Проблема: Бот не отвечает**

-   Решение: Проверьте BOT_TOKEN, убедитесь что бот запущен

### Диагностика:

1. Проверьте логи в папке `data/logs/`
2. Запустите `python scripts/test_system.py`
3. Убедитесь в правильности настройки `data/.env`
4. Проверьте подключение к интернету
5. Перезапустите систему

## 🔄 Обновление системы

### Обновление стоп-слов:

1. Отредактируйте `data/stopwords.txt`
2. В боте используйте команду `/reload_stopwords`

### Обновление кода:

```bash
# Остановка системы (Ctrl+C)
git pull origin main
python main.py  # Перезапуск
```

## 📄 Лицензия

Проект разработан для внутреннего использования компании.

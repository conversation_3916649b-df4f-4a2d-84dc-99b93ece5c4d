"""
Утилиты для работы со стоп-словами
"""

import logging
import os
import shutil
import re
from datetime import datetime
from typing import List, Tuple, Dict, Optional
from ..config.settings import STOPWORDS_FILE

logger = logging.getLogger(__name__)

class StopWordsManager:
    """Менеджер стоп-слов"""
    
    def __init__(self):
        self.stopwords = self.load_stopwords()
    
    def load_stopwords(self) -> List[str]:
        """Загрузка стоп-слов из файла"""
        try:
            with open(STOPWORDS_FILE, 'r', encoding='utf-8') as f:
                words = []
                for line in f:
                    line = line.strip()
                    # Игнорируем пустые строки и комментарии
                    if line and not line.startswith('#'):
                        words.append(line.lower())
            logger.info(f"Загружено {len(words)} стоп-слов")
            return words
        except FileNotFoundError:
            logger.warning(f"Файл стоп-слов {STOPWORDS_FILE} не найден")
            return []
        except Exception as e:
            logger.error(f"Ошибка загрузки стоп-слов: {e}")
            return []
    
    def reload_stopwords(self) -> int:
        """Перезагрузка стоп-слов"""
        self.stopwords = self.load_stopwords()
        return len(self.stopwords)
    
    def contains_stopwords(self, text: str) -> Tuple[bool, List[str]]:
        """
        Проверка текста на наличие стоп-слов

        Улучшенная версия с нормализацией текста для лучшего обнаружения
        стоп-слов в различных форматах и кодировках.
        """
        if not text or not self.stopwords:
            return False, []

        # Нормализация текста для лучшего поиска
        normalized_text = self._normalize_text_for_search(text)
        found_words = []

        for word in self.stopwords:
            # Проверяем как исходное слово, так и нормализованное
            if word in normalized_text:
                found_words.append(word)

        return len(found_words) > 0, found_words

    def _normalize_text_for_search(self, text: str) -> str:
        """
        Нормализация текста для поиска стоп-слов

        Выполняет:
        - Приведение к нижнему регистру
        - Удаление лишних пробелов
        - Нормализация невидимых символов
        - Сохранение специальных символов для точного поиска
        """
        if not text:
            return ""

        # Приведение к нижнему регистру
        normalized = text.lower()

        # Удаление невидимых символов, но сохранение видимых специальных символов
        normalized = ''.join(char for char in normalized if char.isprintable() or char.isspace())

        # Нормализация пробелов (заменяем множественные пробелы на одинарные)
        normalized = ' '.join(normalized.split())

        return normalized

    def validate_and_add_stopword(self, word: str) -> Tuple[bool, str, List[str]]:
        """
        Валидация и добавление стоп-слова с подробной информацией

        Returns:
            Tuple[bool, str, List[str]]: (успех, нормализованное_слово, ошибки)
        """
        from ..bot.validators import StopWordValidator

        # Создаем валидатор с текущими стоп-словами
        validator = StopWordValidator(set(self.stopwords))

        # Валидируем слово
        validation_result = validator.validate_user_input(word)

        if validation_result.is_valid:
            # Добавляем слово
            success = self.add_stopword(validation_result.normalized_data)
            return success, validation_result.normalized_data, []
        else:
            return False, word, validation_result.errors

    def bulk_add_stopwords(self, words: List[str]) -> dict:
        """
        Массовое добавление стоп-слов с детальной статистикой

        Args:
            words: Список слов для добавления

        Returns:
            dict: Статистика добавления
        """
        stats = {
            'total_processed': len(words),
            'added': [],
            'skipped': [],
            'errors': [],
            'duplicates': []
        }

        for word in words:
            success, normalized_word, errors = self.validate_and_add_stopword(word)

            if success:
                stats['added'].append(normalized_word)
            elif errors:
                stats['errors'].append({'word': word, 'errors': errors})
            else:
                # Слово уже существует
                stats['duplicates'].append(normalized_word)

        return stats
    
    def add_stopword(self, word: str) -> bool:
        """Добавление нового стоп-слова"""
        word = word.strip().lower()
        if word and word not in self.stopwords:
            self.stopwords.append(word)
            return self._save_stopwords()
        return False
    
    def remove_stopword(self, word: str) -> bool:
        """Удаление стоп-слова"""
        word = word.strip().lower()
        if word in self.stopwords:
            self.stopwords.remove(word)
            return self._save_stopwords()
        return False
    
    def _save_stopwords(self) -> bool:
        """Сохранение стоп-слов в файл"""
        try:
            with open(STOPWORDS_FILE, 'w', encoding='utf-8') as f:
                f.write("# Список стоп-слов для мониторинга\n")
                f.write("# Каждое слово на новой строке\n")
                f.write("# Строки начинающиеся с # игнорируются\n\n")
                
                for word in sorted(self.stopwords):
                    f.write(f"{word}\n")
            
            logger.info(f"Сохранено {len(self.stopwords)} стоп-слов")
            return True
        except Exception as e:
            logger.error(f"Ошибка сохранения стоп-слов: {e}")
            return False
    
    def get_stats(self) -> dict:
        """Получение статистики стоп-слов"""
        return {
            'total_count': len(self.stopwords),
            'words': self.stopwords[:10] if len(self.stopwords) > 10 else self.stopwords
        }

    def get_paginated_stopwords(self, page: int, per_page: int = 10) -> Dict[str, any]:
        """
        Получение стоп-слов для конкретной страницы с пагинацией
        
        Args:
            page (int): Номер страницы (начиная с 1)
            per_page (int): Количество элементов на странице (по умолчанию 10)
            
        Returns:
            Dict: Словарь с данными пагинации и списком слов
        """
        try:
            # Валидация входных параметров
            if page < 1:
                page = 1
            if per_page < 1:
                per_page = 10
            
            # Сортировка стоп-слов для стабильной пагинации
            sorted_words = sorted(self.stopwords)
            total_words = len(sorted_words)
            total_pages = self.get_total_pages(per_page)
            
            # Вычисление индексов для среза
            start_index = (page - 1) * per_page
            end_index = start_index + per_page
            
            # Получение слов для текущей страницы
            page_words = sorted_words[start_index:end_index]
            
            logger.info(f"Получена страница {page}/{total_pages} со стоп-словами ({len(page_words)} элементов)")
            
            return {
                'words': page_words,
                'current_page': page,
                'per_page': per_page,
                'total_words': total_words,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
            
        except Exception as e:
            logger.error(f"Ошибка при получении пагинированных стоп-слов: {e}")
            return {
                'words': [],
                'current_page': 1,
                'per_page': per_page,
                'total_words': 0,
                'total_pages': 0,
                'has_next': False,
                'has_prev': False
            }
    
    def search_stopwords(self, query: str) -> List[str]:
        """
        Поиск стоп-слов по подстроке
        
        Args:
            query (str): Поисковый запрос
            
        Returns:
            List[str]: Список найденных стоп-слов
        """
        try:
            if not query or not query.strip():
                logger.warning("Получен пустой поисковый запрос")
                return []
            
            # Очистка и нормализация запроса
            query = query.strip().lower()
            found_words = []
            
            # Поиск по подстроке в каждом стоп-слове
            for word in self.stopwords:
                if query in word.lower():
                    found_words.append(word)
            
            # Сортировка результатов для стабильного порядка
            found_words.sort()
            
            logger.info(f"По запросу '{query}' найдено {len(found_words)} стоп-слов")
            return found_words
            
        except Exception as e:
            logger.error(f"Ошибка при поиске стоп-слов по запросу '{query}': {e}")
            return []
    
    def get_total_pages(self, per_page: int = 10) -> int:
        """
        Получение общего количества страниц для пагинации
        
        Args:
            per_page (int): Количество элементов на странице
            
        Returns:
            int: Общее количество страниц
        """
        try:
            if per_page < 1:
                per_page = 10
            
            total_words = len(self.stopwords)
            total_pages = (total_words + per_page - 1) // per_page  # Округление вверх
            
            return max(1, total_pages)  # Минимум 1 страница

        except Exception as e:
            logger.error(f"Ошибка при подсчете страниц: {e}")
            return 1

    
    def validate_stopword(self, word: str) -> Tuple[bool, str]:
        """
        Валидация отдельного стоп-слова
        
        Args:
            word (str): Слово для валидации
            
        Returns:
            Tuple[bool, str]: (статус валидации, сообщение об ошибке)
        """
        try:
            if not word:
                return False, "Слово не может быть пустым"
            
            # Очистка слова от пробелов
            word = word.strip()
            
            if not word:
                return False, "Слово не может состоять только из пробелов"
            
            # Проверка длины
            if len(word) < 2:
                return False, "Слово должно содержать минимум 2 символа"
            
            if len(word) > 50:
                return False, "Слово не может содержать более 50 символов"
            
            # Проверка на допустимые символы (кириллица, латиница, цифры, дефис)
            allowed_pattern = re.compile(r'^[а-яёА-ЯЁa-zA-Z0-9\-]+$')
            if not allowed_pattern.match(word):
                return False, "Слово может содержать только буквы, цифры и дефис"
            
            # Проверка на дубликаты
            if word.lower() in [w.lower() for w in self.stopwords]:
                return False, "Такое стоп-слово уже существует"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"Ошибка при валидации слова '{word}': {e}")
            return False, f"Ошибка валидации: {str(e)}"
    
    def export_to_file(self, filepath: str) -> bool:
        """
        Экспорт стоп-слов в файл
        
        Args:
            filepath (str): Путь к файлу для экспорта
            
        Returns:
            bool: Успешность операции
        """
        try:
            if not filepath:
                logger.error("Не указан путь для экспорта")
                return False
            
            # Создание директории если она не существует
            directory = os.path.dirname(filepath)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
            
            # Подготовка содержимого для экспорта
            export_content = []
            export_content.append(f"# Экспорт стоп-слов PM Searcher Bot")
            export_content.append(f"# Дата экспорта: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            export_content.append(f"# Общее количество: {len(self.stopwords)}")
            export_content.append(f"# Автоматически сгенерированный файл")
            export_content.append("")
            
            # Добавление отсортированных стоп-слов
            for word in sorted(self.stopwords):
                export_content.append(word)
            
            # Запись в файл
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(export_content))
            
            logger.info(f"Экспорт {len(self.stopwords)} стоп-слов в файл '{filepath}' выполнен успешно")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при экспорте стоп-слов в файл '{filepath}': {e}")
            return False
    
    def import_from_file(self, filepath: str) -> Dict[str, any]:
        """
        Импорт стоп-слов из файла с валидацией
        
        Args:
            filepath (str): Путь к файлу для импорта
            
        Returns:
            Dict: Результат импорта с детальной информацией
        """
        try:
            if not filepath or not os.path.exists(filepath):
                logger.error(f"Файл для импорта не найден: '{filepath}'")
                return {
                    'success': False,
                    'added_count': 0,
                    'skipped_count': 0,
                    'invalid_count': 0,
                    'message': 'Файл не найден',
                    'added_words': [],
                    'skipped_words': [],
                    'invalid_words': []
                }
            
            # Создание бекапа перед импортом
            backup_path = self.create_backup()
            if not backup_path:
                logger.warning("Не удалось создать бекап перед импортом")
            
            added_words = []
            skipped_words = []
            invalid_words = []
            
            # Чтение файла
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Обработка каждой строки
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # Пропуск пустых строк и комментариев
                if not line or line.startswith('#'):
                    continue
                
                # Валидация слова
                is_valid, error_message = self.validate_stopword(line)
                
                if is_valid:
                    # Добавление валидного слова
                    if self.add_stopword(line):
                        added_words.append(line)
                    else:
                        skipped_words.append(line)
                else:
                    # Слово не прошло валидацию
                    invalid_words.append(f"Строка {line_num}: '{line}' - {error_message}")
            
            result = {
                'success': True,
                'added': len(added_words),
                'skipped': len(skipped_words),
                'total_processed': len(added_words) + len(skipped_words) + len(invalid_words),
                'invalid_count': len(invalid_words),
                'message': f'Импорт завершен: добавлено {len(added_words)}, пропущено {len(skipped_words)}, невалидных {len(invalid_words)}',
                'added_words': added_words,
                'skipped_words': skipped_words,
                'invalid_words': invalid_words,
                'backup_path': backup_path
            }
            
            logger.info(f"Импорт из файла '{filepath}': {result['message']}")
            return result
            
        except Exception as e:
            logger.error(f"Ошибка при импорте стоп-слов из файла '{filepath}': {e}")
            return {
                'success': False,
                'added_count': 0,
                'skipped_count': 0,
                'invalid_count': 0,
                'message': f'Ошибка импорта: {str(e)}',
                'added_words': [],
                'skipped_words': [],
                'invalid_words': []
            }

    
    def create_backup(self) -> Optional[str]:
        """
        Создание бекапа текущего файла стоп-слов
        
        Returns:
            Optional[str]: Путь к созданному бекапу или None при ошибке
        """
        try:
            if not os.path.exists(STOPWORDS_FILE):
                logger.warning("Основной файл стоп-слов не существует, бекап не создан")
                return None
            
            # Создание имени файла бекапа с временной меткой
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"stopwords_backup_{timestamp}.txt"
            
            # Определение пути для бекапа (в той же директории что и основной файл)
            backup_dir = os.path.dirname(STOPWORDS_FILE)
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # Создание копии файла
            shutil.copy2(STOPWORDS_FILE, backup_path)
            
            logger.info(f"Создан бекап стоп-слов: '{backup_path}'")
            return backup_path
            
        except Exception as e:
            logger.error(f"Ошибка при создании бекапа стоп-слов: {e}")
            return None
    
    def get_detailed_stats(self) -> Dict[str, any]:
        """
        Получение расширенной статистики по стоп-словам
        
        Returns:
            Dict: Детальная статистика
        """
        try:
            # Основные метрики
            total_count = len(self.stopwords)
            
            if total_count == 0:
                return {
                    'total_count': 0,
                    'average_length': 0,
                    'avg_length': 0,
                    'min_length': 0,
                    'max_length': 0,
                    'language_distribution': {},
                    'length_distribution': {},
                    'character_distribution': {},
                    'words_by_length': {},
                    'longest_words': [],
                    'shortest_words': [],
                    'file_info': self._get_file_info()
                }
            
            # Анализ длины слов
            word_lengths = [len(word) for word in self.stopwords]
            avg_length = sum(word_lengths) / len(word_lengths)
            min_length = min(word_lengths)
            max_length = max(word_lengths)
            
            # Распределение по длине
            length_distribution = {}
            for length in word_lengths:
                length_distribution[length] = length_distribution.get(length, 0) + 1
            
            # Распределение по языкам (упрощенная классификация)
            language_distribution = {'cyrillic': 0, 'latin': 0, 'mixed': 0, 'numeric': 0}
            
            for word in self.stopwords:
                if re.search(r'[а-яёА-ЯЁ]', word):
                    if re.search(r'[a-zA-Z]', word):
                        language_distribution['mixed'] += 1
                    else:
                        language_distribution['cyrillic'] += 1
                elif re.search(r'[a-zA-Z]', word):
                    language_distribution['latin'] += 1
                elif re.search(r'[0-9]', word):
                    language_distribution['numeric'] += 1
            
            # Распределение символов
            all_chars = ''.join(self.stopwords)
            char_count = {}
            for char in all_chars:
                char_count[char] = char_count.get(char, 0) + 1
            
            # Топ-10 самых частых символов
            character_distribution = dict(sorted(char_count.items(), key=lambda x: x[1], reverse=True)[:10])
            
            # Группировка слов по длине
            words_by_length = {}
            for word in self.stopwords:
                length = len(word)
                if length not in words_by_length:
                    words_by_length[length] = []
                words_by_length[length].append(word)
            
            # Самые длинные и короткие слова
            sorted_words = sorted(self.stopwords, key=len)
            longest_words = [word for word in sorted_words if len(word) == max_length][:5]
            shortest_words = [word for word in sorted_words if len(word) == min_length][:5]
            
            # Информация о файле
            file_info = self._get_file_info()
            
            stats = {
                'total_count': total_count,
                'average_length': round(avg_length, 2),
                'avg_length': round(avg_length, 2),
                'min_length': min_length,
                'max_length': max_length,
                'language_distribution': language_distribution,
                'length_distribution': length_distribution,
                'character_distribution': character_distribution,
                'words_by_length': {str(k): v for k, v in words_by_length.items()},
                'longest_words': longest_words,
                'shortest_words': shortest_words,
                'file_info': file_info
            }
            
            logger.info(f"Сгенерирована детальная статистика для {total_count} стоп-слов")
            return stats
            
        except Exception as e:
            logger.error(f"Ошибка при генерации детальной статистики: {e}")
            return {
                'total_count': len(self.stopwords),
                'error': str(e),
                'file_info': self._get_file_info()
            }
    
    def _get_file_info(self) -> Dict[str, any]:
        """
        Получение информации о файле стоп-слов
        
        Returns:
            Dict: Информация о файле
        """
        try:
            if not os.path.exists(STOPWORDS_FILE):
                return {
                    'exists': False,
                    'size': 0,
                    'modified': None,
                    'path': STOPWORDS_FILE
                }
            
            stat = os.stat(STOPWORDS_FILE)
            
            return {
                'exists': True,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                'path': STOPWORDS_FILE,
                'size_formatted': self._format_file_size(stat.st_size)
            }
            
        except Exception as e:
            logger.error(f"Ошибка при получении информации о файле: {e}")
            return {
                'exists': False,
                'error': str(e),
                'path': STOPWORDS_FILE
            }
    
    def _format_file_size(self, size_bytes: int) -> str:
        """
        Форматирование размера файла в человеко-читаемый вид
        
        Args:
            size_bytes (int): Размер в байтах
            
        Returns:
            str: Отформатированный размер
        """
        try:
            if size_bytes == 0:
                return "0 B"
            
            size_names = ["B", "KB", "MB", "GB"]
            i = 0
            while size_bytes >= 1024 and i < len(size_names) - 1:
                size_bytes /= 1024.0
                i += 1
            
            return f"{size_bytes:.1f} {size_names[i]}"
            
        except Exception:
            return f"{size_bytes} B"

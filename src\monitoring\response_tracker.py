"""
Система отслеживания времени ответа менеджеров на сообщения лидов
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
from src.config.response_time_manager import response_time_manager
from src.utils.logging import setup_logging

logger = setup_logging(__name__, 'response_tracker.log')

@dataclass
class PendingMessage:
    """Класс для хранения информации о сообщении, ожидающем ответа"""
    message_id: int
    sender_id: int
    sender_name: str
    chat_id: int
    chat_title: str
    account_phone: str
    received_at: datetime
    deadline: datetime
    text: str
    is_from_external: bool = True
    expired_alert_sent: bool = False  # Алерт о просрочке отправлен
    response_received: bool = False
    response_time: Optional[timedelta] = None

class ResponseTracker:
    """Класс для отслеживания времени ответа на сообщения"""
    
    def __init__(self, alert_callback: Optional[Callable] = None):
        self.pending_messages: Dict[str, PendingMessage] = {}  # key: f"{account_phone}_{chat_id}_{message_id}"
        self.alert_callback = alert_callback
        self._monitoring_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start_monitoring(self):
        """Запуск мониторинга дедлайнов"""
        if self._running:
            logger.warning("Мониторинг уже запущен")
            return
        
        self._running = True
        self._monitoring_task = asyncio.create_task(self._monitor_deadlines())
        logger.info("Мониторинг времени ответа запущен")
    
    async def stop_monitoring(self):
        """Остановка мониторинга"""
        self._running = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Мониторинг времени ответа остановлен")
    
    def track_incoming_message(self, message_data: Dict[str, Any]) -> bool:
        """
        Отслеживание входящего сообщения от лида

        Args:
            message_data: Словарь с данными сообщения

        Returns:
            bool: True если сообщение добавлено для отслеживания
        """
        try:
            # Проверяем, включен ли мониторинг
            if not response_time_manager.enabled:
                logger.debug("Мониторинг времени ответа отключен")
                return False

            # Извлекаем данные из сообщения
            account_phone = message_data.get('account_phone')
            message_id = message_data.get('message_id')
            sender_id = message_data.get('sender_id')
            sender_name = message_data.get('sender_name', 'Неизвестный')
            chat_id = message_data.get('chat_id')
            chat_title = message_data.get('chat_title', 'Неизвестный чат')
            text = message_data.get('text', '')
            is_from_external = message_data.get('is_from_external', True)
            chat_type = message_data.get('chat_type', 'unknown')
            is_bot = message_data.get('is_bot', False)

            # Проверяем обязательные поля
            if not all([account_phone, message_id, sender_id, chat_id]):
                logger.warning("Недостаточно данных для отслеживания сообщения")
                return False

            # Проверяем, что сообщение от внешнего пользователя
            if not is_from_external:
                logger.debug(f"Пропускаем сообщение от менеджера: {account_phone}")
                return False

            # Проверяем тип чата (только личные чаты)
            from ..config.monitoring_settings import monitoring_settings
            if not monitoring_settings.should_monitor_chat(chat_type):
                logger.debug(f"Мониторинг для {chat_type} чатов отключен, пропускаем сообщение")
                return False

            # Проверяем фильтрацию ботов
            if monitoring_settings.bot_filtering_enabled and is_bot:
                logger.debug(f"Пропускаем сообщение от бота: {sender_name}")
                return False
            
            # Получаем время ответа для аккаунта
            response_time_minutes = response_time_manager.get_response_time_for_account(account_phone)
            
            # Рассчитываем дедлайн
            received_at = datetime.now()
            deadline = received_at + timedelta(minutes=response_time_minutes)
            
            # Создаем ключ для сообщения
            message_key = f"{account_phone}_{chat_id}_{message_id}"
            
            # Создаем объект отслеживания
            pending_message = PendingMessage(
                message_id=message_id,
                sender_id=sender_id,
                sender_name=sender_name,
                chat_id=chat_id,
                chat_title=chat_title,
                account_phone=account_phone,
                received_at=received_at,
                deadline=deadline,
                text=text[:200],  # Ограничиваем длину текста
                is_from_external=is_from_external
            )
            
            # Добавляем в отслеживание
            self.pending_messages[message_key] = pending_message
            
            # Обновляем статистику
            response_time_manager.update_statistics("total_messages_tracked")
            
            logger.info(f"Добавлено сообщение для отслеживания: {account_phone} от {sender_name} (дедлайн: {deadline.strftime('%H:%M:%S')})")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отслеживания входящего сообщения: {e}")
            return False
    
    def track_outgoing_message(self, message_data: Dict[str, Any]) -> bool:
        """
        Отслеживание исходящего сообщения (ответа менеджера)
        
        Args:
            message_data: Словарь с данными сообщения
            
        Returns:
            bool: True если найдено соответствующее ожидающее сообщение
        """
        try:
            account_phone = message_data.get('account_phone')
            chat_id = message_data.get('chat_id')
            
            if not account_phone or not chat_id:
                return False
            
            # Ищем ожидающие сообщения для этого чата
            response_time = datetime.now()
            found_pending = False
            
            # Создаем список ключей для удаления (чтобы избежать изменения словаря во время итерации)
            keys_to_remove = []
            
            for message_key, pending_msg in self.pending_messages.items():
                if (pending_msg.account_phone == account_phone and 
                    pending_msg.chat_id == chat_id and 
                    not pending_msg.response_received):
                    
                    # Отмечаем ответ получен
                    pending_msg.response_received = True
                    pending_msg.response_time = response_time - pending_msg.received_at
                    
                    # Определяем, был ли ответ вовремя
                    if response_time <= pending_msg.deadline:
                        response_time_manager.update_statistics("total_responses_on_time")
                        status = "вовремя"
                    else:
                        response_time_manager.update_statistics("total_responses_late")
                        status = "с опозданием"
                    
                    logger.info(f"Ответ получен {status}: {account_phone} в чате {pending_msg.chat_title} "
                              f"(время ответа: {pending_msg.response_time})")
                    
                    # Помечаем для удаления
                    keys_to_remove.append(message_key)
                    found_pending = True
            
            # Удаляем обработанные сообщения
            for key in keys_to_remove:
                del self.pending_messages[key]
            
            return found_pending
            
        except Exception as e:
            logger.error(f"Ошибка отслеживания исходящего сообщения: {e}")
            return False
    
    async def _monitor_deadlines(self):
        """Мониторинг дедлайнов и отправка алертов"""
        while self._running:
            try:
                current_time = datetime.now()

                # Список сообщений для удаления
                expired_messages = []

                for message_key, pending_msg in self.pending_messages.items():
                    # Пропускаем уже обработанные сообщения
                    if pending_msg.response_received:
                        continue

                    # Проверяем, истек ли дедлайн
                    if current_time > pending_msg.deadline:
                        if not pending_msg.expired_alert_sent:
                            await self._send_deadline_alert(pending_msg)
                            pending_msg.expired_alert_sent = True
                            response_time_manager.update_statistics("total_missed_responses")

                        # Помечаем для удаления через некоторое время
                        if current_time > pending_msg.deadline + timedelta(hours=1):
                            expired_messages.append(message_key)

                # Удаляем устаревшие сообщения
                for key in expired_messages:
                    del self.pending_messages[key]

                # Ждем до следующей проверки
                await asyncio.sleep(response_time_manager.check_interval_seconds)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Ошибка в мониторинге дедлайнов: {e}")
                await asyncio.sleep(5)  # Короткая пауза при ошибке
    
    async def _send_deadline_alert(self, pending_msg: PendingMessage):
        """Отправка алерта о просрочке дедлайна"""
        try:
            if not self.alert_callback:
                logger.warning("Alert callback не установлен")
                return

            current_time = datetime.now()
            time_overdue = current_time - pending_msg.deadline

            alert_text = (
                f"🚨 ПРОСРОЧЕН ОТВЕТ МЕНЕДЖЕРА!\n\n"
                f"📱 Аккаунт: {pending_msg.account_phone}\n"
                f"👤 От: {pending_msg.sender_name}\n"
                f"💬 Чат: {pending_msg.chat_title}\n"
                f"📝 Сообщение: {pending_msg.text}\n"
                f"⏰ Получено: {pending_msg.received_at.strftime('%H:%M:%S')}\n"
                f"⚠️ Дедлайн: {pending_msg.deadline.strftime('%H:%M:%S')}\n"
                f"🔴 Просрочено на: {str(time_overdue).split('.')[0]}\n"
                f"📅 Дата: {current_time.strftime('%Y-%m-%d')}"
            )

            await self.alert_callback(pending_msg.account_phone, alert_text)

        except Exception as e:
            logger.error(f"Ошибка отправки алерта о просрочке: {e}")
    
    def get_pending_messages_count(self) -> int:
        """Получить количество ожидающих ответа сообщений"""
        return len([msg for msg in self.pending_messages.values() if not msg.response_received])
    
    def get_pending_messages_for_account(self, account_phone: str) -> List[Dict[str, Any]]:
        """Получить список ожидающих сообщений для аккаунта"""
        messages = []
        for pending_msg in self.pending_messages.values():
            if (pending_msg.account_phone == account_phone and 
                not pending_msg.response_received):
                messages.append(asdict(pending_msg))
        return messages
    
    def clear_old_messages(self, hours: int = 24):
        """Очистка старых сообщений"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        keys_to_remove = []
        
        for key, pending_msg in self.pending_messages.items():
            if pending_msg.received_at < cutoff_time:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.pending_messages[key]
        
        if keys_to_remove:
            logger.info(f"Удалено {len(keys_to_remove)} старых сообщений")
    
    def get_status(self) -> Dict[str, Any]:
        """Получить статус трекера"""
        return {
            "running": self._running,
            "pending_messages_count": self.get_pending_messages_count(),
            "total_tracked": len(self.pending_messages),
            "monitoring_enabled": response_time_manager.enabled
        }

"""
Юнит-тесты для TelethonManager

Покрывает все методы класса TelethonManager с мокированием Telethon API,
обработкой исключений и тестами безопасности.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path

from telethon import TelegramClient
from telethon.errors import (
    SessionPasswordNeededError, PhoneCodeInvalidError,
    FloodWaitError
)
from telethon.events import NewMessage

from src.monitoring.telethon_manager import TelethonManager
from src.utils.stopwords import StopWordsManager
from tests.fixtures.test_constants import (
    TEST_ADMIN_USER_ID, TEST_REGULAR_USER_ID,
    VALID_PHONE_NUMBERS, INVALID_PHONE_NUMBERS,
    VALID_CONFIRMATION_CODES, INVALID_CONFIRMATION_CODES
)
from tests.utils.mock_factories import (
    TelethonMockFactory, ErrorMockFactory, create_test_accounts
)

def create_mock_alert_manager():
    """Создание mock AlertManager для тестов"""
    mock_alert_manager = Mock()
    mock_alert_manager.send_alert_with_stats = AsyncMock(return_value={
        'user_sent': True,
        'admins_sent': 1,
        'admins_failed': 0,
        'total_admins': 1
    })
    mock_alert_manager.send_alert_to_all = AsyncMock()
    return mock_alert_manager


class TestTelethonManagerInit:
    """Тесты инициализации TelethonManager"""
    
    def test_init_creates_empty_clients_dict(self):
        """Тест создания пустого словаря клиентов"""
        mock_alert_manager = create_mock_alert_manager()
        manager = TelethonManager(mock_alert_manager)

        assert isinstance(manager.clients, dict)
        assert len(manager.clients) == 0
        assert manager.alert_manager == mock_alert_manager
    
    def test_init_creates_stopwords_manager(self):
        """Тест создания экземпляра StopWordsManager"""
        mock_alert_manager = create_mock_alert_manager()
        manager = TelethonManager(mock_alert_manager)

        assert isinstance(manager.stopwords_manager, StopWordsManager)
    
    def test_init_with_alert_manager(self):
        """Тест инициализации с AlertManager"""
        mock_alert_manager = create_mock_alert_manager()
        manager = TelethonManager(mock_alert_manager)

        assert manager.alert_manager == mock_alert_manager
        assert manager._initialization_done is False


class TestStartAuth:
    """Тесты метода start_auth()"""
    
    @pytest.mark.asyncio
    async def test_start_auth_success_new_user(self):
        """Тест успешного начала авторизации для нового пользователя"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        
        mock_client = TelethonMockFactory.create_client(is_authorized=False)
        
        with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client):
            with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                result = await manager.start_auth(user_id, phone)
                
                assert result is True
                assert user_id in manager.clients
                assert manager.clients[user_id]['client'] == mock_client
                assert manager.clients[user_id]['phone'] == phone
                assert manager.clients[user_id]['monitoring'] is False
                
                mock_client.connect.assert_called_once()
                mock_client.is_user_authorized.assert_called_once()
                mock_client.send_code_request.assert_called_once_with(phone)
    
    @pytest.mark.asyncio
    async def test_start_auth_already_authorized(self):
        """Тест начала авторизации для уже авторизованного пользователя"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        
        mock_client = TelethonMockFactory.create_client(is_authorized=True)
        
        with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client):
            with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                result = await manager.start_auth(user_id, phone)
                
                assert result is True
                assert user_id in manager.clients
                
                mock_client.connect.assert_called_once()
                mock_client.is_user_authorized.assert_called_once()
                mock_client.send_code_request.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_start_auth_connection_error(self):
        """Тест ошибки подключения при начале авторизации"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        
        mock_client = TelethonMockFactory.create_client()
        mock_client.connect.side_effect = ConnectionError("Connection failed")
        
        with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client):
            with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                with pytest.raises(Exception, match="Connection failed"):
                    await manager.start_auth(user_id, phone)
                
                assert user_id not in manager.clients
    
    @pytest.mark.asyncio
    async def test_start_auth_send_code_error(self):
        """Тест ошибки отправки кода"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        
        mock_client = TelethonMockFactory.create_client(is_authorized=False)
        mock_client.send_code_request.side_effect = FloodWaitError("Too many requests", seconds=30)
        
        with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client):
            with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                with pytest.raises(Exception):
                    await manager.start_auth(user_id, phone)
    
    @pytest.mark.asyncio
    async def test_start_auth_replaces_existing_client(self):
        """Тест замены существующего клиента"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        
        # Добавляем существующего клиента
        old_client = Mock()
        manager.clients[user_id] = {
            'client': old_client,
            'phone': '+79997654321',
            'monitoring': True
        }
        
        mock_client = TelethonMockFactory.create_client(is_authorized=False)
        
        with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client):
            with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                result = await manager.start_auth(user_id, phone)
                
                assert result is True
                assert manager.clients[user_id]['client'] == mock_client
                assert manager.clients[user_id]['phone'] == phone
                assert manager.clients[user_id]['monitoring'] is False
    
    @pytest.mark.parametrize("phone", VALID_PHONE_NUMBERS)
    @pytest.mark.asyncio
    async def test_start_auth_valid_phones(self, phone):
        """Параметризованный тест валидных номеров телефонов"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        
        mock_client = TelethonMockFactory.create_client(is_authorized=False)
        
        with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client):
            with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                result = await manager.start_auth(user_id, phone)
                
                assert result is True
                assert manager.clients[user_id]['phone'] == phone


class TestCompleteAuth:
    """Тесты метода complete_auth()"""
    
    @pytest.mark.asyncio
    async def test_complete_auth_success(self):
        """Тест успешного завершения авторизации"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        code = "12345"
        
        mock_client = TelethonMockFactory.create_client()
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': phone,
            'monitoring': False
        }
        
        alert_callback = AsyncMock()
        
        with patch.object(manager, 'start_monitoring') as mock_start_monitoring:
            result = await manager.complete_auth(user_id, phone, code, alert_callback)
            
            assert result is True
            assert manager.clients[user_id]['monitoring'] is True
            
            mock_client.sign_in.assert_called_once_with(phone, code)
            mock_start_monitoring.assert_called_once_with(user_id, alert_callback)
    
    @pytest.mark.asyncio
    async def test_complete_auth_client_not_found(self):
        """Тест завершения авторизации без существующего клиента"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        code = "12345"
        
        with pytest.raises(Exception, match="Клиент не найден"):
            await manager.complete_auth(user_id, phone, code)
    
    @pytest.mark.asyncio
    async def test_complete_auth_invalid_code(self):
        """Тест неверного кода подтверждения"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        code = "00000"
        
        mock_client = TelethonMockFactory.create_client(
            should_raise_on_auth=PhoneCodeInvalidError("Invalid code")
        )
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': phone,
            'monitoring': False
        }
        
        with pytest.raises(Exception, match="Неверный код подтверждения"):
            await manager.complete_auth(user_id, phone, code)
        
        # Клиент должен быть удален при ошибке
        assert user_id not in manager.clients
    
    @pytest.mark.asyncio
    async def test_complete_auth_2fa_required(self):
        """Тест требования двухфакторной аутентификации"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        code = "12345"
        
        mock_client = TelethonMockFactory.create_client(
            should_raise_on_auth=SessionPasswordNeededError("2FA required")
        )
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': phone,
            'monitoring': False
        }
        
        with pytest.raises(Exception, match="Требуется двухфакторная аутентификация"):
            await manager.complete_auth(user_id, phone, code)
        
        # Клиент должен быть удален при ошибке
        assert user_id not in manager.clients
    
    @pytest.mark.asyncio
    async def test_complete_auth_cleanup_on_error(self):
        """Тест очистки клиента при ошибке"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        code = "12345"
        
        mock_client = TelethonMockFactory.create_client()
        mock_client.sign_in.side_effect = Exception("Unexpected error")
        
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': phone,
            'monitoring': False
        }
        
        with pytest.raises(Exception, match="Unexpected error"):
            await manager.complete_auth(user_id, phone, code)
        
        # Клиент должен быть удален и отключен
        assert user_id not in manager.clients
        mock_client.disconnect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_complete_auth_disconnect_error_ignored(self):
        """Тест игнорирования ошибки отключения при очистке"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        code = "12345"
        
        mock_client = TelethonMockFactory.create_client()
        mock_client.sign_in.side_effect = Exception("Auth error")
        mock_client.disconnect.side_effect = Exception("Disconnect error")
        
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': phone,
            'monitoring': False
        }
        
        # Ошибка авторизации должна быть поднята, ошибка отключения - проигнорирована
        with pytest.raises(Exception, match="Auth error"):
            await manager.complete_auth(user_id, phone, code)
        
        assert user_id not in manager.clients
    
    @pytest.mark.parametrize("code", VALID_CONFIRMATION_CODES)
    @pytest.mark.asyncio
    async def test_complete_auth_valid_codes(self, code):
        """Параметризованный тест валидных кодов подтверждения"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"
        
        mock_client = TelethonMockFactory.create_client()
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': phone,
            'monitoring': False
        }
        
        with patch.object(manager, 'start_monitoring'):
            result = await manager.complete_auth(user_id, phone, code)

            assert result is True
            mock_client.sign_in.assert_called_once_with(phone, code)


class TestStartMonitoring:
    """Тесты метода start_monitoring()"""

    @pytest.mark.asyncio
    async def test_start_monitoring_success(self):
        """Тест успешного запуска мониторинга"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        mock_client = TelethonMockFactory.create_client()
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': '+***********',
            'monitoring': False
        }

        alert_callback = AsyncMock()

        await manager.start_monitoring(user_id, alert_callback)

        # Проверяем что обработчик событий добавлен
        mock_client.add_event_handler.assert_called_once()

        # Проверяем что запущена задача поддержания соединения
        assert hasattr(manager, '_monitoring_tasks')

    @pytest.mark.asyncio
    async def test_start_monitoring_client_not_found(self):
        """Тест запуска мониторинга без существующего клиента"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        with pytest.raises(Exception, match="Клиент не найден"):
            await manager.start_monitoring(user_id)

    @pytest.mark.asyncio
    async def test_start_monitoring_message_handler(self):
        """Тест обработчика сообщений мониторинга"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        # Настраиваем стоп-слова
        manager.stopwords_manager.contains_stopwords = Mock(return_value=(True, ["спам"]))

        mock_client = TelethonMockFactory.create_client()
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': '+***********',
            'monitoring': False
        }

        alert_callback = AsyncMock()

        await manager.start_monitoring(user_id, alert_callback)

        # Получаем обработчик событий
        call_args = mock_client.add_event_handler.call_args
        event_handler = call_args[0][0]

        # Создаем тестовое событие
        mock_event = TelethonMockFactory.create_new_message_event(
            text="Это спам сообщение",
            is_outgoing=True
        )

        # Вызываем обработчик
        await event_handler(mock_event)

        # Проверяем что callback был вызван
        alert_callback.assert_called_once()
        call_text = alert_callback.call_args[0][0]
        assert "спам" in call_text.lower()

    @pytest.mark.asyncio
    async def test_start_monitoring_ignores_incoming_messages(self):
        """Тест игнорирования входящих сообщений"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        mock_client = TelethonMockFactory.create_client()
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': '+***********',
            'monitoring': False
        }

        alert_callback = AsyncMock()

        await manager.start_monitoring(user_id, alert_callback)

        # Получаем обработчик событий
        call_args = mock_client.add_event_handler.call_args
        event_handler = call_args[0][0]

        # Создаем входящее сообщение
        mock_event = TelethonMockFactory.create_new_message_event(
            text="Входящее сообщение",
            is_outgoing=False
        )

        # Вызываем обработчик
        await event_handler(mock_event)

        # Callback не должен быть вызван
        alert_callback.assert_not_called()

    @pytest.mark.asyncio
    async def test_start_monitoring_no_stopwords_found(self):
        """Тест обработки сообщений без стоп-слов"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        # Настраиваем отсутствие стоп-слов
        manager.stopwords_manager.contains_stopwords = Mock(return_value=(False, []))

        mock_client = TelethonMockFactory.create_client()
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': '+***********',
            'monitoring': False
        }

        alert_callback = AsyncMock()

        await manager.start_monitoring(user_id, alert_callback)

        # Получаем обработчик событий
        call_args = mock_client.add_event_handler.call_args
        event_handler = call_args[0][0]

        # Создаем тестовое событие
        mock_event = TelethonMockFactory.create_new_message_event(
            text="Обычное сообщение",
            is_outgoing=True
        )

        # Вызываем обработчик
        await event_handler(mock_event)

        # Callback не должен быть вызван
        alert_callback.assert_not_called()

    @pytest.mark.asyncio
    async def test_start_monitoring_handler_exception(self):
        """Тест обработки исключений в обработчике событий"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        # Настраиваем исключение в проверке стоп-слов
        manager.stopwords_manager.contains_stopwords = Mock(
            side_effect=Exception("Stopwords error")
        )

        mock_client = TelethonMockFactory.create_client()
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': '+***********',
            'monitoring': False
        }

        alert_callback = AsyncMock()

        await manager.start_monitoring(user_id, alert_callback)

        # Получаем обработчик событий
        call_args = mock_client.add_event_handler.call_args
        event_handler = call_args[0][0]

        # Создаем тестовое событие
        mock_event = TelethonMockFactory.create_new_message_event()

        # Обработчик не должен падать при исключении
        try:
            await event_handler(mock_event)
        except Exception:
            pytest.fail("Event handler should not raise exceptions")


class TestDisconnectClient:
    """Тесты метода disconnect_client()"""

    @pytest.mark.asyncio
    async def test_disconnect_client_success(self):
        """Тест успешного отключения клиента"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        mock_client = TelethonMockFactory.create_client()
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': '+***********',
            'monitoring': True
        }

        await manager.disconnect_client(user_id)

        mock_client.disconnect.assert_called_once()
        assert user_id not in manager.clients

    @pytest.mark.asyncio
    async def test_disconnect_client_not_found(self):
        """Тест отключения несуществующего клиента"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        # Не должно вызывать исключение
        await manager.disconnect_client(user_id)

    @pytest.mark.asyncio
    async def test_disconnect_client_with_error(self):
        """Тест обработки ошибки при отключении"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        mock_client = TelethonMockFactory.create_client()
        mock_client.disconnect.side_effect = Exception("Disconnect error")

        manager.clients[user_id] = {
            'client': mock_client,
            'phone': '+***********',
            'monitoring': True
        }

        # Ошибка должна быть проигнорирована, клиент удален
        await manager.disconnect_client(user_id)

        assert user_id not in manager.clients


class TestGetConnectedAccounts:
    """Тесты метода get_connected_accounts()"""

    def test_get_connected_accounts_empty(self):
        """Тест получения пустого списка аккаунтов"""
        manager = TelethonManager()

        accounts = manager.get_connected_accounts()

        assert accounts == []

    def test_get_connected_accounts_single(self):
        """Тест получения одного аккаунта"""
        manager = TelethonManager()
        phone = '+***********'

        mock_client = Mock()
        manager.clients[phone] = {
            'client': mock_client,
            'monitoring': True,
            'session_file': 'test_session.session'
        }

        accounts = manager.get_connected_accounts()

        assert len(accounts) == 1
        assert accounts[0]['user_id'] == phone  # Для обратной совместимости
        assert accounts[0]['phone'] == phone
        assert accounts[0]['monitoring'] is True
        assert accounts[0]['session_file'] == 'test_session.session'

    def test_get_connected_accounts_multiple(self):
        """Тест получения нескольких аккаунтов"""
        manager = TelethonManager()

        test_accounts = create_test_accounts()

        for account in test_accounts:
            phone = account['phone']
            manager.clients[phone] = {
                'client': Mock(),
                'monitoring': account['monitoring'],
                'session_file': f'{phone.replace("+", "")}.session'
            }

        accounts = manager.get_connected_accounts()

        assert len(accounts) == len(test_accounts)

        for i, account in enumerate(accounts):
            assert account['user_id'] == test_accounts[i]['phone']  # Для обратной совместимости
            assert account['phone'] == test_accounts[i]['phone']
            assert account['monitoring'] == test_accounts[i]['monitoring']
            assert 'session_file' in account

    def test_get_connected_accounts_mixed_monitoring_status(self):
        """Тест получения аккаунтов с разным статусом мониторинга"""
        manager = TelethonManager()

        # Активный аккаунт
        phone1 = '+***********'
        manager.clients[phone1] = {
            'client': Mock(),
            'monitoring': True,
            'session_file': '***********.session'
        }

        # Неактивный аккаунт
        phone2 = '+***********'
        manager.clients[phone2] = {
            'client': Mock(),
            'monitoring': False,
            'session_file': '***********.session'
        }

        accounts = manager.get_connected_accounts()

        assert len(accounts) == 2

        active_accounts = [acc for acc in accounts if acc['monitoring']]
        inactive_accounts = [acc for acc in accounts if not acc['monitoring']]

        assert len(active_accounts) == 1
        assert len(inactive_accounts) == 1

        # Проверяем, что все аккаунты имеют необходимые поля
        for account in accounts:
            assert 'user_id' in account
            assert 'phone' in account
            assert 'monitoring' in account
            assert 'session_file' in account


class TestTelethonManagerEdgeCases:
    """Тесты граничных случаев и безопасности TelethonManager"""

    @pytest.mark.asyncio
    async def test_multiple_auth_attempts_same_user(self):
        """Тест множественных попыток авторизации одного пользователя"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"

        mock_client1 = TelethonMockFactory.create_client(is_authorized=False)
        mock_client2 = TelethonMockFactory.create_client(is_authorized=False)

        with patch('src.monitoring.telethon_manager.TelegramClient', side_effect=[mock_client1, mock_client2]):
            with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                # Первая попытка
                result1 = await manager.start_auth(user_id, phone)
                assert result1 is True
                assert manager.clients[user_id]['client'] == mock_client1

                # Вторая попытка должна заменить первую
                result2 = await manager.start_auth(user_id, phone)
                assert result2 is True
                assert manager.clients[user_id]['client'] == mock_client2

    @pytest.mark.asyncio
    async def test_concurrent_auth_different_users(self):
        """Тест одновременной авторизации разных пользователей"""
        manager = TelethonManager()
        user1_id = TEST_REGULAR_USER_ID
        user2_id = TEST_ADMIN_USER_ID
        phone1 = "+***********"
        phone2 = "+***********"

        mock_client1 = TelethonMockFactory.create_client(is_authorized=False)
        mock_client2 = TelethonMockFactory.create_client(is_authorized=False)

        with patch('src.monitoring.telethon_manager.TelegramClient', side_effect=[mock_client1, mock_client2]):
            with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                # Одновременная авторизация
                results = await asyncio.gather(
                    manager.start_auth(user1_id, phone1),
                    manager.start_auth(user2_id, phone2)
                )

                assert all(results)
                assert len(manager.clients) == 2
                assert user1_id in manager.clients
                assert user2_id in manager.clients

    @pytest.mark.asyncio
    async def test_accounts_persistence(self):
        """Тест персистентности аккаунтов"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"

        # Мокаем методы сохранения/загрузки
        with patch.object(manager, '_save_accounts_metadata') as mock_save, \
             patch.object(manager, '_load_accounts_metadata', return_value={}):

            mock_client = TelethonMockFactory.create_client()

            with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client):
                with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                    # Подключаем аккаунт
                    await manager.start_auth(user_id, phone)

                    # Проверяем что метаданные сохранились
                    mock_save.assert_called()

                    # Проверяем что аккаунт в памяти
                    assert user_id in manager.clients
                    assert manager.clients[user_id]['phone'] == phone

    @pytest.mark.asyncio
    async def test_initialize_existing_accounts(self):
        """Тест инициализации существующих аккаунтов"""
        mock_alert_manager = create_mock_alert_manager()
        manager = TelethonManager(mock_alert_manager)

        # Мокаем метаданные существующего аккаунта
        existing_metadata = {
            "*********": {
                "phone": "+***********",
                "monitoring": True,
                "last_updated": "2025-07-28T12:00:00.000000"
            }
        }

        with patch.object(manager, '_load_accounts_metadata', return_value=existing_metadata), \
             patch('src.monitoring.telethon_manager.SESSIONS_DIR') as mock_sessions_dir:

            # Мокаем файлы сессий
            mock_session_file = Path('/tmp/sessions/*********.session')
            mock_sessions_dir.exists.return_value = True
            mock_sessions_dir.glob.return_value = [mock_session_file]

            mock_client = TelethonMockFactory.create_client()

            with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client), \
                 patch.object(manager, 'start_monitoring') as mock_start_monitoring:

                # Инициализируем
                await manager._initialize_existing_accounts()

                # Проверяем что аккаунт восстановлен
                assert ********* in manager.clients
                assert manager.clients[*********]['phone'] == "+***********"

                # Проверяем что мониторинг запущен с alert_callback
                mock_start_monitoring.assert_called_once()
                call_args = mock_start_monitoring.call_args
                assert call_args[0][0] == *********  # user_id
                assert call_args[0][1] is not None  # alert_callback должен быть передан

    def test_create_alert_callback_with_alert_manager(self):
        """Тест создания alert_callback с AlertManager"""
        mock_alert_manager = create_mock_alert_manager()
        manager = TelethonManager(mock_alert_manager)
        user_id = TEST_REGULAR_USER_ID

        alert_callback = manager._create_alert_callback(user_id)

        assert alert_callback is not None
        assert callable(alert_callback)

    def test_create_alert_callback_without_alert_manager(self):
        """Тест создания alert_callback без AlertManager"""
        manager = TelethonManager(None)
        user_id = TEST_REGULAR_USER_ID

        alert_callback = manager._create_alert_callback(user_id)

        assert alert_callback is None

    @pytest.mark.asyncio
    async def test_alert_callback_functionality(self):
        """Тест функциональности созданного alert_callback"""
        mock_alert_manager = create_mock_alert_manager()
        manager = TelethonManager(mock_alert_manager)
        user_id = TEST_REGULAR_USER_ID
        alert_text = "Test alert"

        alert_callback = manager._create_alert_callback(user_id)

        # Вызываем callback
        await alert_callback(alert_text)

        # Проверяем что AlertManager был вызван
        mock_alert_manager.send_alert_with_stats.assert_called_once_with(user_id, alert_text)

    @pytest.mark.asyncio
    async def test_auth_with_invalid_user_id(self):
        """Тест авторизации с невалидным user_id"""
        manager = TelethonManager()
        invalid_user_ids = [0, -1, None, "invalid"]
        phone = "+***********"

        for invalid_id in invalid_user_ids:
            if invalid_id is None or isinstance(invalid_id, str):
                # Эти случаи могут вызвать TypeError
                with pytest.raises((TypeError, ValueError)):
                    await manager.start_auth(invalid_id, phone)
            else:
                # Числовые ID должны обрабатываться
                mock_client = TelethonMockFactory.create_client(is_authorized=False)
                with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client):
                    with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                        result = await manager.start_auth(invalid_id, phone)
                        assert isinstance(result, bool)

    @pytest.mark.parametrize("invalid_phone", INVALID_PHONE_NUMBERS)
    @pytest.mark.asyncio
    async def test_auth_with_invalid_phone(self, invalid_phone):
        """Параметризованный тест авторизации с невалидными номерами"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        mock_client = TelethonMockFactory.create_client(is_authorized=False)

        with patch('src.monitoring.telethon_manager.TelegramClient', return_value=mock_client):
            with patch('src.monitoring.telethon_manager.SESSIONS_DIR', Path('/tmp/sessions')):
                # Некоторые невалидные номера могут вызвать исключения в Telethon
                try:
                    result = await manager.start_auth(user_id, invalid_phone)
                    # Если исключение не возникло, проверяем что номер сохранен
                    assert manager.clients[user_id]['phone'] == invalid_phone
                except Exception:
                    # Исключения от Telethon допустимы для невалидных номеров
                    pass

    @pytest.mark.parametrize("invalid_code", INVALID_CONFIRMATION_CODES)
    @pytest.mark.asyncio
    async def test_complete_auth_with_invalid_codes(self, invalid_code):
        """Параметризованный тест завершения авторизации с невалидными кодами"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID
        phone = "+***********"

        mock_client = TelethonMockFactory.create_client(
            should_raise_on_auth=PhoneCodeInvalidError("Invalid code")
        )
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': phone,
            'monitoring': False
        }

        with pytest.raises(Exception):
            await manager.complete_auth(user_id, phone, invalid_code)

        # Клиент должен быть удален при ошибке
        assert user_id not in manager.clients

    @pytest.mark.asyncio
    async def test_monitoring_with_malicious_message_content(self):
        """Тест мониторинга с вредоносным содержимым сообщений"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        # Настраиваем обнаружение стоп-слов
        manager.stopwords_manager.contains_stopwords = Mock(return_value=(True, ["test"]))

        mock_client = TelethonMockFactory.create_client()
        manager.clients[user_id] = {
            'client': mock_client,
            'phone': '+***********',
            'monitoring': False
        }

        alert_callback = AsyncMock()

        await manager.start_monitoring(user_id, alert_callback)

        # Получаем обработчик событий
        call_args = mock_client.add_event_handler.call_args
        event_handler = call_args[0][0]

        # Тестируем различные вредоносные сообщения
        malicious_messages = [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "\x00\x01\x02\x03",  # Бинарные данные
            "A" * 10000,  # Очень длинное сообщение
        ]

        for malicious_text in malicious_messages:
            mock_event = TelethonMockFactory.create_new_message_event(
                text=malicious_text,
                is_outgoing=True
            )

            # Обработчик не должен падать
            try:
                await event_handler(mock_event)
            except Exception as e:
                pytest.fail(f"Handler failed with malicious input '{malicious_text}': {e}")

    @pytest.mark.asyncio
    async def test_memory_cleanup_on_multiple_disconnects(self):
        """Тест очистки памяти при множественных отключениях"""
        manager = TelethonManager()

        # Создаем несколько клиентов
        user_ids = [123, 456, 789]
        for user_id in user_ids:
            mock_client = TelethonMockFactory.create_client()
            manager.clients[user_id] = {
                'client': mock_client,
                'phone': f'+7999{user_id}',
                'monitoring': True
            }

        # Отключаем всех клиентов
        for user_id in user_ids:
            await manager.disconnect_client(user_id)

        # Проверяем что память очищена
        assert len(manager.clients) == 0

    @pytest.mark.asyncio
    async def test_error_handling_in_stopwords_check(self):
        """Тест обработки ошибок при проверке стоп-слов"""
        manager = TelethonManager()
        user_id = TEST_REGULAR_USER_ID

        # Настраиваем исключение в проверке стоп-слов
        error_types = [
            Exception("General error"),
            AttributeError("Attribute error"),
            TypeError("Type error"),
            ValueError("Value error")
        ]

        for error in error_types:
            manager.stopwords_manager.contains_stopwords = Mock(side_effect=error)

            mock_client = TelethonMockFactory.create_client()
            manager.clients[user_id] = {
                'client': mock_client,
                'phone': '+***********',
                'monitoring': False
            }

            alert_callback = AsyncMock()

            await manager.start_monitoring(user_id, alert_callback)

            # Получаем обработчик событий
            call_args = mock_client.add_event_handler.call_args
            event_handler = call_args[0][0]

            mock_event = TelethonMockFactory.create_new_message_event()

            # Обработчик не должен падать при любых ошибках
            try:
                await event_handler(mock_event)
            except Exception as e:
                pytest.fail(f"Handler failed with error {type(error).__name__}: {e}")

            # Очищаем для следующей итерации
            manager.clients.clear()

    def test_clients_dict_thread_safety_simulation(self):
        """Симуляция потокобезопасности словаря клиентов"""
        manager = TelethonManager()

        # Симулируем одновременные операции
        user_ids = list(range(100))

        # Добавляем клиентов
        for user_id in user_ids:
            mock_client = Mock()
            manager.clients[user_id] = {
                'client': mock_client,
                'phone': f'+7999{user_id:07d}',
                'monitoring': user_id % 2 == 0
            }

        # Проверяем что все клиенты добавлены
        assert len(manager.clients) == 100

        # Удаляем половину клиентов
        for user_id in user_ids[::2]:
            del manager.clients[user_id]

        # Проверяем что осталось 50 клиентов
        assert len(manager.clients) == 50

        # Проверяем корректность данных
        accounts = manager.get_connected_accounts()
        assert len(accounts) == 50

        for account in accounts:
            assert account['user_id'] % 2 == 1  # Остались только нечетные ID


class TestBotFiltering:
    """Тесты функциональности фильтрации ботов"""

    def test_is_bot_sender_with_bot_flag(self):
        """Тест определения бота по флагу bot"""
        manager = TelethonManager()

        # Создаем mock объект пользователя-бота
        mock_bot_sender = Mock()
        mock_bot_sender.bot = True
        mock_bot_sender.username = "test_bot"

        result = manager._is_bot_sender(mock_bot_sender)
        assert result is True

    def test_is_bot_sender_with_bot_username(self):
        """Тест определения бота по username с окончанием _bot"""
        manager = TelethonManager()

        # Создаем mock объект пользователя с bot username
        mock_bot_sender = Mock()
        mock_bot_sender.bot = False  # Флаг bot отсутствует
        mock_bot_sender.username = "PriemkaLitva_bot"

        result = manager._is_bot_sender(mock_bot_sender)
        assert result is True

    def test_is_bot_sender_regular_user(self):
        """Тест определения обычного пользователя"""
        manager = TelethonManager()

        # Создаем mock объект обычного пользователя
        mock_user_sender = Mock()
        mock_user_sender.bot = False
        mock_user_sender.username = "regular_user"

        result = manager._is_bot_sender(mock_user_sender)
        assert result is False

    def test_is_bot_sender_no_username(self):
        """Тест определения пользователя без username"""
        manager = TelethonManager()

        # Создаем mock объект пользователя без username
        mock_user_sender = Mock()
        mock_user_sender.bot = False
        mock_user_sender.username = None

        result = manager._is_bot_sender(mock_user_sender)
        assert result is False

    def test_is_bot_sender_with_none_sender(self):
        """Тест обработки None в качестве отправителя"""
        manager = TelethonManager()

        # Проверяем обработку None
        result = manager._is_bot_sender(None)
        # При None должен возвращать False (безопасный fallback)
        assert result is False

    def test_is_bot_sender_various_bot_patterns(self):
        """Тест различных паттернов ботов"""
        manager = TelethonManager()

        test_cases = [
            ("support_bot", True),
            ("helper_bot", True),
            ("assistant_bot", True),
            ("notification_bot", True),
            ("regular_user", False),
            ("user_support", False),  # Содержит support, но не заканчивается на _bot
            ("botuser", False),  # Содержит bot, но не заканчивается на _bot
        ]

        for username, expected in test_cases:
            mock_sender = Mock()
            mock_sender.bot = False  # Проверяем только по username
            mock_sender.username = username

            result = manager._is_bot_sender(mock_sender)
            assert result == expected, f"Failed for username: {username}"

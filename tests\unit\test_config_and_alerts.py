"""
Тесты конфигурации и алертов PM Searcher Bot

Покрывает валидацию переменных окружения и отправку алертов
с мокированием Bot API.
"""

import pytest
import os
from unittest.mock import Mock, AsyncMock, patch

from aiogram import Bot

from src.config.environment import EnvironmentConfig, env_config
from src.bot.alerts import AlertManager
from tests.fixtures.test_constants import TEST_ADMIN_USER_ID, TEST_REGULAR_USER_ID
from tests.utils.mock_factories import MockFactory, AlertMockFactory


class TestEnvironmentConfig:
    """Тесты класса EnvironmentConfig"""
    
    def test_environment_config_initialization(self):
        """Тест инициализации конфигурации окружения"""
        config = EnvironmentConfig()
        
        # Проверяем что основные атрибуты существуют
        assert hasattr(config, 'BOT_TOKEN')
        assert hasattr(config, 'API_ID')
        assert hasattr(config, 'API_HASH')
        assert hasattr(config, 'ADMIN_CHAT_IDS')
    
    def test_environment_config_from_env_vars(self):
        """Тест загрузки конфигурации из переменных окружения"""
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN',
            'API_ID': '12345',
            'API_HASH': 'test_api_hash',
            'ADMIN_CHAT_IDS': '123456789,987654321'
        }
        
        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()
            
            assert config.BOT_TOKEN == '123456:TEST_BOT_TOKEN'
            assert config.API_ID == 12345
            assert config.API_HASH == 'test_api_hash'
            assert config.ADMIN_CHAT_IDS == [123456789, 987654321]
    
    def test_environment_config_missing_required_vars(self):
        """Тест обработки отсутствующих обязательных переменных"""
        # Очищаем переменные окружения
        required_vars = ['BOT_TOKEN', 'API_ID', 'API_HASH', 'ADMIN_CHAT_IDS']
        
        with patch.dict(os.environ, {}, clear=True):
            config = EnvironmentConfig()
            
            # Проверяем что конфигурация создается с дефолтными значениями
            assert config.BOT_TOKEN is None or config.BOT_TOKEN == ""
            assert config.API_ID is None or config.API_ID == 0
            assert config.API_HASH is None or config.API_HASH == ""
            assert config.ADMIN_CHAT_IDS == []
    
    def test_environment_config_invalid_api_id(self):
        """Тест обработки невалидного API_ID"""
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN',
            'API_ID': 'invalid_number',
            'API_HASH': 'test_api_hash',
            'ADMIN_CHAT_IDS': '123456789'
        }
        
        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()
            
            # API_ID должен быть 0 или None при невалидном значении
            assert config.API_ID == 0 or config.API_ID is None
    
    def test_environment_config_invalid_admin_chat_ids(self):
        """Тест обработки невалидных ADMIN_CHAT_IDS"""
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN',
            'API_ID': '12345',
            'API_HASH': 'test_api_hash',
            'ADMIN_CHAT_IDS': 'invalid,123456789,another_invalid'
        }
        
        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()
            
            # Должны быть отфильтрованы только валидные ID
            assert isinstance(config.ADMIN_CHAT_IDS, list)
            assert 123456789 in config.ADMIN_CHAT_IDS
            # Невалидные значения должны быть исключены
            assert len([x for x in config.ADMIN_CHAT_IDS if isinstance(x, int)]) >= 1


class TestEnvironmentConfigValidation:
    """Тесты валидации конфигурации окружения"""
    
    def test_validate_complete_config(self):
        """Тест валидации полной конфигурации"""
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN_WITH_CORRECT_FORMAT',
            'API_ID': '12345',
            'API_HASH': 'test_api_hash_32_characters_long',
            'ADMIN_CHAT_IDS': '123456789,987654321'
        }
        
        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()
            
            is_valid, errors = config.validate()
            
            assert is_valid is True
            assert len(errors) == 0
    
    def test_validate_missing_bot_token(self):
        """Тест валидации с отсутствующим BOT_TOKEN"""
        test_env_vars = {
            'API_ID': '12345',
            'API_HASH': 'test_api_hash',
            'ADMIN_CHAT_IDS': '123456789'
        }
        
        with patch.dict(os.environ, test_env_vars, clear=True):
            config = EnvironmentConfig()
            
            is_valid, errors = config.validate()
            
            assert is_valid is False
            assert any('BOT_TOKEN' in error for error in errors)
    
    def test_validate_invalid_bot_token_format(self):
        """Тест валидации с невалидным форматом BOT_TOKEN"""
        test_env_vars = {
            'BOT_TOKEN': 'invalid_token_format',
            'API_ID': '12345',
            'API_HASH': 'test_api_hash',
            'ADMIN_CHAT_IDS': '123456789'
        }
        
        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()
            
            is_valid, errors = config.validate()
            
            assert is_valid is False
            assert any('BOT_TOKEN' in error and 'формат' in error.lower() for error in errors)
    
    def test_validate_missing_api_credentials(self):
        """Тест валидации с отсутствующими API учетными данными"""
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN',
            'ADMIN_CHAT_IDS': '123456789'
        }
        
        with patch.dict(os.environ, test_env_vars, clear=True):
            config = EnvironmentConfig()
            
            is_valid, errors = config.validate()
            
            assert is_valid is False
            assert any('API_ID' in error for error in errors)
            assert any('API_HASH' in error for error in errors)
    
    def test_validate_no_admin_chat_ids(self):
        """Тест валидации без администраторских чатов"""
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN',
            'API_ID': '12345',
            'API_HASH': 'test_api_hash'
        }
        
        with patch.dict(os.environ, test_env_vars, clear=True):
            config = EnvironmentConfig()
            
            is_valid, errors = config.validate()
            
            # Может быть валидным или невалидным в зависимости от требований
            # Проверяем что есть предупреждение об отсутствии админов
            if not is_valid:
                assert any('ADMIN_CHAT_IDS' in error for error in errors)


class TestEnvironmentConfigIsAdmin:
    """Тесты метода is_admin()"""
    
    def test_is_admin_valid_admin(self):
        """Тест проверки валидного администратора"""
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN',
            'API_ID': '12345',
            'API_HASH': 'test_api_hash',
            'ADMIN_CHAT_ID': f'{TEST_ADMIN_USER_ID},{TEST_REGULAR_USER_ID}'  # Исправлено: ADMIN_CHAT_ID вместо ADMIN_CHAT_IDS
        }

        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()

            assert config.is_admin(TEST_ADMIN_USER_ID) is True
            assert config.is_admin(TEST_REGULAR_USER_ID) is True
    
    def test_is_admin_invalid_admin(self):
        """Тест проверки невалидного администратора"""
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN',
            'API_ID': '12345',
            'API_HASH': 'test_api_hash',
            'ADMIN_CHAT_ID': f'{TEST_ADMIN_USER_ID}'  # Исправлено: ADMIN_CHAT_ID вместо ADMIN_CHAT_IDS
        }

        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()

            assert config.is_admin(TEST_ADMIN_USER_ID) is True
            assert config.is_admin(TEST_REGULAR_USER_ID) is False
            assert config.is_admin(999999999) is False
    
    def test_is_admin_no_admins_configured(self):
        """Тест проверки администратора когда админы не настроены"""
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN',
            'API_ID': '12345',
            'API_HASH': 'test_api_hash',
            'ADMIN_CHAT_ID': ''  # Исправлено: ADMIN_CHAT_ID вместо ADMIN_CHAT_IDS
        }
        
        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()
            
            assert config.is_admin(TEST_ADMIN_USER_ID) is False
            assert config.is_admin(TEST_REGULAR_USER_ID) is False
    
    def test_is_admin_edge_cases(self):
        """Тест граничных случаев проверки администратора"""
        test_env_vars = {
            'ADMIN_CHAT_ID': f'{TEST_ADMIN_USER_ID}'  # Исправлено: ADMIN_CHAT_ID вместо ADMIN_CHAT_IDS
        }
        
        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()
            
            # Тест с None
            assert config.is_admin(None) is False
            
            # Тест с 0
            assert config.is_admin(0) is False
            
            # Тест с отрицательным числом
            assert config.is_admin(-1) is False
            
            # Тест со строкой
            try:
                result = config.is_admin("123456789")
                assert isinstance(result, bool)
            except (TypeError, ValueError):
                # Ожидаемое поведение для невалидного типа
                pass

    def test_is_admin_multiple_admins(self):
        """Тест проверки нескольких администраторов"""
        admin_ids = [7738438019, 7506112063, 1290467190]
        test_env_vars = {
            'BOT_TOKEN': '123456:TEST_BOT_TOKEN',
            'API_ID': '12345',
            'API_HASH': 'test_api_hash',
            'ADMIN_CHAT_ID': ','.join(map(str, admin_ids))
        }

        with patch.dict(os.environ, test_env_vars):
            config = EnvironmentConfig()

            # Проверяем что все админы распознаются
            for admin_id in admin_ids:
                assert config.is_admin(admin_id) is True, f"Admin {admin_id} should be recognized as admin"

            # Проверяем что неадмин не распознается
            assert config.is_admin(999999999) is False

            # Проверяем количество админов
            assert len(config.ADMIN_CHAT_IDS) == 3
            assert set(config.ADMIN_CHAT_IDS) == set(admin_ids)


class TestAlertManager:
    """Тесты класса AlertManager"""
    
    def test_alert_manager_initialization(self):
        """Тест инициализации AlertManager"""
        mock_bot = MockFactory.create_bot()
        
        alert_manager = AlertManager(mock_bot)
        
        assert alert_manager.bot == mock_bot
        assert hasattr(alert_manager, 'send_alert_to_user')
        assert hasattr(alert_manager, 'send_alert_to_admins')
    
    @pytest.mark.asyncio
    async def test_send_alert_to_user_success(self):
        """Тест успешной отправки алерта пользователю"""
        mock_bot = MockFactory.create_bot()
        alert_manager = AlertManager(mock_bot)
        
        user_id = TEST_REGULAR_USER_ID
        alert_text = "🚨 Обнаружено стоп-слово: спам"
        
        await alert_manager.send_alert_to_user(user_id, alert_text)
        
        # Проверяем что сообщение было отправлено
        mock_bot.send_message.assert_called_once_with(user_id, alert_text)
    
    @pytest.mark.asyncio
    async def test_send_alert_to_user_failure(self):
        """Тест обработки ошибки отправки алерта пользователю"""
        mock_bot = MockFactory.create_bot()
        mock_bot.send_message.side_effect = Exception("Send message failed")
        
        alert_manager = AlertManager(mock_bot)
        
        user_id = TEST_REGULAR_USER_ID
        alert_text = "Test alert"
        
        # Метод не должен выбрасывать исключение
        try:
            await alert_manager.send_alert_to_user(user_id, alert_text)
        except Exception as e:
            pytest.fail(f"send_alert_to_user should handle exceptions gracefully: {e}")
        
        # Проверяем что попытка отправки была сделана
        mock_bot.send_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_alert_to_admins_success(self):
        """Тест успешной отправки алерта администраторам"""
        mock_bot = MockFactory.create_bot()
        alert_manager = AlertManager(mock_bot)
        
        admin_ids = [TEST_ADMIN_USER_ID, 999999999]
        alert_text = "🚨 Системное уведомление"
        
        with patch.object(env_config, 'ADMIN_CHAT_IDS', admin_ids):
            await alert_manager.send_alert_to_admins(alert_text)
            
            # Проверяем что сообщения были отправлены всем админам
            assert mock_bot.send_message.call_count == len(admin_ids)
            
            # Проверяем параметры вызовов
            calls = mock_bot.send_message.call_args_list
            for i, call in enumerate(calls):
                args, kwargs = call
                assert args[0] == admin_ids[i]  # первый аргумент - user_id
                assert args[1] == alert_text     # второй аргумент - text
    
    @pytest.mark.asyncio
    async def test_send_alert_to_admins_no_admins(self):
        """Тест отправки алерта когда нет администраторов"""
        mock_bot = MockFactory.create_bot()
        alert_manager = AlertManager(mock_bot)
        
        alert_text = "Test alert"
        
        with patch.object(env_config, 'ADMIN_CHAT_IDS', []):
            await alert_manager.send_alert_to_admins(alert_text)
            
            # Сообщения не должны отправляться
            mock_bot.send_message.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_send_alert_to_admins_partial_failure(self):
        """Тест частичной неудачи отправки алертов администраторам"""
        mock_bot = MockFactory.create_bot()
        
        # Настраиваем мок так, чтобы первый вызов был успешным, второй - неудачным
        mock_bot.send_message.side_effect = [None, Exception("Failed to send")]
        
        alert_manager = AlertManager(mock_bot)
        
        admin_ids = [TEST_ADMIN_USER_ID, 999999999]
        alert_text = "Test alert"
        
        with patch.object(env_config, 'ADMIN_CHAT_IDS', admin_ids):
            # Метод не должен выбрасывать исключение
            try:
                await alert_manager.send_alert_to_admins(alert_text)
            except Exception as e:
                pytest.fail(f"send_alert_to_admins should handle partial failures: {e}")
            
            # Проверяем что были попытки отправки всем админам
            assert mock_bot.send_message.call_count == len(admin_ids)
    
    @pytest.mark.asyncio
    async def test_send_admin_notification_success(self):
        """Тест успешной отправки административного уведомления"""
        mock_bot = MockFactory.create_bot()
        alert_manager = AlertManager(mock_bot)

        notification_text = "📊 Новый пользователь подключил аккаунт"

        with patch.object(env_config, 'ADMIN_CHAT_IDS', [TEST_ADMIN_USER_ID]):
            await alert_manager.send_admin_notification(notification_text)

            # Проверяем что уведомление было отправлено
            mock_bot.send_message.assert_called_once()
            call_args = mock_bot.send_message.call_args

            assert call_args[0][0] == TEST_ADMIN_USER_ID  # первый аргумент - user_id
            assert notification_text in call_args[0][1]   # второй аргумент - text

    @pytest.mark.asyncio
    async def test_send_alert_to_admins_partial_failure(self):
        """Тест отправки алерта администраторам с частичными ошибками"""
        mock_bot = MockFactory.create_bot()
        alert_manager = AlertManager(mock_bot)

        admin_ids = [TEST_ADMIN_USER_ID, 999999999, 888888888]
        alert_text = "🚨 Тестовый алерт"

        # Настраиваем мок так, чтобы второй админ вызывал ошибку
        def side_effect(user_id, text):
            if user_id == 999999999:
                raise Exception("Бот заблокирован пользователем")
            return AsyncMock()

        mock_bot.send_message.side_effect = side_effect

        with patch.object(env_config, 'ADMIN_CHAT_IDS', admin_ids):
            # Не должно поднимать исключение
            await alert_manager.send_alert_to_admins(alert_text)

            # Проверяем что попытки отправки были сделаны всем админам
            assert mock_bot.send_message.call_count == 3

            # Проверяем параметры вызовов
            calls = mock_bot.send_message.call_args_list
            sent_to_ids = [call[0][0] for call in calls]
            assert set(sent_to_ids) == set(admin_ids)

    @pytest.mark.asyncio
    async def test_send_alert_with_stats_success(self):
        """Тест новой функции send_alert_with_stats"""
        mock_bot = MockFactory.create_bot()
        alert_manager = AlertManager(mock_bot)

        user_id = TEST_REGULAR_USER_ID
        admin_ids = [TEST_ADMIN_USER_ID, 999999999]
        alert_text = "🚨 Тестовый алерт со статистикой"

        with patch.object(env_config, 'ADMIN_CHAT_IDS', admin_ids):

            stats = await alert_manager.send_alert_with_stats(user_id, alert_text)

            # Проверяем статистику
            assert stats['user_sent'] is True
            assert stats['admins_sent'] == 2
            assert stats['admins_failed'] == 0
            assert stats['total_admins'] == 2

            # Проверяем что сообщения были отправлены
            assert mock_bot.send_message.call_count == 3  # пользователь + 2 админа
    
    @pytest.mark.asyncio
    async def test_alert_manager_edge_cases(self):
        """Тест граничных случаев AlertManager"""
        mock_bot = MockFactory.create_bot()
        alert_manager = AlertManager(mock_bot)
        
        # Тест с пустым текстом алерта
        await alert_manager.send_alert_to_user(TEST_REGULAR_USER_ID, "")
        mock_bot.send_message.assert_called_once()
        
        mock_bot.send_message.reset_mock()
        
        # Тест с None текстом алерта
        try:
            await alert_manager.send_alert_to_user(TEST_REGULAR_USER_ID, None)
        except Exception:
            # Может выбросить исключение для None текста
            pass
        
        # Тест с очень длинным текстом алерта
        long_text = "Очень длинный текст алерта " * 100
        await alert_manager.send_alert_to_user(TEST_REGULAR_USER_ID, long_text)
        
        # Проверяем что попытка отправки была сделана
        assert mock_bot.send_message.called

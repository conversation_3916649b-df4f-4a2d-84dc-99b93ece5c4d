#!/usr/bin/env python3
"""
Тест системы алертов для тимлидов при назначении аккаунтов
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import AsyncMock, Mock

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.teamleads_manager import teamleads_manager
from src.bot.alerts import AlertManager

async def test_teamlead_alert_system():
    """Тест системы алертов для тимлидов"""
    print("🧪 Тест системы алертов для тимлидов")
    print("=" * 50)
    
    # Создаем mock бота
    mock_bot = Mock()
    mock_bot.send_message = AsyncMock()
    
    # Создаем AlertManager
    alert_manager = AlertManager(mock_bot)
    
    # Тестовые данные
    test_phone = "+380508127592"
    test_admin_id = 1290467190
    test_teamlead_username = "ftyhbdd"
    test_teamlead_id = 1900852105
    
    print(f"📱 Тестовый номер: {test_phone}")
    print(f"👤 Тестовый тимлид: {test_teamlead_username} (ID: {test_teamlead_id})")
    print(f"👨‍💼 Администратор: {test_admin_id}")
    
    # Тест 1: Проверяем текущее состояние тимлидов
    print(f"\n1️⃣ Проверка текущих тимлидов...")
    all_teamleads = teamleads_manager.get_all_teamleads()
    print(f"   Всего тимлидов: {len(all_teamleads)}")
    
    for username, data in all_teamleads.items():
        assigned_phones = data.get('assigned_phones', [])
        print(f"   • {username} (ID: {data.get('user_id')}): {len(assigned_phones)} номеров")
    
    # Тест 2: Проверяем назначения для тестового номера
    print(f"\n2️⃣ Проверка назначений для {test_phone}...")
    assigned_teamleads = teamleads_manager.get_teamleads_for_phone(test_phone)
    print(f"   Назначено тимлидов: {len(assigned_teamleads)}")
    
    for teamlead in assigned_teamleads:
        username = teamlead.get('username')
        user_id = teamlead.get('user_id')
        print(f"   • {username} (ID: {user_id})")
    
    # Тест 3: Назначаем номер тимлиду (если еще не назначен)
    print(f"\n3️⃣ Назначение номера тимлиду...")
    if not assigned_teamleads:
        success = teamleads_manager.assign_phone_to_teamlead(test_phone, test_teamlead_username, test_admin_id)
        print(f"   Результат назначения: {'✅ Успешно' if success else '❌ Ошибка'}")
        
        if success:
            # Проверяем обновленные назначения
            updated_teamleads = teamleads_manager.get_teamleads_for_phone(test_phone)
            print(f"   Обновленные назначения: {len(updated_teamleads)} тимлидов")
    else:
        print(f"   Номер уже назначен тимлидам")
    
    # Тест 4: Тестируем отправку алерта времени ответа
    print(f"\n4️⃣ Тест алерта времени ответа...")
    
    test_alert_text = (
        f"🚨 ПРОСРОЧЕН ОТВЕТ МЕНЕДЖЕРА!\n\n"
        f"📱 Аккаунт: {test_phone}\n"
        f"👤 От: Тестовый Клиент\n"
        f"💬 Чат: Тестовый чат\n"
        f"📝 Сообщение: Срочный вопрос по продукту\n"
        f"⏰ Получено: {datetime.now().strftime('%H:%M:%S')}\n"
        f"⚠️ Дедлайн: {datetime.now().strftime('%H:%M:%S')}\n"
        f"🔴 Просрочено на: 5 минут\n"
        f"📅 Дата: {datetime.now().strftime('%Y-%m-%d')}"
    )
    
    # Отправляем алерт через систему маршрутизации
    await alert_manager.send_response_time_alert(test_phone, test_alert_text)
    
    # Проверяем вызовы mock бота
    call_count = mock_bot.send_message.call_count
    print(f"   Отправлено сообщений: {call_count}")
    
    if call_count > 0:
        # Анализируем вызовы
        calls = mock_bot.send_message.call_args_list
        recipients = []
        
        for call in calls:
            args, kwargs = call
            user_id = args[0] if args else kwargs.get('chat_id')
            recipients.append(user_id)
        
        print(f"   Получатели алертов:")
        for recipient in recipients:
            if recipient == test_teamlead_id:
                print(f"   • {recipient} (Тимлид {test_teamlead_username}) ✅")
            else:
                print(f"   • {recipient} (Администратор)")
        
        # Проверяем правильность маршрутизации
        teamleads_for_phone = teamleads_manager.get_teamleads_for_phone(test_phone)
        if teamleads_for_phone:
            expected_recipients = [tl.get('user_id') for tl in teamleads_for_phone]
            actual_recipients = recipients
            
            routing_correct = set(expected_recipients) == set(actual_recipients)
            print(f"   Маршрутизация корректна: {'✅ Да' if routing_correct else '❌ Нет'}")
            
            if not routing_correct:
                print(f"   Ожидались: {expected_recipients}")
                print(f"   Получили: {actual_recipients}")
        else:
            # Если тимлиды не назначены, алерт должен идти всем админам
            print(f"   Тимлиды не назначены → алерт отправлен администраторам")
    else:
        print(f"   ❌ Алерты не отправлены")
    
    # Тест 5: Тестируем алерт с фото
    print(f"\n5️⃣ Тест фото алерта...")
    
    test_photo_data = b"fake_photo_data"
    test_photo_caption = (
        f"📸 ВХОДЯЩЕЕ ФОТО\n\n"
        f"📱 Аккаунт: {test_phone}\n"
        f"👤 От: Тестовый Отправитель\n"
        f"💬 Чат: Тестовый чат\n"
        f"🕐 Время: {datetime.now().strftime('%H:%M:%S')}"
    )
    
    # Сбрасываем mock
    mock_bot.send_photo = AsyncMock()
    
    # Отправляем фото алерт
    await alert_manager.send_photo_alert_to_teamleads_for_phone(test_phone, test_photo_data, test_photo_caption)
    
    photo_call_count = mock_bot.send_photo.call_count
    print(f"   Отправлено фото: {photo_call_count}")
    
    if photo_call_count > 0:
        photo_calls = mock_bot.send_photo.call_args_list
        photo_recipients = []
        
        for call in photo_calls:
            args, kwargs = call
            user_id = args[0] if args else kwargs.get('chat_id')
            photo_recipients.append(user_id)
        
        print(f"   Получатели фото алертов:")
        for recipient in photo_recipients:
            if recipient == test_teamlead_id:
                print(f"   • {recipient} (Тимлид {test_teamlead_username}) ✅")
            else:
                print(f"   • {recipient} (Администратор)")
    
    # Тест 6: Проверяем fallback на админов
    print(f"\n6️⃣ Тест fallback на администраторов...")
    
    # Временно отменяем назначение
    if teamleads_manager.get_teamleads_for_phone(test_phone):
        teamleads_manager.unassign_phone_from_teamlead(test_phone, test_teamlead_username, test_admin_id)
        print(f"   Временно отменили назначение тимлида")
    
    # Сбрасываем mock
    mock_bot.send_message.reset_mock()
    
    # Отправляем алерт для номера без назначенных тимлидов
    await alert_manager.send_response_time_alert(test_phone, "Тест fallback алерта")
    
    fallback_call_count = mock_bot.send_message.call_count
    print(f"   Отправлено fallback сообщений: {fallback_call_count}")
    
    if fallback_call_count > 0:
        fallback_calls = mock_bot.send_message.call_args_list
        fallback_recipients = []
        
        for call in fallback_calls:
            args, kwargs = call
            user_id = args[0] if args else kwargs.get('chat_id')
            fallback_recipients.append(user_id)
        
        print(f"   Fallback получатели:")
        for recipient in fallback_recipients:
            print(f"   • {recipient} (Администратор)")
        
        # Проверяем, что это действительно админы
        from src.config.environment import env_config
        admin_chat_ids = env_config.ADMIN_CHAT_IDS

        admin_recipients = [r for r in fallback_recipients if r in admin_chat_ids]
        if admin_recipients:
            print(f"   ✅ Fallback работает корректно - алерт отправлен {len(admin_recipients)} админам")
        else:
            print(f"   ⚠️ Возможная проблема с fallback маршрутизацией")
    
    # Восстанавливаем назначение (если было)
    if assigned_teamleads:
        teamleads_manager.assign_phone_to_teamlead(test_phone, test_teamlead_username, test_admin_id)
        print(f"   Восстановили назначение тимлида")
    
    print(f"\n" + "=" * 50)
    print(f"✅ Тестирование системы алертов завершено!")
    
    # Итоги
    print(f"\n📊 Итоги тестирования:")
    print(f"• Система тимлидов: {'✅ Работает' if len(all_teamleads) > 0 else '⚠️ Нет тимлидов'}")
    print(f"• Назначение номеров: {'✅ Работает' if teamleads_manager.get_teamleads_for_phone(test_phone) else '⚠️ Не назначено'}")
    print(f"• Алерты времени ответа: {'✅ Работают' if call_count > 0 else '❌ Не работают'}")
    print(f"• Фото алерты: {'✅ Работают' if photo_call_count > 0 else '❌ Не работают'}")
    print(f"• Fallback на админов: {'✅ Работает' if fallback_call_count > 0 else '❌ Не работает'}")

if __name__ == "__main__":
    asyncio.run(test_teamlead_alert_system())

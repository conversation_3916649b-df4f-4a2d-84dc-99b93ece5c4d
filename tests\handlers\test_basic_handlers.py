"""
Тесты обработчиков basic.py

Покрывает команды /start, /help и обработку неизвестных команд
с мокированием aiogram Message объектов.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from aiogram import Dispatcher
from aiogram.types import Message, User, Chat
from aiogram.filters import Command

from src.bot.handlers.basic import register_basic_handlers
from src.config.environment import env_config
from tests.fixtures.test_constants import TEST_ADMIN_USER_ID, TEST_REGULAR_USER_ID
from tests.utils.mock_factories import MockFactory, create_admin_user, create_regular_user
from tests.utils.test_helpers import assert_called_with_text, create_mock_message


class TestBasicHandlersRegistration:
    """Тесты регистрации обработчиков"""
    
    def test_register_basic_handlers(self):
        """Тест регистрации основных обработчиков"""
        mock_dp = Mock(spec=Dispatcher)
        
        # Мокируем декораторы
        mock_dp.message = Mock(return_value=lambda func: func)
        
        register_basic_handlers(mock_dp)
        
        # Проверяем что декораторы были вызваны
        assert mock_dp.message.call_count >= 2  # Минимум для /start и /help


class TestStartCommand:
    """Тесты команды /start"""
    
    @pytest.mark.asyncio
    async def test_start_command_regular_user(self):
        """Тест команды /start для обычного пользователя"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "/start")
        
        with patch.object(env_config, 'is_admin', return_value=False):
            with patch('src.bot.handlers.basic.get_keyboard') as mock_get_keyboard:
                mock_keyboard = Mock()
                mock_get_keyboard.return_value = mock_keyboard
                
                # Импортируем и вызываем обработчик
                from src.bot.handlers.basic import register_basic_handlers
                
                # Создаем временный диспетчер для получения обработчика
                temp_dp = Mock()
                handlers = []
                
                def mock_message_decorator(filter_obj):
                    def decorator(handler):
                        handlers.append((filter_obj, handler))
                        return handler
                    return decorator
                
                temp_dp.message = mock_message_decorator
                register_basic_handlers(temp_dp)
                
                # Находим обработчик /start
                start_handler = None
                for filter_obj, handler in handlers:
                    if hasattr(filter_obj, 'commands') or str(filter_obj).find('start') != -1:
                        start_handler = handler
                        break
                
                if start_handler:
                    await start_handler(message)
                
                # Проверяем что ответ был отправлен
                message.answer.assert_called_once()
                call_args = message.answer.call_args
                response_text = call_args[0][0]
                
                # Проверяем содержимое ответа
                assert "Добро пожаловать" in response_text
                assert "Подключить аккаунт" in response_text
                assert "Мои аккаунты" in response_text
                assert "Управление стоп-словами" in response_text
                
                # Для обычного пользователя не должно быть админских функций
                assert "Администраторские функции" not in response_text
                
                # Проверяем что клавиатура была запрошена
                mock_get_keyboard.assert_called_once_with(user_data.user_id)
    
    @pytest.mark.asyncio
    async def test_start_command_admin_user(self):
        """Тест команды /start для администратора"""
        user_data = create_admin_user()
        message = MockFactory.create_message(user_data, "/start")
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch('src.bot.handlers.basic.get_keyboard') as mock_get_keyboard:
                mock_keyboard = Mock()
                mock_get_keyboard.return_value = mock_keyboard
                
                # Создаем обработчик напрямую для тестирования
                async def mock_start_handler(message: Message):
                    user_id = message.from_user.id
                    welcome_text = (
                        "🔍 Добро пожаловать в систему мониторинга сообщений!\n\n"
                        "Доступные функции:\n"
                        "• Подключить аккаунт - добавить Telegram аккаунт для мониторинга\n"
                        "• Мои аккаунты - просмотр подключенных аккаунтов\n"
                        "• Управление стоп-словами - настройка списка запрещенных слов"
                    )
                    
                    if env_config.is_admin(user_id):
                        welcome_text += "\n\n👑 Администраторские функции:\n• Все подключенные аккаунты\n• Статистика"
                    
                    from src.bot.keyboards import get_keyboard
                    await message.answer(welcome_text, reply_markup=get_keyboard(user_id))
                
                await mock_start_handler(message)
                
                # Проверяем что ответ был отправлен
                message.answer.assert_called_once()
                call_args = message.answer.call_args
                response_text = call_args[0][0]
                
                # Проверяем содержимое ответа для админа
                assert "Добро пожаловать" in response_text
                assert "Администраторские функции" in response_text
                assert "Все подключенные аккаунты" in response_text
                assert "Статистика" in response_text
    
    @pytest.mark.asyncio
    async def test_start_command_keyboard_selection(self):
        """Тест выбора клавиатуры в команде /start"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "/start")
        
        with patch('src.bot.handlers.basic.get_keyboard') as mock_get_keyboard:
            with patch.object(env_config, 'is_admin', return_value=False):
                mock_keyboard = Mock()
                mock_get_keyboard.return_value = mock_keyboard
                
                # Создаем простой обработчик для тестирования
                async def mock_start_handler(message: Message):
                    from src.bot.keyboards import get_keyboard
                    await message.answer("Test", reply_markup=get_keyboard(message.from_user.id))
                
                await mock_start_handler(message)
                
                # Проверяем что get_keyboard был вызван с правильным user_id
                mock_get_keyboard.assert_called_once_with(user_data.user_id)
                
                # Проверяем что клавиатура была передана в ответ
                call_kwargs = message.answer.call_args[1]
                assert 'reply_markup' in call_kwargs
                assert call_kwargs['reply_markup'] == mock_keyboard
    
    @pytest.mark.asyncio
    async def test_start_command_different_user_ids(self):
        """Тест команды /start с различными user_id"""
        test_user_ids = [TEST_REGULAR_USER_ID, TEST_ADMIN_USER_ID, 999999999, 1]
        
        for user_id in test_user_ids:
            user_data = create_regular_user()
            user_data.user_id = user_id
            message = MockFactory.create_message(user_data, "/start")
            
            with patch('src.bot.handlers.basic.get_keyboard') as mock_get_keyboard:
                with patch.object(env_config, 'is_admin', return_value=False):
                    mock_keyboard = Mock()
                    mock_get_keyboard.return_value = mock_keyboard
                    
                    # Простой обработчик
                    async def mock_start_handler(message: Message):
                        from src.bot.keyboards import get_keyboard
                        await message.answer("Test", reply_markup=get_keyboard(message.from_user.id))
                    
                    await mock_start_handler(message)
                    
                    # Проверяем что функция была вызвана с правильным ID
                    mock_get_keyboard.assert_called_with(user_id)


class TestHelpCommand:
    """Тесты команды /help"""
    
    @pytest.mark.asyncio
    async def test_help_command_regular_user(self):
        """Тест команды /help для обычного пользователя"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "/help")
        
        with patch.object(env_config, 'is_admin', return_value=False):
            # Создаем обработчик напрямую
            async def mock_help_handler(message: Message):
                user_id = message.from_user.id
                
                help_text = (
                    "🔍 Система мониторинга сообщений\n\n"
                    "📱 Основные функции:\n"
                    "• Подключить аккаунт - добавить Telegram аккаунт\n"
                    "• Мои аккаунты - просмотр ваших аккаунтов\n"
                    "• Управление стоп-словами - настройка фильтров\n\n"
                    "🤖 Команды:\n"
                    "/start - главное меню\n"
                    "/help - эта справка\n"
                )
                
                if env_config.is_admin(user_id):
                    help_text += (
                        "\n👑 Администраторские команды:\n"
                        "/reload_stopwords - перезагрузить стоп-слова\n"
                        "• Все подключенные аккаунты\n"
                        "• Статистика\n"
                    )
                
                await message.answer(help_text)
            
            await mock_help_handler(message)
            
            # Проверяем что ответ был отправлен
            message.answer.assert_called_once()
            call_args = message.answer.call_args
            response_text = call_args[0][0]
            
            # Проверяем содержимое справки
            assert "Система мониторинга сообщений" in response_text
            assert "Основные функции" in response_text
            assert "Подключить аккаунт" in response_text
            assert "/start" in response_text
            assert "/help" in response_text
            
            # Для обычного пользователя не должно быть админских команд
            assert "Администраторские команды" not in response_text
            assert "/reload_stopwords" not in response_text
    
    @pytest.mark.asyncio
    async def test_help_command_admin_user(self):
        """Тест команды /help для администратора"""
        user_data = create_admin_user()
        message = MockFactory.create_message(user_data, "/help")
        
        with patch.object(env_config, 'is_admin', return_value=True):
            # Создаем обработчик напрямую
            async def mock_help_handler(message: Message):
                user_id = message.from_user.id
                
                help_text = (
                    "🔍 Система мониторинга сообщений\n\n"
                    "📱 Основные функции:\n"
                    "• Подключить аккаунт - добавить Telegram аккаунт\n"
                    "• Мои аккаунты - просмотр ваших аккаунтов\n"
                    "• Управление стоп-словами - настройка фильтров\n\n"
                    "🤖 Команды:\n"
                    "/start - главное меню\n"
                    "/help - эта справка\n"
                )
                
                if env_config.is_admin(user_id):
                    help_text += (
                        "\n👑 Администраторские команды:\n"
                        "/reload_stopwords - перезагрузить стоп-слова\n"
                        "• Все подключенные аккаунты\n"
                        "• Статистика\n"
                    )
                
                await message.answer(help_text)
            
            await mock_help_handler(message)
            
            # Проверяем что ответ был отправлен
            message.answer.assert_called_once()
            call_args = message.answer.call_args
            response_text = call_args[0][0]
            
            # Проверяем содержимое справки для админа
            assert "Система мониторинга сообщений" in response_text
            assert "Администраторские команды" in response_text
            assert "/reload_stopwords" in response_text
            assert "Все подключенные аккаунты" in response_text
            assert "Статистика" in response_text
    
    @pytest.mark.asyncio
    async def test_help_command_no_reply_markup(self):
        """Тест что команда /help не отправляет клавиатуру"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "/help")
        
        with patch.object(env_config, 'is_admin', return_value=False):
            # Простой обработчик
            async def mock_help_handler(message: Message):
                await message.answer("Help text")
            
            await mock_help_handler(message)
            
            # Проверяем что клавиатура не была передана
            call_kwargs = message.answer.call_args[1] if message.answer.call_args[1] else {}
            assert 'reply_markup' not in call_kwargs or call_kwargs.get('reply_markup') is None


class TestBasicHandlersEdgeCases:
    """Тесты граничных случаев для базовых обработчиков"""
    
    @pytest.mark.asyncio
    async def test_handlers_with_none_user(self):
        """Тест обработчиков с None пользователем"""
        message = Mock(spec=Message)
        message.from_user = None
        message.answer = AsyncMock()
        
        # Обработчики должны корректно обрабатывать отсутствие пользователя
        try:
            async def mock_start_handler(message: Message):
                if message.from_user:
                    user_id = message.from_user.id
                    await message.answer(f"User ID: {user_id}")
                else:
                    await message.answer("No user")
            
            await mock_start_handler(message)
            message.answer.assert_called_once_with("No user")
        except Exception as e:
            pytest.fail(f"Handler should handle None user gracefully: {e}")
    
    @pytest.mark.asyncio
    async def test_handlers_with_invalid_user_id(self):
        """Тест обработчиков с невалидным user_id"""
        invalid_user_ids = [0, -1, None]
        
        for invalid_id in invalid_user_ids:
            user_data = create_regular_user()
            user_data.user_id = invalid_id
            message = MockFactory.create_message(user_data, "/start")
            
            with patch.object(env_config, 'is_admin', return_value=False):
                with patch('src.bot.handlers.basic.get_keyboard') as mock_get_keyboard:
                    mock_keyboard = Mock()
                    mock_get_keyboard.return_value = mock_keyboard
                    
                    try:
                        async def mock_start_handler(message: Message):
                            user_id = message.from_user.id if message.from_user else 0
                            from src.bot.keyboards import get_keyboard
                            await message.answer("Test", reply_markup=get_keyboard(user_id))
                        
                        await mock_start_handler(message)
                        
                        # Проверяем что обработчик не упал
                        message.answer.assert_called_once()
                        mock_get_keyboard.assert_called_once_with(invalid_id)
                    except Exception as e:
                        # Некоторые невалидные ID могут вызывать исключения
                        if invalid_id is None:
                            # None ID может вызвать TypeError
                            assert isinstance(e, (TypeError, AttributeError))
                        else:
                            pytest.fail(f"Handler should handle invalid user_id {invalid_id}: {e}")
    
    @pytest.mark.asyncio
    async def test_handlers_exception_handling(self):
        """Тест обработки исключений в обработчиках"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "/start")
        
        with patch.object(env_config, 'is_admin', side_effect=Exception("Config error")):
            # Обработчик должен корректно обрабатывать исключения
            try:
                async def mock_start_handler(message: Message):
                    try:
                        user_id = message.from_user.id
                        is_admin = env_config.is_admin(user_id)
                        await message.answer(f"Admin: {is_admin}")
                    except Exception:
                        await message.answer("Error occurred")
                
                await mock_start_handler(message)
                
                # Проверяем что обработчик отправил сообщение об ошибке
                message.answer.assert_called_once_with("Error occurred")
            except Exception as e:
                pytest.fail(f"Handler should handle exceptions gracefully: {e}")
    
    @pytest.mark.asyncio
    async def test_message_answer_failure(self):
        """Тест обработки ошибки отправки сообщения"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "/start")
        
        # Мокируем ошибку отправки сообщения
        message.answer.side_effect = Exception("Send message failed")
        
        with patch.object(env_config, 'is_admin', return_value=False):
            try:
                async def mock_start_handler(message: Message):
                    try:
                        await message.answer("Test message")
                    except Exception:
                        # Логируем ошибку, но не падаем
                        pass
                
                await mock_start_handler(message)
                
                # Проверяем что попытка отправки была сделана
                message.answer.assert_called_once()
            except Exception as e:
                pytest.fail(f"Handler should handle send message errors: {e}")
    
    @pytest.mark.asyncio
    async def test_concurrent_handler_calls(self):
        """Тест одновременных вызовов обработчиков"""
        import asyncio
        
        # Создаем несколько сообщений
        messages = []
        for i in range(5):
            user_data = create_regular_user()
            user_data.user_id = TEST_REGULAR_USER_ID + i
            message = MockFactory.create_message(user_data, "/start")
            messages.append(message)
        
        with patch.object(env_config, 'is_admin', return_value=False):
            with patch('src.bot.handlers.basic.get_keyboard', return_value=Mock()):
                async def mock_start_handler(message: Message):
                    # Симулируем небольшую задержку
                    await asyncio.sleep(0.01)
                    await message.answer("Response")
                
                # Запускаем обработчики одновременно
                tasks = [mock_start_handler(msg) for msg in messages]
                await asyncio.gather(*tasks)
                
                # Проверяем что все обработчики отработали
                for message in messages:
                    message.answer.assert_called_once_with("Response")

#!/usr/bin/env python3
"""
Скрипт для создания метаданных для существующих файлов сессий
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.settings import ACCOUNTS_METADATA_FILE, SESSIONS_DIR

def create_metadata_for_sessions():
    """Создание метаданных для существующих файлов сессий"""
    print("🔍 Создание метаданных для существующих сессий...")
    print("=" * 50)
    
    # Проверяем существование директории сессий
    if not SESSIONS_DIR.exists():
        print("❌ Директория сессий не найдена")
        return
    
    # Получаем список файлов сессий
    session_files = list(SESSIONS_DIR.glob('*.session'))
    print(f"📁 Найдено {len(session_files)} файлов сессий")
    
    if not session_files:
        print("❌ Файлы сессий не найдены")
        return
    
    # Создаем метаданные для каждого файла сессии
    metadata = {}
    
    for session_file in session_files:
        try:
            # Извлекаем user_id из имени файла
            stem = session_file.stem
            if stem.startswith('account_'):
                user_id = stem.replace('account_', '')
            else:
                user_id = stem
            
            print(f"\n📄 Обработка файла: {session_file.name}")
            print(f"🆔 Извлеченный user_id: {user_id}")
            
            # Пытаемся определить номер телефона
            # Если user_id начинается с 380, это украинский номер
            if user_id.startswith('380'):
                phone = f"+{user_id}"
            else:
                # Для других случаев используем user_id как есть
                phone = f"+{user_id}"
            
            print(f"📞 Предполагаемый номер: {phone}")
            
            # Создаем запись метаданных
            metadata[phone] = {
                'session_file': session_file.name,
                'monitoring': False,  # По умолчанию отключаем мониторинг
                'last_updated': datetime.now().isoformat(),
                'needs_reauth': True  # Помечаем что нужна повторная авторизация
            }
            
            print(f"✅ Создана запись метаданных для {phone}")
            
        except Exception as e:
            print(f"❌ Ошибка обработки файла {session_file.name}: {e}")
    
    # Сохраняем метаданные
    if metadata:
        # Создаем backup существующего файла
        if ACCOUNTS_METADATA_FILE.exists():
            backup_path = ACCOUNTS_METADATA_FILE.with_suffix('.json.backup_before_manual_create')
            ACCOUNTS_METADATA_FILE.rename(backup_path)
            print(f"\n📦 Создан backup: {backup_path}")
        
        # Сохраняем новые метаданные
        with open(ACCOUNTS_METADATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Создано {len(metadata)} записей метаданных")
        print(f"💾 Метаданные сохранены в {ACCOUNTS_METADATA_FILE}")
        
        # Показываем созданные записи
        print("\n📋 Созданные записи:")
        for phone, data in metadata.items():
            print(f"   - {phone}: {data['session_file']} (требует авторизации)")
        
        print("\n💡 Рекомендации:")
        print("1. Запустите бота и попробуйте подключить аккаунты заново")
        print("2. Если сессии устарели, удалите их и создайте новые")
        print("3. Проверьте, что API_ID и API_HASH корректны в .env файле")
        
    else:
        print("\n❌ Не удалось создать ни одной записи метаданных")

if __name__ == "__main__":
    create_metadata_for_sessions()

"""
Основные fixtures для тестирования PM Searcher Bot
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, AsyncMock, MagicMock, patch
from typing import Dict, Any, List

# Импорты для мокирования aiogram
from aiogram import Bot, Dispatcher
from aiogram.types import Message, CallbackQuery, User, Chat, Update
from aiogram.fsm.context import FSMContext
from aiogram.fsm.storage.memory import MemoryStorage

# Импорты для мокирования telethon
from telethon import TelegramClient
from telethon.events import NewMessage

# Импорты проекта
from src.utils.stopwords import StopWordsManager
from src.monitoring.telethon_manager import TelethonManager
from src.bot.alerts import AlertManager
from src.config.environment import EnvironmentConfig


# =======================================
# БАЗОВЫЕ FIXTURES
# =======================================

@pytest.fixture(scope="session")
def event_loop():
    """Создание event loop для всей сессии тестов"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Временная директория для тестов"""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def temp_stopwords_file(temp_dir):
    """Временный файл стоп-слов для тестов"""
    stopwords_file = temp_dir / "test_stopwords.txt"
    
    # Создаем файл с тестовыми данными
    test_content = """# Тестовые стоп-слова
# Комментарии игнорируются

спам
реклама
продажа
купить
тест
"""
    stopwords_file.write_text(test_content, encoding='utf-8')
    return stopwords_file


# =======================================
# FIXTURES ДЛЯ МОКИРОВАНИЯ AIOGRAM
# =======================================

@pytest.fixture
def mock_bot():
    """Мок Telegram бота"""
    bot = Mock(spec=Bot)
    bot.send_message = AsyncMock()
    bot.edit_message_text = AsyncMock()
    bot.edit_message_reply_markup = AsyncMock()
    bot.answer_callback_query = AsyncMock()
    bot.session = Mock()
    bot.session.close = AsyncMock()
    return bot


@pytest.fixture
def mock_dispatcher():
    """Мок Dispatcher"""
    dp = Mock(spec=Dispatcher)
    dp.storage = MemoryStorage()
    dp.start_polling = AsyncMock()
    return dp


@pytest.fixture
def mock_user():
    """Мок пользователя Telegram"""
    return User(
        id=123456789,
        is_bot=False,
        first_name="Test",
        last_name="User",
        username="testuser",
        language_code="ru"
    )


@pytest.fixture
def mock_admin_user():
    """Мок администратора"""
    return User(
        id=*********,
        is_bot=False,
        first_name="Admin",
        last_name="User",
        username="adminuser",
        language_code="ru"
    )


@pytest.fixture
def mock_chat():
    """Мок чата"""
    return Chat(
        id=-123456789,
        type="private"
    )


@pytest.fixture
def mock_message(mock_user, mock_chat):
    """Мок сообщения пользователя"""
    message = Mock(spec=Message)
    message.from_user = mock_user
    message.chat = mock_chat
    message.message_id = 1
    message.text = "Test message"
    message.answer = AsyncMock()
    message.reply = AsyncMock()
    message.edit_text = AsyncMock()
    return message


@pytest.fixture
def mock_admin_message(mock_admin_user, mock_chat):
    """Мок сообщения администратора"""
    message = Mock(spec=Message)
    message.from_user = mock_admin_user
    message.chat = mock_chat
    message.message_id = 2
    message.text = "Admin message"
    message.answer = AsyncMock()
    message.reply = AsyncMock()
    message.edit_text = AsyncMock()
    return message


@pytest.fixture
def mock_callback_query(mock_user, mock_message):
    """Мок callback query"""
    callback = Mock(spec=CallbackQuery)
    callback.from_user = mock_user
    callback.message = mock_message
    callback.data = "test_callback"
    callback.answer = AsyncMock()
    callback.edit_message_text = AsyncMock()
    callback.edit_message_reply_markup = AsyncMock()
    return callback


@pytest.fixture
def mock_fsm_context():
    """Мок FSM контекста"""
    context = Mock(spec=FSMContext)
    context.set_state = AsyncMock()
    context.get_state = AsyncMock(return_value=None)
    context.clear = AsyncMock()
    context.set_data = AsyncMock()
    context.get_data = AsyncMock(return_value={})
    context.update_data = AsyncMock()
    return context


# =======================================
# FIXTURES ДЛЯ МОКИРОВАНИЯ TELETHON
# =======================================

@pytest.fixture
def mock_telegram_client():
    """Мок Telethon TelegramClient"""
    client = Mock(spec=TelegramClient)
    client.connect = AsyncMock()
    client.disconnect = AsyncMock()
    client.is_user_authorized = AsyncMock(return_value=True)
    client.send_code_request = AsyncMock()
    client.sign_in = AsyncMock()
    client.add_event_handler = Mock()
    client.remove_event_handler = Mock()
    client.run_until_disconnected = AsyncMock()
    return client


@pytest.fixture
def mock_new_message_event():
    """Мок события нового сообщения Telethon"""
    event = Mock(spec=NewMessage.Event)
    event.message = Mock()
    event.message.text = "Test outgoing message"
    event.message.out = True  # Исходящее сообщение
    return event


# =======================================
# FIXTURES ДЛЯ КОМПОНЕНТОВ ПРОЕКТА
# =======================================

@pytest.fixture
def stopwords_manager(temp_stopwords_file):
    """Экземпляр StopWordsManager для тестов"""
    with patch('src.utils.stopwords.STOPWORDS_FILE', temp_stopwords_file):
        manager = StopWordsManager()
        return manager


@pytest.fixture
def mock_telethon_manager():
    """Мок TelethonManager"""
    manager = Mock(spec=TelethonManager)
    manager.clients = {}
    manager.stopwords_manager = Mock()
    manager.start_auth = AsyncMock(return_value=True)
    manager.complete_auth = AsyncMock(return_value=True)
    manager.start_monitoring = AsyncMock()
    manager.disconnect_client = AsyncMock()
    manager.get_connected_accounts = Mock(return_value=[])
    manager.reload_stopwords = Mock(return_value=5)
    return manager


@pytest.fixture
def alert_manager(mock_bot):
    """Экземпляр AlertManager для тестов"""
    return AlertManager(mock_bot)


@pytest.fixture
def mock_environment_config():
    """Мок конфигурации окружения"""
    config = Mock(spec=EnvironmentConfig)
    config.BOT_TOKEN = "123456:TEST_BOT_TOKEN"
    config.API_ID = 12345
    config.API_HASH = "test_api_hash"
    config.ADMIN_CHAT_IDS = [*********]
    config.is_admin = Mock(return_value=False)
    config.validate = Mock(return_value=(True, []))
    return config


# =======================================
# FIXTURES ДЛЯ ТЕСТОВЫХ ДАННЫХ
# =======================================

@pytest.fixture
def valid_stopwords():
    """Валидные стоп-слова для тестов"""
    return [
        "спам", "реклама", "продажа", "купить",
        "test_word", "тест", "проверка",
        "a", "очень_длинное_стоп_слово_для_тестирования"
    ]


@pytest.fixture
def invalid_stopwords():
    """Невалидные стоп-слова для тестов"""
    return [
        "", "   ", "\n\t", "слово с пробелами",
        "слово\nс\nпереносами", "слово/с/слешами",
        "слово<с>тегами", "слово\"с\"кавычками",
        "слово;с;точкой;запятой", "слово|с|пайпом",
        "слово\\с\\бэкслешами", "слово:с:двоеточием"
    ]


@pytest.fixture
def malicious_inputs():
    """Вредоносные входные данные для тестов безопасности"""
    return [
        # SQL injection
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "admin'--",
        
        # XSS
        "<script>alert('xss')</script>",
        "<img src=x onerror=alert('xss')>",
        "javascript:alert('xss')",
        
        # Path traversal
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32\\config\\sam",
        "....//....//....//etc//passwd",
        
        # Command injection
        "; rm -rf /",
        "| cat /etc/passwd",
        "$(whoami)",
        "`id`"
    ]


@pytest.fixture
def admin_user_id():
    """ID администратора для тестов"""
    return *********


@pytest.fixture
def regular_user_id():
    """ID обычного пользователя для тестов"""
    return 123456789


# =======================================
# FIXTURES ДЛЯ ФАЙЛОВЫХ ОПЕРАЦИЙ
# =======================================

@pytest.fixture
def valid_import_files():
    """Валидные файлы для импорта"""
    return {
        "simple": "# Комментарий\nспам\nреклама\n",
        "with_comments": "# Только комментарии\n# Еще комментарий\n",
        "mixed": "слово1\nслово2\n# комментарий\nслово3\n",
        "empty_lines": "слово1\n\n\nслово2\n\n"
    }


@pytest.fixture
def invalid_import_files():
    """Невалидные файлы для импорта"""
    return {
        "empty": "",
        "only_spaces": "   \n\t\n   ",
        "invalid_chars": "слово с пробелами\nнормальное_слово\n",
        "too_large": "слово\n" * 10000,  # Слишком много слов
        "binary_data": b"\x00\x01\x02\x03\x04\x05"
    }

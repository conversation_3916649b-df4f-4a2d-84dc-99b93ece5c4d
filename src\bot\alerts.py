"""
Модуль для управления алертами
"""

from typing import List, Optional, Union
from aiogram import Bot
from aiogram.types import InputFile, BufferedInputFile
from src.config.environment import env_config
from src.config.teamleads_manager import teamleads_manager
from src.utils.logging import setup_logging

logger = setup_logging(__name__, 'alerts.log')

class AlertManager:
    """Менеджер алертов"""
    
    def __init__(self, bot: Bo<PERSON>):
        self.bot = bot
    
    async def send_alert_to_user(self, user_id: int, alert_text: str):
        """Отправка алерта пользователю"""
        try:
            await self.bot.send_message(user_id, alert_text)
            logger.info(f"Алерт успешно отправлен пользователю {user_id}")
        except Exception as e:
            logger.error(f"Ошибка отправки алерта пользователю {user_id}: {e}")
            # Не поднимаем исключение, чтобы не прерывать отправку другим получателям
    
    async def send_alert_to_admins(self, alert_text: str):
        """Отправка алерта всем администраторам"""
        successful_sends = 0
        failed_sends = 0

        for admin_id in env_config.ADMIN_CHAT_IDS:
            if admin_id <= 0:  # Пропускаем невалидные ID
                logger.warning(f"Пропущен невалидный ID администратора: {admin_id}")
                continue

            try:
                await self.bot.send_message(admin_id, alert_text)
                successful_sends += 1
                logger.info(f"Алерт успешно отправлен администратору {admin_id}")
            except Exception as e:
                failed_sends += 1
                logger.error(f"Ошибка отправки алерта администратору {admin_id}: {e}")

        # Итоговое логирование
        total_admins = len([aid for aid in env_config.ADMIN_CHAT_IDS if aid > 0])
        logger.info(f"Отправка алертов завершена: {successful_sends}/{total_admins} успешно, {failed_sends} ошибок")
    
    async def send_alert_to_all(self, user_id: int, alert_text: str):
        """Отправка алерта пользователю и всем администраторам"""
        # Отправка пользователю
        await self.send_alert_to_user(user_id, alert_text)

        # Отправка всем администраторам (включая того, кто подключил аккаунт)
        await self.send_alert_to_admins(alert_text)
    
    async def send_admin_notification(self, notification_text: str):
        """Отправка уведомления только администраторам"""
        await self.send_alert_to_admins(notification_text)

    async def send_alert_to_teamleads_for_phone(self, phone: str, alert_text: str):
        """
        Отправка алерта тимлидам, назначенным на конкретный номер телефона

        Args:
            phone (str): Номер телефона
            alert_text (str): Текст алерта
        """
        # Получаем тимлидов для этого номера
        assigned_teamleads = teamleads_manager.get_teamleads_for_phone(phone)

        if not assigned_teamleads:
            # Если нет назначенных тимлидов, отправляем всем админам (fallback)
            logger.info(f"Нет назначенных тимлидов для номера {phone}, отправляем всем админам")
            await self.send_alert_to_admins(alert_text)
            return

        successful_sends = 0
        failed_sends = 0

        for teamlead in assigned_teamleads:
            user_id = teamlead.get('user_id')
            username = teamlead.get('username', 'Неизвестно')

            if not user_id or user_id <= 0:
                logger.warning(f"Пропущен тимлид {username} с невалидным ID: {user_id}")
                continue

            try:
                await self.bot.send_message(user_id, alert_text)
                successful_sends += 1
                logger.info(f"Алерт успешно отправлен тимлиду {username} (ID: {user_id}) для номера {phone}")
            except Exception as e:
                failed_sends += 1
                logger.error(f"Ошибка отправки алерта тимлиду {username} (ID: {user_id}): {e}")

        # Итоговое логирование
        logger.info(f"Отправка алертов для номера {phone} завершена: {successful_sends}/{len(assigned_teamleads)} успешно, {failed_sends} ошибок")

    async def send_response_time_alert(self, phone: str, alert_text: str):
        """
        Отправка алерта о просрочке времени ответа всем администраторам

        Args:
            phone (str): Номер телефона аккаунта
            alert_text (str): Текст алерта
        """
        try:
            # Алерты о просрочке отправляем ВСЕМ АДМИНИСТРАТОРАМ
            logger.info(f"Отправка алерта о просрочке для номера {phone} всем администраторам")
            await self.send_alert_to_admins(alert_text)

        except Exception as e:
            logger.error(f"Ошибка отправки алерта о времени ответа для номера {phone}: {e}")
            # Fallback: отправляем всем администраторам
            try:
                await self.send_alert_to_admins(alert_text)
            except Exception as fallback_error:
                logger.error(f"Fallback отправка алерта также не удалась: {fallback_error}")

    async def send_response_time_notification_to_admins(self, notification_text: str):
        """
        Отправка уведомления о настройках времени ответа только администраторам

        Args:
            notification_text (str): Текст уведомления
        """
        await self.send_alert_to_admins(notification_text)

    async def send_alert_with_phone_routing(self, phone: str, alert_text: str):
        """
        Отправка алерта с маршрутизацией по номеру телефона

        Если для номера назначены тимлиды - отправляет им.
        Если нет - отправляет всем администраторам.

        Args:
            phone (str): Номер телефона
            alert_text (str): Текст алерта
        """
        await self.send_alert_to_teamleads_for_phone(phone, alert_text)

    async def send_alert_with_stats(self, user_id: int, alert_text: str) -> dict:
        """
        Отправка алерта с возвращением детальной статистики

        Returns:
            dict: Статистика отправки {
                'user_sent': bool,
                'admins_sent': int,
                'admins_failed': int,
                'total_admins': int
            }
        """
        stats = {
            'user_sent': False,
            'admins_sent': 0,
            'admins_failed': 0,
            'total_admins': 0
        }

        # Отправка пользователю
        try:
            await self.bot.send_message(user_id, alert_text)
            stats['user_sent'] = True
            logger.info(f"Алерт успешно отправлен пользователю {user_id}")
        except Exception as e:
            logger.error(f"Ошибка отправки алерта пользователю {user_id}: {e}")

        # Отправка всем администраторам (включая того, кто подключил аккаунт)
        valid_admins = [aid for aid in env_config.ADMIN_CHAT_IDS if aid > 0]
        stats['total_admins'] = len(valid_admins)

        for admin_id in valid_admins:
            try:
                await self.bot.send_message(admin_id, alert_text)
                stats['admins_sent'] += 1
                logger.info(f"Алерт успешно отправлен администратору {admin_id}")
            except Exception as e:
                stats['admins_failed'] += 1
                logger.error(f"Ошибка отправки алерта администратору {admin_id}: {e}")

        return stats

    async def send_photo_alert_to_user(self, user_id: int, photo_data: bytes, caption: str):
        """Отправка фото алерта пользователю"""
        try:
            photo_file = BufferedInputFile(photo_data, filename="photo_alert.jpg")
            await self.bot.send_photo(user_id, photo_file, caption=caption)
            logger.info(f"Фото алерт успешно отправлен пользователю {user_id}")
        except Exception as e:
            logger.error(f"Ошибка отправки фото алерта пользователю {user_id}: {e}")

    async def send_photo_alert_to_admins(self, photo_data: bytes, caption: str):
        """Отправка фото алерта всем администраторам"""
        successful_sends = 0
        failed_sends = 0

        for admin_id in env_config.ADMIN_CHAT_IDS:
            if admin_id <= 0:  # Пропускаем невалидные ID
                logger.warning(f"Пропущен невалидный ID администратора: {admin_id}")
                continue

            try:
                photo_file = BufferedInputFile(photo_data, filename="photo_alert.jpg")
                await self.bot.send_photo(admin_id, photo_file, caption=caption)
                successful_sends += 1
                logger.info(f"Фото алерт успешно отправлен администратору {admin_id}")
            except Exception as e:
                failed_sends += 1
                logger.error(f"Ошибка отправки фото алерта администратору {admin_id}: {e}")

        # Итоговое логирование
        total_admins = len([aid for aid in env_config.ADMIN_CHAT_IDS if aid > 0])
        logger.info(f"Отправка фото алертов завершена: {successful_sends}/{total_admins} успешно, {failed_sends} ошибок")

    async def send_photo_alert_to_all(self, user_id: int, photo_data: bytes, caption: str):
        """Отправка фото алерта пользователю и всем администраторам"""
        # Отправка пользователю
        await self.send_photo_alert_to_user(user_id, photo_data, caption)

        # Отправка всем администраторам (включая того, кто подключил аккаунт)
        await self.send_photo_alert_to_admins(photo_data, caption)

    async def send_photo_alert_to_teamleads_for_phone(self, phone: str, photo_data: bytes, caption: str):
        """
        Отправка фото алерта тимлидам, назначенным на конкретный номер телефона

        Args:
            phone (str): Номер телефона
            photo_data (bytes): Данные фотографии
            caption (str): Подпись к фото
        """
        # Получаем тимлидов для этого номера
        assigned_teamleads = teamleads_manager.get_teamleads_for_phone(phone)

        if not assigned_teamleads:
            # Если нет назначенных тимлидов, отправляем всем админам (fallback)
            logger.info(f"Нет назначенных тимлидов для номера {phone}, отправляем фото алерт всем админам")
            await self.send_photo_alert_to_admins(photo_data, caption)
            return

        successful_sends = 0
        failed_sends = 0

        for teamlead in assigned_teamleads:
            user_id = teamlead.get('user_id')
            username = teamlead.get('username', 'Неизвестно')

            if not user_id or user_id <= 0:
                logger.warning(f"Пропущен тимлид {username} с невалидным ID: {user_id}")
                continue

            try:
                photo_file = BufferedInputFile(photo_data, filename="photo_alert.jpg")
                await self.bot.send_photo(user_id, photo_file, caption=caption)
                successful_sends += 1
                logger.info(f"Фото алерт успешно отправлен тимлиду {username} (ID: {user_id}) для номера {phone}")
            except Exception as e:
                failed_sends += 1
                logger.error(f"Ошибка отправки фото алерта тимлиду {username} (ID: {user_id}): {e}")

        # Итоговое логирование
        logger.info(f"Отправка фото алертов для номера {phone} завершена: {successful_sends}/{len(assigned_teamleads)} успешно, {failed_sends} ошибок")

    async def send_photo_alert_with_phone_routing(self, phone: str, photo_data: bytes, caption: str):
        """
        Отправка фото алерта с маршрутизацией по номеру телефона

        Если для номера назначены тимлиды - отправляет им.
        Если нет - отправляет всем администраторам.

        Args:
            phone (str): Номер телефона
            photo_data (bytes): Данные фотографии
            caption (str): Подпись к фото
        """
        await self.send_photo_alert_to_teamleads_for_phone(phone, photo_data, caption)

    async def send_photo_alert_with_stats(self, user_id: int, photo_data: bytes, caption: str) -> dict:
        """
        Отправка фото алерта с возвращением детальной статистики

        Returns:
            dict: Статистика отправки {
                'user_sent': bool,
                'admins_sent': int,
                'admins_failed': int,
                'total_admins': int
            }
        """
        stats = {
            'user_sent': False,
            'admins_sent': 0,
            'admins_failed': 0,
            'total_admins': 0
        }

        # Отправка пользователю
        try:
            photo_file = BufferedInputFile(photo_data, filename="photo_alert.jpg")
            await self.bot.send_photo(user_id, photo_file, caption=caption)
            stats['user_sent'] = True
            logger.info(f"Фото алерт успешно отправлен пользователю {user_id}")
        except Exception as e:
            logger.error(f"Ошибка отправки фото алерта пользователю {user_id}: {e}")

        # Отправка всем администраторам (включая того, кто подключил аккаунт)
        valid_admins = [aid for aid in env_config.ADMIN_CHAT_IDS if aid > 0]
        stats['total_admins'] = len(valid_admins)

        for admin_id in valid_admins:
            try:
                photo_file = BufferedInputFile(photo_data, filename="photo_alert.jpg")
                await self.bot.send_photo(admin_id, photo_file, caption=caption)
                stats['admins_sent'] += 1
                logger.info(f"Фото алерт успешно отправлен администратору {admin_id}")
            except Exception as e:
                stats['admins_failed'] += 1
                logger.error(f"Ошибка отправки фото алерта администратору {admin_id}: {e}")

        return stats

"""
Обработчики для управления настройками времени ответа
"""

import logging
from datetime import datetime
from aiogram import Dispatcher, types
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command

from ..states import ResponseTimeManagement
from ..keyboards import get_keyboard
from src.config.environment import env_config
from src.config.response_time_manager import response_time_manager
from src.utils.logging import setup_logging

logger = setup_logging(__name__, 'response_time_management.log')


def register_response_time_handlers(dp: Di<PERSON>atcher, telethon_manager, response_tracker):
    """Регистрация обработчиков управления временем ответа"""
    
    @dp.message(lambda m: m.text == "⏰ Время ответа")
    async def response_time_management(message: types.Message):
        user_id = message.from_user.id
        
        # Только для администраторов
        if not env_config.is_admin(user_id):
            await message.answer("❌ Доступ запрещен")
            return
        
        # Получаем текущие настройки
        settings = response_time_manager.get_all_settings()
        global_settings = settings.get("global_settings", {})

        # Получаем статус трекера
        tracker_status = response_tracker.get_status() if response_tracker else {"running": False, "pending_messages_count": 0}

        enabled = global_settings.get("enabled", False)
        default_time = global_settings.get("default_response_time_minutes", 10)
        check_interval = global_settings.get("check_interval_seconds", 30)

        status_emoji = "🟢" if enabled else "🔴"
        status_text = "Включен" if enabled else "Выключен"

        text = (
            f"⏰ **Управление временем ответа**\n\n"
            f"📊 **Текущий статус:** {status_emoji} {status_text}\n"
            f"⏱️ **Время ответа по умолчанию:** {default_time} мин\n"
            f"🔄 **Интервал проверки:** {check_interval} сек\n"
            f"🔄 **Активных дедлайнов:** {tracker_status.get('pending_messages_count', 0)}\n\n"
            f"Используйте кнопки ниже для управления:"
        )

        # Импортируем клавиатуру для управления временем ответа
        from ..keyboards import response_time_management_keyboard

        await message.answer(text, reply_markup=response_time_management_keyboard(), parse_mode='Markdown')

    @dp.callback_query(lambda c: c.data == "response_time_main_settings")
    async def response_time_main_settings(callback: types.CallbackQuery):
        """Обработчик основных настроек времени ответа"""
        await callback.answer()
        
        user_id = callback.from_user.id
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return
        
        settings = response_time_manager.get_all_settings()
        global_settings = settings.get("global_settings", {})
        
        enabled = global_settings.get("enabled", False)
        default_time = global_settings.get("default_response_time_minutes", 10)
        check_interval = global_settings.get("check_interval_seconds", 30)

        status_emoji = "🟢" if enabled else "🔴"
        status_text = "Включен" if enabled else "Выключен"
        
        text = (
            f"⚙️ **Основные настройки времени ответа**\n\n"
            f"📊 **Статус:** {status_emoji} {status_text}\n"
            f"⏱️ **Время ответа по умолчанию:** {default_time} минут\n"
            f"🔄 **Интервал проверки:** {check_interval} секунд\n\n"
            f"💡 **Описание настроек:**\n"
            f"• **Время ответа** - максимальное время для ответа на сообщение лида\n"
            f"• **Интервал проверки** - как часто проверять дедлайны"
        )
        
        from ..keyboards import response_time_main_settings_keyboard
        await callback.message.edit_text(text, reply_markup=response_time_main_settings_keyboard(enabled), parse_mode='Markdown')

    @dp.callback_query(lambda c: c.data == "enable_response_time")
    async def enable_response_time(callback: types.CallbackQuery):
        """Включение мониторинга времени ответа"""
        await callback.answer("🟢 Мониторинг времени ответа включен")
        
        user_id = callback.from_user.id
        success = response_time_manager.set_enabled(True, user_id)
        
        if success:
            # Запускаем трекер если он не запущен
            if response_tracker and not response_tracker._running:
                await response_tracker.start_monitoring()
            
            logger.info(f"Мониторинг времени ответа включен администратором {user_id}")
            
            # Обновляем интерфейс
            await response_time_main_settings(callback)
        else:
            await callback.message.edit_text("❌ Ошибка включения мониторинга времени ответа")

    @dp.callback_query(lambda c: c.data == "disable_response_time")
    async def disable_response_time(callback: types.CallbackQuery):
        """Выключение мониторинга времени ответа"""
        await callback.answer("🔴 Мониторинг времени ответа выключен")
        
        user_id = callback.from_user.id
        success = response_time_manager.set_enabled(False, user_id)
        
        if success:
            # Останавливаем трекер
            if response_tracker and response_tracker._running:
                await response_tracker.stop_monitoring()
            
            logger.info(f"Мониторинг времени ответа выключен администратором {user_id}")
            
            # Обновляем интерфейс
            await response_time_main_settings(callback)
        else:
            await callback.message.edit_text("❌ Ошибка выключения мониторинга времени ответа")

    @dp.callback_query(lambda c: c.data == "set_default_response_time")
    async def set_default_response_time(callback: types.CallbackQuery, state: FSMContext):
        """Установка времени ответа по умолчанию"""
        await callback.answer()
        
        current_time = response_time_manager.default_response_time_minutes
        
        text = (
            f"⏰ **Установка времени ответа по умолчанию**\n\n"
            f"📊 **Текущее значение:** {current_time} минут\n\n"
            f"📝 Введите новое время ответа в минутах (от 1 до 1440):\n\n"
            f"💡 **Примеры:**\n"
            f"• 5 - для быстрого ответа\n"
            f"• 10 - стандартное время\n"
            f"• 30 - для сложных запросов\n"
            f"• 60 - для детального анализа"
        )

        from ..keyboards import response_time_cancel_keyboard
        await callback.message.edit_text(text, reply_markup=response_time_cancel_keyboard(), parse_mode='Markdown')
        await state.set_state(ResponseTimeManagement.waiting_for_default_response_time)

    @dp.message(ResponseTimeManagement.waiting_for_default_response_time)
    async def process_default_response_time(message: types.Message, state: FSMContext):
        """Обработка ввода времени ответа по умолчанию"""
        try:
            minutes = int(message.text.strip())
            
            if minutes < 1 or minutes > 1440:
                await message.answer("❌ Время ответа должно быть от 1 до 1440 минут (24 часа)")
                return
            
            user_id = message.from_user.id
            success = response_time_manager.set_default_response_time(minutes, user_id)
            
            if success:
                await message.answer(
                    f"✅ Время ответа по умолчанию установлено: {minutes} минут",
                    reply_markup=get_keyboard(user_id)
                )
                logger.info(f"Время ответа по умолчанию изменено на {minutes} минут администратором {user_id}")
            else:
                await message.answer("❌ Ошибка сохранения настроек")
            
            await state.clear()
            
        except ValueError:
            await message.answer("❌ Введите корректное число минут")



    @dp.callback_query(lambda c: c.data == "set_check_interval")
    async def set_check_interval(callback: types.CallbackQuery, state: FSMContext):
        """Установка интервала проверки"""
        await callback.answer()

        user_id = callback.from_user.id
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return

        # Получаем текущие настройки
        settings = response_time_manager.get_all_settings()
        global_settings = settings.get("global_settings", {})
        current_interval = global_settings.get("check_interval_seconds", 30)

        text = (
            f"🔄 **Установка интервала проверки**\n\n"
            f"📊 **Текущее значение:** {current_interval} секунд\n\n"
            f"📝 Введите интервал проверки в секундах (от 10 до 300):\n\n"
            f"💡 **Примеры:**\n"
            f"• 10 - очень частая проверка (высокая нагрузка)\n"
            f"• 30 - стандартная проверка\n"
            f"• 60 - умеренная проверка\n"
            f"• 120 - редкая проверка (низкая нагрузка)\n\n"
            f"⚠️ **Примечание:** Меньший интервал = более точное отслеживание, но больше нагрузки"
        )

        await callback.message.edit_text(text, parse_mode='Markdown')
        await state.set_state(ResponseTimeManagement.waiting_for_check_interval)

    @dp.message(ResponseTimeManagement.waiting_for_check_interval)
    async def process_check_interval(message: types.Message, state: FSMContext):
        """Обработка ввода интервала проверки"""
        try:
            seconds = int(message.text.strip())

            if seconds < 10 or seconds > 300:
                await message.answer("❌ Интервал проверки должен быть от 10 до 300 секунд (5 минут)")
                return

            user_id = message.from_user.id
            success = response_time_manager.set_check_interval(seconds, user_id)

            if success:
                await message.answer(
                    f"✅ Интервал проверки установлен: {seconds} секунд",
                    reply_markup=get_keyboard(user_id)
                )
                logger.info(f"Интервал проверки изменен на {seconds} секунд администратором {user_id}")

                # Перезапускаем трекер с новым интервалом если он запущен
                if response_tracker and response_tracker._running:
                    await response_tracker.stop_monitoring()
                    await response_tracker.start_monitoring()
                    await message.answer("🔄 Мониторинг перезапущен с новым интервалом")
            else:
                await message.answer("❌ Ошибка сохранения настроек")

        except ValueError:
            await message.answer("❌ Введите корректное число секунд")

        await state.clear()



    @dp.callback_query(lambda c: c.data == "back_to_response_time_management")
    async def back_to_response_time_management(callback: types.CallbackQuery):
        """Возврат к главному меню управления временем ответа"""
        await callback.answer()

        user_id = callback.from_user.id
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return

        # Получаем настройки
        settings = response_time_manager.get_all_settings()
        global_settings = settings.get("global_settings", {})

        # Получаем статус трекера для активных дедлайнов
        tracker_status = response_tracker.get_status() if response_tracker else {"running": False, "pending_messages_count": 0}

        # Формируем текст
        enabled = global_settings.get("enabled", False)
        status_emoji = "🟢" if enabled else "🔴"
        status_text = "Включен" if enabled else "Выключен"
        default_time = global_settings.get("default_response_time_minutes", 10)
        check_interval = global_settings.get("check_interval_seconds", 30)

        text = (
            f"⏰ **Управление временем ответа**\n\n"
            f"📊 **Текущий статус:** {status_emoji} {status_text}\n"
            f"⏱️ **Время ответа по умолчанию:** {default_time} мин\n"
            f"🔄 **Интервал проверки:** {check_interval} сек\n"
            f"🔄 **Активных дедлайнов:** {tracker_status.get('pending_messages_count', 0)}\n\n"
            f"Используйте кнопки ниже для управления:"
        )

        from ..keyboards import response_time_management_keyboard
        await callback.message.edit_text(text, reply_markup=response_time_management_keyboard(), parse_mode='Markdown')

    @dp.callback_query(lambda c: c.data == "response_time_account_settings")
    async def response_time_account_settings(callback: types.CallbackQuery):
        """Настройки времени ответа для конкретных аккаунтов"""
        await callback.answer()

        user_id = callback.from_user.id
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return

        # Получаем список аккаунтов
        accounts = telethon_manager.get_connected_accounts()

        if not accounts:
            text = (
                f"📱 **Настройки аккаунтов**\n\n"
                f"❌ В системе нет подключенных аккаунтов\n\n"
                f"Сначала подключите аккаунты через меню 'Мои аккаунты'"
            )
            buttons = [
                [types.InlineKeyboardButton(text="◀️ Назад", callback_data="back_to_response_time_management")]
            ]
            keyboard = types.InlineKeyboardMarkup(inline_keyboard=buttons)
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode='Markdown')
            return

        text = (
            f"📱 **Настройки времени ответа по аккаунтам**\n\n"
            f"📊 Всего аккаунтов: {len(accounts)}\n\n"
            f"Выберите аккаунт для настройки:\n"
            f"✅ - авторизован\n"
            f"⚠️ - требует авторизации"
        )

        from ..keyboards import response_time_account_settings_keyboard
        await callback.message.edit_text(text, reply_markup=response_time_account_settings_keyboard(accounts), parse_mode='Markdown')

    @dp.callback_query(lambda c: c.data.startswith("response_time_account:"))
    async def response_time_account_detail(callback: types.CallbackQuery):
        """Детальные настройки для конкретного аккаунта"""
        await callback.answer()

        try:
            phone = callback.data.split(":")[1]

            # Получаем настройки для аккаунта
            account_response_time = response_time_manager.get_response_time_for_account(phone)
            default_response_time = response_time_manager.default_response_time_minutes

            # Проверяем, есть ли индивидуальные настройки
            settings = response_time_manager.get_all_settings()
            has_custom_settings = phone in settings.get("account_specific_settings", {})

            # Получаем количество ожидающих сообщений
            pending_messages = response_tracker.get_pending_messages_for_account(phone) if response_tracker else []

            text = (
                f"📱 **Настройки для аккаунта**\n\n"
                f"📞 **Номер:** {phone}\n"
                f"⏰ **Время ответа:** {account_response_time} мин"
            )

            if has_custom_settings:
                text += " (индивидуальное)"
            else:
                text += f" (по умолчанию: {default_response_time} мин)"

            text += (
                f"\n📋 **Активных дедлайнов:** {len(pending_messages)}\n\n"
                f"💡 **Описание:**\n"
                f"Индивидуальные настройки переопределяют значение по умолчанию только для этого аккаунта."
            )

            from ..keyboards import response_time_account_detail_keyboard
            await callback.message.edit_text(text, reply_markup=response_time_account_detail_keyboard(phone, has_custom_settings), parse_mode='Markdown')

        except (ValueError, IndexError):
            await callback.message.edit_text("❌ Некорректные данные аккаунта")

    @dp.callback_query(lambda c: c.data.startswith("set_account_response_time:"))
    async def set_account_response_time(callback: types.CallbackQuery, state: FSMContext):
        """Установка времени ответа для конкретного аккаунта"""
        await callback.answer()

        try:
            phone = callback.data.split(":")[1]

            current_time = response_time_manager.get_response_time_for_account(phone)
            default_time = response_time_manager.default_response_time_minutes

            text = (
                f"⏰ **Установка времени ответа**\n\n"
                f"📞 **Аккаунт:** {phone}\n"
                f"📊 **Текущее значение:** {current_time} минут\n"
                f"📊 **По умолчанию:** {default_time} минут\n\n"
                f"📝 Введите новое время ответа в минутах (от 1 до 1440):\n\n"
                f"💡 **Примеры:**\n"
                f"• 5 - для VIP клиентов\n"
                f"• 15 - для обычных запросов\n"
                f"• 60 - для сложных вопросов"
            )

            await callback.message.edit_text(text, parse_mode='Markdown')
            await state.update_data(account_phone=phone)
            await state.set_state(ResponseTimeManagement.waiting_for_account_response_time)

        except (ValueError, IndexError):
            await callback.message.edit_text("❌ Некорректные данные аккаунта")

    @dp.message(ResponseTimeManagement.waiting_for_account_response_time)
    async def process_account_response_time(message: types.Message, state: FSMContext):
        """Обработка ввода времени ответа для аккаунта"""
        try:
            minutes = int(message.text.strip())

            if minutes < 1 or minutes > 1440:
                await message.answer("❌ Время ответа должно быть от 1 до 1440 минут (24 часа)")
                return

            data = await state.get_data()
            phone = data.get('account_phone')

            if not phone:
                await message.answer("❌ Ошибка: номер аккаунта не найден")
                await state.clear()
                return

            user_id = message.from_user.id
            success = response_time_manager.set_account_response_time(phone, minutes, user_id)

            if success:
                await message.answer(
                    f"✅ Время ответа для аккаунта {phone} установлено: {minutes} минут",
                    reply_markup=get_keyboard(user_id)
                )
                logger.info(f"Время ответа для аккаунта {phone} изменено на {minutes} минут администратором {user_id}")
            else:
                await message.answer("❌ Ошибка сохранения настроек")

            await state.clear()

        except ValueError:
            await message.answer("❌ Введите корректное число минут")

    @dp.callback_query(lambda c: c.data.startswith("reset_account_response_time:"))
    async def reset_account_response_time(callback: types.CallbackQuery):
        """Сброс индивидуальных настроек времени ответа для аккаунта"""
        await callback.answer()

        user_id = callback.from_user.id
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return

        try:
            phone = callback.data.split(":")[1]

            # Получаем текущие настройки
            current_time = response_time_manager.get_response_time_for_account(phone)
            default_time = response_time_manager.default_response_time_minutes

            # Проверяем, есть ли индивидуальные настройки
            settings = response_time_manager.get_all_settings()
            has_custom_settings = phone in settings.get("account_specific_settings", {})

            if not has_custom_settings:
                await callback.message.edit_text(
                    f"ℹ️ **Аккаунт {phone}**\n\n"
                    f"Уже использует настройки по умолчанию ({default_time} мин).\n"
                    f"Нечего сбрасывать.",
                    parse_mode='Markdown'
                )
                return

            # Выполняем сброс
            success = response_time_manager.remove_account_settings(phone, user_id)

            if success:
                text = (
                    f"✅ **Настройки сброшены**\n\n"
                    f"📱 **Аккаунт:** {phone}\n"
                    f"⏰ **Было:** {current_time} мин (индивидуальное)\n"
                    f"⏰ **Стало:** {default_time} мин (по умолчанию)\n\n"
                    f"Аккаунт теперь использует глобальные настройки времени ответа."
                )
                logger.info(f"Индивидуальные настройки времени ответа для аккаунта {phone} сброшены администратором {user_id}")
            else:
                text = "❌ Ошибка сброса настроек"

            # Возвращаемся к детальным настройкам аккаунта
            from ..keyboards import response_time_account_detail_keyboard
            await callback.message.edit_text(
                text,
                reply_markup=response_time_account_detail_keyboard(phone, False),
                parse_mode='Markdown'
            )

        except (ValueError, IndexError):
            await callback.message.edit_text("❌ Некорректные данные аккаунта")





    @dp.callback_query(lambda c: c.data == "refresh_response_time_management")
    async def refresh_response_time_management(callback: types.CallbackQuery):
        """Обновление главного меню управления временем ответа"""
        await callback.answer("🔄 Обновлено")
        await back_to_response_time_management(callback)

    @dp.callback_query(lambda c: c.data == "response_time_bulk_settings")
    async def response_time_bulk_settings(callback: types.CallbackQuery):
        """Обработчик массовых настроек времени ответа"""
        await callback.answer()

        user_id = callback.from_user.id
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return

        # Получаем количество подключенных аккаунтов
        connected_accounts = telethon_manager.get_connected_accounts()
        accounts_count = len(connected_accounts)

        if accounts_count == 0:
            await callback.message.edit_text(
                "❌ **Нет подключенных аккаунтов**\n\n"
                "Сначала подключите аккаунты для настройки времени ответа.",
                parse_mode='Markdown'
            )
            return

        # Получаем текущие настройки
        settings = response_time_manager.get_all_settings()
        account_settings = settings.get("account_specific_settings", {})
        accounts_with_custom = len(account_settings)
        default_time = response_time_manager.default_response_time_minutes

        text = (
            f"🔧 **Массовые настройки времени ответа**\n\n"
            f"📊 **Статистика аккаунтов:**\n"
            f"• Всего подключено: {accounts_count}\n"
            f"• С индивидуальными настройками: {accounts_with_custom}\n"
            f"• Используют настройки по умолчанию: {accounts_count - accounts_with_custom}\n\n"
            f"⏰ **Время по умолчанию:** {default_time} минут\n\n"
            f"💡 **Доступные операции:**\n"
            f"• **Установить время для всех** - применить одинаковое время ко всем аккаунтам\n"
            f"• **Сбросить все к умолчанию** - удалить индивидуальные настройки"
        )

        from ..keyboards import response_time_bulk_settings_keyboard
        await callback.message.edit_text(text, reply_markup=response_time_bulk_settings_keyboard(), parse_mode='Markdown')

    @dp.callback_query(lambda c: c.data == "bulk_set_response_time")
    async def bulk_set_response_time(callback: types.CallbackQuery, state: FSMContext):
        """Начало процесса массовой установки времени ответа"""
        await callback.answer()

        user_id = callback.from_user.id
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return

        # Получаем список аккаунтов
        connected_accounts = telethon_manager.get_connected_accounts()
        accounts_count = len(connected_accounts)

        if accounts_count == 0:
            await callback.message.edit_text("❌ Нет подключенных аккаунтов")
            return

        current_default = response_time_manager.default_response_time_minutes

        text = (
            f"⏰ **Массовая установка времени ответа**\n\n"
            f"📊 **Будет применено к {accounts_count} аккаунтам:**\n"
        )

        # Показываем список аккаунтов
        for i, account in enumerate(connected_accounts[:5], 1):  # Показываем первые 5
            phone = account['phone']
            current_time = response_time_manager.get_response_time_for_account(phone)
            text += f"• {phone} (сейчас: {current_time} мин)\n"

        if accounts_count > 5:
            text += f"• ... и еще {accounts_count - 5} аккаунтов\n"

        text += (
            f"\n📝 Введите новое время ответа в минутах (от 1 до 1440):\n\n"
            f"💡 **Примеры:**\n"
            f"• 5 - для быстрого ответа\n"
            f"• 10 - стандартное время\n"
            f"• 30 - для сложных запросов\n\n"
            f"📊 **Текущее время по умолчанию:** {current_default} минут"
        )

        await callback.message.edit_text(text, parse_mode='Markdown')

        # Сохраняем список аккаунтов в состояние
        account_phones = [acc['phone'] for acc in connected_accounts]
        await state.update_data(account_phones=account_phones)
        await state.set_state(ResponseTimeManagement.waiting_for_bulk_response_time)

    @dp.message(ResponseTimeManagement.waiting_for_bulk_response_time)
    async def process_bulk_response_time(message: types.Message, state: FSMContext):
        """Обработка ввода времени для массовой установки"""
        try:
            minutes = int(message.text.strip())

            if minutes < 1 or minutes > 1440:
                await message.answer("❌ Время ответа должно быть от 1 до 1440 минут (24 часа)")
                return

            # Получаем данные из состояния
            data = await state.get_data()
            account_phones = data.get('account_phones', [])

            if not account_phones:
                await message.answer("❌ Список аккаунтов не найден")
                await state.clear()
                return

            # Показываем подтверждение
            text = (
                f"⚠️ **Подтверждение массовой операции**\n\n"
                f"⏰ **Время ответа:** {minutes} минут\n"
                f"📱 **Количество аккаунтов:** {len(account_phones)}\n\n"
                f"❗ **Внимание:** Эта операция перезапишет индивидуальные настройки времени ответа для всех подключенных аккаунтов.\n\n"
                f"Вы уверены, что хотите продолжить?"
            )

            # Сохраняем время в состояние
            await state.update_data(bulk_minutes=minutes)

            from ..keyboards import bulk_operation_confirmation_keyboard
            await message.answer(
                text,
                reply_markup=bulk_operation_confirmation_keyboard("set", len(account_phones)),
                parse_mode='Markdown'
            )

        except ValueError:
            await message.answer("❌ Введите корректное число минут")
        except Exception as e:
            logger.error(f"Ошибка обработки массового времени ответа: {e}")
            await message.answer("❌ Произошла ошибка при обработке")
            await state.clear()

    @dp.callback_query(lambda c: c.data == "confirm_bulk_set_response_time")
    async def confirm_bulk_set_response_time(callback: types.CallbackQuery, state: FSMContext):
        """Подтверждение массовой установки времени ответа"""
        await callback.answer()

        user_id = callback.from_user.id

        try:
            # Получаем данные из состояния
            data = await state.get_data()
            account_phones = data.get('account_phones', [])
            minutes = data.get('bulk_minutes')

            if not account_phones or not minutes:
                await callback.message.edit_text("❌ Данные операции не найдены")
                await state.clear()
                return

            # Выполняем массовую установку
            result = response_time_manager.set_response_time_for_all_accounts(minutes, account_phones, user_id)

            if result["success"]:
                success_text = (
                    f"✅ **Массовая установка завершена!**\n\n"
                    f"⏰ **Установлено время:** {minutes} минут\n"
                    f"📱 **Обработано аккаунтов:** {result['processed_count']}/{result['total_accounts']}\n"
                )

                if result.get('failed_phones'):
                    success_text += f"\n❌ **Ошибки для аккаунтов:** {len(result['failed_phones'])}\n"
                    for phone in result['failed_phones'][:3]:  # Показываем первые 3
                        success_text += f"• {phone}\n"
                    if len(result['failed_phones']) > 3:
                        success_text += f"• ... и еще {len(result['failed_phones']) - 3}\n"

                success_text += f"\n🕐 **Время операции:** {datetime.now().strftime('%H:%M:%S')}"

                # Отправляем уведомление администраторам
                notification_text = (
                    f"🔧 **МАССОВАЯ НАСТРОЙКА ВРЕМЕНИ ОТВЕТА**\n\n"
                    f"👤 **Администратор:** {user_id}\n"
                    f"⏰ **Установлено время:** {minutes} минут\n"
                    f"📱 **Обработано аккаунтов:** {result['processed_count']}\n"
                    f"🕐 **Время:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )

                # Отправляем уведомление через AlertManager
                try:
                    from ..alerts import alert_manager
                    await alert_manager.send_response_time_notification_to_admins(notification_text)
                except Exception as e:
                    logger.error(f"Ошибка отправки уведомления о массовой настройке: {e}")

                await callback.message.edit_text(success_text, parse_mode='Markdown')
                logger.info(f"Массовая установка времени ответа {minutes} мин для {result['processed_count']} аккаунтов администратором {user_id}")

            else:
                error_text = f"❌ **Ошибка массовой установки:**\n{result.get('error', 'Неизвестная ошибка')}"
                await callback.message.edit_text(error_text, parse_mode='Markdown')

            await state.clear()

        except Exception as e:
            logger.error(f"Ошибка подтверждения массовой установки: {e}")
            await callback.message.edit_text("❌ Произошла ошибка при выполнении операции")
            await state.clear()

    @dp.callback_query(lambda c: c.data == "bulk_clear_response_time")
    async def bulk_clear_response_time(callback: types.CallbackQuery, state: FSMContext):
        """Массовая очистка индивидуальных настроек времени ответа"""
        await callback.answer()

        user_id = callback.from_user.id
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return

        # Получаем список аккаунтов с индивидуальными настройками
        settings = response_time_manager.get_all_settings()
        account_settings = settings.get("account_specific_settings", {})

        if not account_settings:
            await callback.message.edit_text(
                "ℹ️ **Нет индивидуальных настроек**\n\n"
                "Все аккаунты уже используют настройки по умолчанию.",
                parse_mode='Markdown'
            )
            return

        accounts_with_custom = list(account_settings.keys())
        default_time = response_time_manager.default_response_time_minutes

        text = (
            f"🗑️ **Сброс к настройкам по умолчанию**\n\n"
            f"📊 **Аккаунтов с индивидуальными настройками:** {len(accounts_with_custom)}\n"
            f"⏰ **Время по умолчанию:** {default_time} минут\n\n"
            f"📱 **Аккаунты для сброса:**\n"
        )

        # Показываем аккаунты с индивидуальными настройками
        for i, phone in enumerate(accounts_with_custom[:5], 1):  # Показываем первые 5
            current_time = account_settings[phone].get('response_time_minutes', default_time)
            text += f"• {phone} ({current_time} мин → {default_time} мин)\n"

        if len(accounts_with_custom) > 5:
            text += f"• ... и еще {len(accounts_with_custom) - 5} аккаунтов\n"

        text += (
            f"\n❗ **Внимание:** Эта операция удалит все индивидуальные настройки времени ответа. "
            f"Все аккаунты будут использовать время по умолчанию ({default_time} минут).\n\n"
            f"Вы уверены, что хотите продолжить?"
        )

        # Сохраняем список аккаунтов в состояние
        await state.update_data(accounts_to_clear=accounts_with_custom)

        from ..keyboards import bulk_operation_confirmation_keyboard
        await callback.message.edit_text(
            text,
            reply_markup=bulk_operation_confirmation_keyboard("clear", len(accounts_with_custom)),
            parse_mode='Markdown'
        )

    @dp.callback_query(lambda c: c.data == "confirm_bulk_clear_response_time")
    async def confirm_bulk_clear_response_time(callback: types.CallbackQuery, state: FSMContext):
        """Подтверждение массовой очистки настроек времени ответа"""
        await callback.answer()

        user_id = callback.from_user.id

        try:
            # Получаем данные из состояния
            data = await state.get_data()
            accounts_to_clear = data.get('accounts_to_clear', [])

            if not accounts_to_clear:
                await callback.message.edit_text("❌ Список аккаунтов для очистки не найден")
                await state.clear()
                return

            # Выполняем массовую очистку
            result = response_time_manager.clear_all_account_settings(accounts_to_clear, user_id)

            if result["success"]:
                default_time = response_time_manager.default_response_time_minutes
                success_text = (
                    f"✅ **Массовая очистка завершена!**\n\n"
                    f"🗑️ **Очищено настроек:** {result['cleared_count']}/{result['total_accounts']}\n"
                    f"⏰ **Все аккаунты используют:** {default_time} минут\n"
                )

                if result.get('not_found_phones'):
                    success_text += f"\n⚠️ **Не найдено настроек:** {len(result['not_found_phones'])}\n"

                success_text += f"\n🕐 **Время операции:** {datetime.now().strftime('%H:%M:%S')}"

                # Отправляем уведомление администраторам
                notification_text = (
                    f"🗑️ **МАССОВАЯ ОЧИСТКА НАСТРОЕК ВРЕМЕНИ ОТВЕТА**\n\n"
                    f"👤 **Администратор:** {user_id}\n"
                    f"🗑️ **Очищено настроек:** {result['cleared_count']}\n"
                    f"⏰ **Время по умолчанию:** {default_time} минут\n"
                    f"🕐 **Время:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )

                # Отправляем уведомление через AlertManager
                try:
                    from ..alerts import alert_manager
                    await alert_manager.send_response_time_notification_to_admins(notification_text)
                except Exception as e:
                    logger.error(f"Ошибка отправки уведомления о массовой очистке: {e}")

                await callback.message.edit_text(success_text, parse_mode='Markdown')
                logger.info(f"Массовая очистка настроек времени ответа для {result['cleared_count']} аккаунтов администратором {user_id}")

            else:
                error_text = f"❌ **Ошибка массовой очистки:**\n{result.get('error', 'Неизвестная ошибка')}"
                await callback.message.edit_text(error_text, parse_mode='Markdown')

            await state.clear()

        except Exception as e:
            logger.error(f"Ошибка подтверждения массовой очистки: {e}")
            await callback.message.edit_text("❌ Произошла ошибка при выполнении операции")
            await state.clear()

    @dp.callback_query(lambda c: c.data == "back_to_response_time_account_settings")
    async def back_to_response_time_account_settings(callback: types.CallbackQuery):
        """Возврат к настройкам аккаунтов"""
        await callback.answer()
        await response_time_account_settings(callback)

    @dp.callback_query(lambda c: c.data == "cancel_response_time_input")
    async def cancel_response_time_input(callback: types.CallbackQuery, state: FSMContext):
        """Отмена ввода времени ответа"""
        await callback.answer()

        # Очищаем состояние
        await state.clear()

        # Возвращаемся к основным настройкам времени ответа
        user_id = callback.from_user.id
        if not env_config.is_admin(user_id):
            await callback.message.edit_text("❌ Доступ запрещен")
            return

        settings = response_time_manager.get_all_settings()
        global_settings = settings.get("global_settings", {})

        enabled = global_settings.get("enabled", False)
        default_time = global_settings.get("default_response_time_minutes", 10)
        check_interval = global_settings.get("check_interval_seconds", 30)

        status_emoji = "🟢" if enabled else "🔴"
        status_text = "Включен" if enabled else "Выключен"

        text = (
            f"⚙️ **Основные настройки времени ответа**\n\n"
            f"📊 **Статус:** {status_emoji} {status_text}\n"
            f"⏱️ **Время ответа по умолчанию:** {default_time} минут\n"
            f"🔄 **Интервал проверки:** {check_interval} секунд\n\n"
            f"💡 **Описание настроек:**\n"
            f"• **Время ответа** - максимальное время для ответа на сообщение лида\n"
            f"• **Интервал проверки** - как часто проверять дедлайны"
        )

        from ..keyboards import response_time_main_settings_keyboard
        await callback.message.edit_text(text, reply_markup=response_time_main_settings_keyboard(enabled), parse_mode='Markdown')

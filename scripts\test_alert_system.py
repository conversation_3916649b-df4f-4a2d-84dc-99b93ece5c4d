#!/usr/bin/env python3
"""
Простой тест системы алертов
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.monitoring.response_tracker import ResponseTracker, PendingMessage

async def test_alert_system():
    """Тест системы алертов"""
    print("🧪 Тест системы алертов времени ответа")
    print("=" * 50)
    
    alerts_received = []
    
    async def alert_callback(phone: str, alert_text: str):
        alert = {
            'phone': phone,
            'alert': alert_text,
            'timestamp': datetime.now()
        }
        alerts_received.append(alert)
        print(f"🚨 АЛЕРТ: {phone}")
        print(f"   {alert_text[:100]}...")
    
    # Создаем трекер
    tracker = ResponseTracker(alert_callback)
    
    # Создаем просроченное сообщение напрямую
    now = datetime.now()
    expired_deadline = now - timedelta(minutes=1)  # Дедлайн час назад
    
    pending_message = PendingMessage(
        message_id=12345,
        sender_id=*********,
        sender_name='Тестовый Лид',
        chat_id=*********,
        chat_title='Тестовый чат',
        account_phone='+************',
        received_at=now - timedelta(minutes=10),
        deadline=expired_deadline,
        text='Тестовое сообщение для алерта',
        is_from_external=True
    )
    
    # Добавляем сообщение напрямую в трекер
    message_key = f"{pending_message.account_phone}_{pending_message.chat_id}_{pending_message.message_id}"
    tracker.pending_messages[message_key] = pending_message
    
    print(f"📝 Добавлено просроченное сообщение:")
    print(f"   Дедлайн: {expired_deadline.strftime('%H:%M:%S')}")
    print(f"   Текущее время: {now.strftime('%H:%M:%S')}")
    print(f"   Просрочено на: {now - expired_deadline}")
    
    # Запускаем мониторинг
    await tracker.start_monitoring()
    
    print(f"\n⏳ Ожидание алерта (3 секунды)...")
    await asyncio.sleep(3)
    
    # Проверяем результаты
    print(f"\n📊 Результаты:")
    print(f"   Получено алертов: {len(alerts_received)}")
    
    if alerts_received:
        for i, alert in enumerate(alerts_received, 1):
            print(f"   {i}. {alert['phone']} в {alert['timestamp'].strftime('%H:%M:%S')}")
            print(f"      {alert['alert'][:100]}...")
        print("\n✅ Система алертов работает!")
    else:
        print("\n❌ Алерты не получены")
    
    await tracker.stop_monitoring()
    
    return len(alerts_received) > 0

if __name__ == "__main__":
    success = asyncio.run(test_alert_system())
    sys.exit(0 if success else 1)

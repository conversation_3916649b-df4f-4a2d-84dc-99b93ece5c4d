"""
Управление настройками мониторинга
"""

import json
import logging
from typing import Dict, Any
from pathlib import Path
from .settings import MONITORING_CONFIG_FILE

logger = logging.getLogger(__name__)

class MonitoringSettings:
    """Класс для управления настройками мониторинга"""
    
    def __init__(self):
        self.config_file = MONITORING_CONFIG_FILE
        self._settings = self._load_settings()
    
    def _load_settings(self) -> Dict[str, Any]:
        """Загрузка настроек из файла"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                logger.info(f"Настройки мониторинга загружены из {self.config_file}")
                return settings
            else:
                # Настройки по умолчанию
                default_settings = {
                    "group_chat_monitoring_enabled": True,  # По умолчанию включен
                    "private_chat_monitoring_enabled": True,  # Всегда включен
                    "bot_filtering_enabled": True,  # По умолчанию фильтрация ботов включена
                    "last_updated": None,
                    "updated_by": None
                }
                self._save_settings(default_settings)
                logger.info("Созданы настройки мониторинга по умолчанию")
                return default_settings
        except Exception as e:
            logger.error(f"Ошибка загрузки настроек мониторинга: {e}")
            # Возвращаем настройки по умолчанию при ошибке
            return {
                "group_chat_monitoring_enabled": True,
                "private_chat_monitoring_enabled": True,
                "bot_filtering_enabled": True,
                "last_updated": None,
                "updated_by": None
            }
    
    def _save_settings(self, settings: Dict[str, Any]) -> bool:
        """Сохранение настроек в файл"""
        try:
            # Создаем директорию если её нет
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Настройки мониторинга сохранены в {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Ошибка сохранения настроек мониторинга: {e}")
            return False
    
    @property
    def group_chat_monitoring_enabled(self) -> bool:
        """Включен ли мониторинг групповых чатов"""
        return self._settings.get("group_chat_monitoring_enabled", True)
    
    @property
    def private_chat_monitoring_enabled(self) -> bool:
        """Включен ли мониторинг личных сообщений (всегда True)"""
        return self._settings.get("private_chat_monitoring_enabled", True)

    @property
    def bot_filtering_enabled(self) -> bool:
        """Включена ли фильтрация алертов от ботов"""
        return self._settings.get("bot_filtering_enabled", True)
    
    def set_group_chat_monitoring(self, enabled: bool, updated_by: int = None) -> bool:
        """
        Установка состояния мониторинга групповых чатов
        
        Args:
            enabled (bool): Включить/выключить мониторинг групповых чатов
            updated_by (int): ID пользователя, который изменил настройку
            
        Returns:
            bool: Успешность операции
        """
        try:
            from datetime import datetime
            
            self._settings["group_chat_monitoring_enabled"] = enabled
            self._settings["last_updated"] = datetime.now().isoformat()
            self._settings["updated_by"] = updated_by
            
            success = self._save_settings(self._settings)
            
            if success:
                status = "включен" if enabled else "отключен"
                logger.info(f"Мониторинг групповых чатов {status} пользователем {updated_by}")
            
            return success
        except Exception as e:
            logger.error(f"Ошибка установки настройки мониторинга групповых чатов: {e}")
            return False

    def set_bot_filtering(self, enabled: bool, updated_by: int = None) -> bool:
        """
        Установка состояния фильтрации алертов от ботов

        Args:
            enabled (bool): Включить/выключить фильтрацию ботов
            updated_by (int): ID пользователя, который изменил настройку

        Returns:
            bool: Успешность операции
        """
        try:
            from datetime import datetime

            self._settings["bot_filtering_enabled"] = enabled
            self._settings["last_updated"] = datetime.now().isoformat()
            self._settings["updated_by"] = updated_by

            success = self._save_settings(self._settings)

            if success:
                status = "включена" if enabled else "отключена"
                logger.info(f"Фильтрация алертов от ботов {status} пользователем {updated_by}")

            return success
        except Exception as e:
            logger.error(f"Ошибка установки настройки фильтрации ботов: {e}")
            return False
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """
        Получение полного статуса мониторинга
        
        Returns:
            Dict[str, Any]: Словарь с настройками мониторинга
        """
        return {
            "group_chat_monitoring": self.group_chat_monitoring_enabled,
            "private_chat_monitoring": self.private_chat_monitoring_enabled,
            "bot_filtering": self.bot_filtering_enabled,
            "last_updated": self._settings.get("last_updated"),
            "updated_by": self._settings.get("updated_by")
        }
    
    def should_monitor_chat(self, chat_type: str) -> bool:
        """
        Определение, нужно ли мониторить чат определенного типа
        
        Args:
            chat_type (str): Тип чата ('private', 'group', 'supergroup', 'channel')
            
        Returns:
            bool: True если чат нужно мониторить
        """
        if chat_type == 'private':
            return self.private_chat_monitoring_enabled
        elif chat_type in ['group', 'supergroup', 'channel']:
            return self.group_chat_monitoring_enabled
        else:
            # Для неизвестных типов используем настройку групповых чатов
            return self.group_chat_monitoring_enabled
    
    def reload_settings(self) -> bool:
        """
        Перезагрузка настроек из файла
        
        Returns:
            bool: Успешность операции
        """
        try:
            self._settings = self._load_settings()
            logger.info("Настройки мониторинга перезагружены")
            return True
        except Exception as e:
            logger.error(f"Ошибка перезагрузки настроек мониторинга: {e}")
            return False

# Глобальный экземпляр настроек мониторинга
monitoring_settings = MonitoringSettings()

"""
Тесты обработчиков admin.py

Покрывает административные функции, проверку прав доступа и статистику
с мокированием административных прав.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch

from aiogram import Dispatcher
from aiogram.types import Message, User, Chat
from aiogram.filters import Command

from src.bot.handlers.admin import register_admin_handlers
from src.config.environment import env_config
from src.monitoring.telethon_manager import TelethonManager
from tests.fixtures.test_constants import TEST_ADMIN_USER_ID, TEST_REGULAR_USER_ID
from tests.utils.mock_factories import (
    MockFactory, TelethonMockFactory, create_admin_user, create_regular_user, create_test_accounts
)


class TestAdminHandlersRegistration:
    """Тесты регистрации административных обработчиков"""
    
    def test_register_admin_handlers(self):
        """Тест регистрации административных обработчиков"""
        mock_dp = Mock(spec=Dispatcher)
        mock_telethon_manager = Mock(spec=TelethonManager)
        
        # Мокируем декораторы
        mock_dp.message = Mock(return_value=lambda func: func)
        
        register_admin_handlers(mock_dp, mock_telethon_manager)
        
        # Проверяем что декораторы были вызваны
        assert mock_dp.message.call_count >= 4  # Минимум для основных админских функций


class TestManageStopwordsHandler:
    """Тесты обработчика 'Управление стоп-словами'"""
    
    @pytest.mark.asyncio
    async def test_manage_stopwords_admin_access(self):
        """Тест доступа администратора к управлению стоп-словами"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "Управление стоп-словами")
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_telethon_manager.reload_stopwords.return_value = 15
        
        with patch.object(env_config, 'is_admin', return_value=True):
            async def mock_manage_stopwords_handler(message: Message):
                user_id = message.from_user.id
                
                # Только администратор может управлять стоп-словами
                if not env_config.is_admin(user_id):
                    await message.answer("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
                    return
                
                stopwords_count = mock_telethon_manager.reload_stopwords()
                
                text = (
                    f"📝 Управление стоп-словами\n\n"
                    f"Текущее количество: {stopwords_count}\n\n"
                    f"Для добавления/изменения стоп-слов отредактируйте файл:\n"
                    f"`data/stopwords.txt`\n\n"
                    f"После изменения используйте команду /reload_stopwords"
                )
                
                await message.answer(text, parse_mode='Markdown')
            
            await mock_manage_stopwords_handler(message)
            
            # Проверяем что reload_stopwords был вызван
            mock_telethon_manager.reload_stopwords.assert_called_once()
            
            # Проверяем ответ
            message.answer.assert_called_once()
            call_args = message.answer.call_args
            response_text = call_args[0][0]
            
            assert "📝 Управление стоп-словами" in response_text
            assert "Текущее количество: 15" in response_text
            assert "data/stopwords.txt" in response_text
            assert "/reload_stopwords" in response_text
            
            # Проверяем что использовался Markdown
            call_kwargs = call_args[1]
            assert call_kwargs.get('parse_mode') == 'Markdown'
    
    @pytest.mark.asyncio
    async def test_manage_stopwords_regular_user_denied(self):
        """Тест запрета доступа обычному пользователю"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "Управление стоп-словами")
        
        with patch.object(env_config, 'is_admin', return_value=False):
            async def mock_manage_stopwords_handler(message: Message):
                user_id = message.from_user.id
                
                # Только администратор может управлять стоп-словами
                if not env_config.is_admin(user_id):
                    await message.answer("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
                    return
                
                await message.answer("Admin content")
            
            await mock_manage_stopwords_handler(message)
            
            # Проверяем что доступ был запрещен
            message.answer.assert_called_once()
            call_args = message.answer.call_args
            response_text = call_args[0][0]
            
            assert "❌ Доступ запрещен" in response_text
            assert "Только администратор" in response_text
    
    @pytest.mark.asyncio
    async def test_manage_stopwords_zero_count(self):
        """Тест отображения нулевого количества стоп-слов"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "Управление стоп-словами")
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_telethon_manager.reload_stopwords.return_value = 0
        
        with patch.object(env_config, 'is_admin', return_value=True):
            async def mock_manage_stopwords_handler(message: Message):
                stopwords_count = mock_telethon_manager.reload_stopwords()
                text = f"Текущее количество: {stopwords_count}"
                await message.answer(text)
            
            await mock_manage_stopwords_handler(message)
            
            call_args = message.answer.call_args
            response_text = call_args[0][0]
            
            assert "Текущее количество: 0" in response_text



class TestStatisticsHandler:
    """Тесты обработчика 'Статистика'"""
    
    @pytest.mark.asyncio
    async def test_statistics_admin_access(self):
        """Тест доступа администратора к статистике"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "Статистика")
        
        test_accounts = [
            {'user_id': 123, 'phone': '+***********', 'monitoring': True},
            {'user_id': 456, 'phone': '+***********', 'monitoring': False},
            {'user_id': 789, 'phone': '+***********', 'monitoring': True}
        ]
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager(test_accounts)
        mock_telethon_manager.stopwords_manager.stopwords = ["спам", "реклама", "продажа"]
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch.object(env_config, 'ADMIN_CHAT_IDS', [TEST_ADMIN_USER_ID, 999999]):
                with patch('src.bot.handlers.admin.datetime') as mock_datetime:
                    mock_datetime.now.return_value.strftime.return_value = "2023-12-01 15:30:45"
                    
                    async def mock_statistics_handler(message: Message):
                        user_id = message.from_user.id
                        
                        # Только для администратора
                        if not env_config.is_admin(user_id):
                            await message.answer("❌ Доступ запрещен")
                            return
                        
                        accounts = mock_telethon_manager.get_connected_accounts()
                        active_accounts = [acc for acc in accounts if acc['monitoring']]
                        stopwords_count = len(mock_telethon_manager.stopwords_manager.stopwords)
                        
                        text = (
                            f"📊 Статистика системы\n\n"
                            f"📱 Всего аккаунтов: {len(accounts)}\n"
                            f"🟢 Активных: {len(active_accounts)}\n"
                            f"🔴 Неактивных: {len(accounts) - len(active_accounts)}\n"
                            f"📝 Стоп-слов: {stopwords_count}\n"
                            f"👑 Администраторов: {len(env_config.ADMIN_CHAT_IDS)}\n"
                            f"🕐 Время: {mock_datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                        )
                        
                        await message.answer(text)
                    
                    await mock_statistics_handler(message)
                    
                    # Проверяем ответ
                    message.answer.assert_called_once()
                    call_args = message.answer.call_args
                    response_text = call_args[0][0]
                    
                    assert "📊 Статистика системы" in response_text
                    assert "📱 Всего аккаунтов: 3" in response_text
                    assert "🟢 Активных: 2" in response_text
                    assert "🔴 Неактивных: 1" in response_text
                    assert "📝 Стоп-слов: 3" in response_text
                    assert "👑 Администраторов: 2" in response_text
                    assert "🕐 Время: 2023-12-01 15:30:45" in response_text
    
    @pytest.mark.asyncio
    async def test_statistics_empty_system(self):
        """Тест статистики пустой системы"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "Статистика")
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager([])
        mock_telethon_manager.stopwords_manager.stopwords = []
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch.object(env_config, 'ADMIN_CHAT_IDS', [TEST_ADMIN_USER_ID]):
                async def mock_statistics_handler(message: Message):
                    accounts = mock_telethon_manager.get_connected_accounts()
                    active_accounts = [acc for acc in accounts if acc['monitoring']]
                    stopwords_count = len(mock_telethon_manager.stopwords_manager.stopwords)
                    
                    text = (
                        f"📊 Статистика системы\n\n"
                        f"📱 Всего аккаунтов: {len(accounts)}\n"
                        f"🟢 Активных: {len(active_accounts)}\n"
                        f"🔴 Неактивных: {len(accounts) - len(active_accounts)}\n"
                        f"📝 Стоп-слов: {stopwords_count}\n"
                        f"👑 Администраторов: {len(env_config.ADMIN_CHAT_IDS)}"
                    )
                    
                    await message.answer(text)
                
                await mock_statistics_handler(message)
                
                call_args = message.answer.call_args
                response_text = call_args[0][0]
                
                assert "📱 Всего аккаунтов: 0" in response_text
                assert "🟢 Активных: 0" in response_text
                assert "🔴 Неактивных: 0" in response_text
                assert "📝 Стоп-слов: 0" in response_text
                assert "👑 Администраторов: 1" in response_text
    
    @pytest.mark.asyncio
    async def test_statistics_regular_user_denied(self):
        """Тест запрета доступа обычному пользователю к статистике"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "Статистика")
        
        with patch.object(env_config, 'is_admin', return_value=False):
            async def mock_statistics_handler(message: Message):
                user_id = message.from_user.id
                
                # Только для администратора
                if not env_config.is_admin(user_id):
                    await message.answer("❌ Доступ запрещен")
                    return
                
                await message.answer("Admin content")
            
            await mock_statistics_handler(message)
            
            # Проверяем что доступ был запрещен
            message.answer.assert_called_once_with("❌ Доступ запрещен")


class TestReloadStopwordsCommand:
    """Тесты команды /reload_stopwords"""

    @pytest.mark.asyncio
    async def test_reload_stopwords_admin_success(self):
        """Тест успешной перезагрузки стоп-слов администратором"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "/reload_stopwords")

        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_telethon_manager.reload_stopwords.return_value = 25

        with patch.object(env_config, 'is_admin', return_value=True):
            async def mock_reload_stopwords_handler(message: Message):
                user_id = message.from_user.id

                # Только для администратора
                if not env_config.is_admin(user_id):
                    await message.answer("❌ Доступ запрещен")
                    return

                try:
                    count = mock_telethon_manager.reload_stopwords()
                    await message.answer(f"✅ Стоп-слова перезагружены. Загружено: {count}")
                except Exception as e:
                    await message.answer(f"❌ Ошибка перезагрузки: {e}")

            await mock_reload_stopwords_handler(message)

            # Проверяем что reload_stopwords был вызван
            mock_telethon_manager.reload_stopwords.assert_called_once()

            # Проверяем ответ
            message.answer.assert_called_once()
            call_args = message.answer.call_args
            response_text = call_args[0][0]

            assert "✅ Стоп-слова перезагружены" in response_text
            assert "Загружено: 25" in response_text

    @pytest.mark.asyncio
    async def test_reload_stopwords_admin_error(self):
        """Тест ошибки при перезагрузке стоп-слов"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "/reload_stopwords")

        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_telethon_manager.reload_stopwords.side_effect = Exception("File not found")

        with patch.object(env_config, 'is_admin', return_value=True):
            async def mock_reload_stopwords_handler(message: Message):
                user_id = message.from_user.id

                if not env_config.is_admin(user_id):
                    await message.answer("❌ Доступ запрещен")
                    return

                try:
                    count = mock_telethon_manager.reload_stopwords()
                    await message.answer(f"✅ Стоп-слова перезагружены. Загружено: {count}")
                except Exception as e:
                    await message.answer(f"❌ Ошибка перезагрузки: {e}")

            await mock_reload_stopwords_handler(message)

            # Проверяем что ошибка была обработана
            message.answer.assert_called_once()
            call_args = message.answer.call_args
            response_text = call_args[0][0]

            assert "❌ Ошибка перезагрузки" in response_text
            assert "File not found" in response_text

    @pytest.mark.asyncio
    async def test_reload_stopwords_regular_user_denied(self):
        """Тест запрета доступа обычному пользователю к команде перезагрузки"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "/reload_stopwords")

        with patch.object(env_config, 'is_admin', return_value=False):
            async def mock_reload_stopwords_handler(message: Message):
                user_id = message.from_user.id

                # Только для администратора
                if not env_config.is_admin(user_id):
                    await message.answer("❌ Доступ запрещен")
                    return

                await message.answer("Admin content")

            await mock_reload_stopwords_handler(message)

            # Проверяем что доступ был запрещен
            message.answer.assert_called_once_with("❌ Доступ запрещен")


class TestAdminHandlersEdgeCases:
    """Тесты граничных случаев для административных обработчиков"""

    @pytest.mark.asyncio
    async def test_admin_check_with_none_user(self):
        """Тест проверки админских прав с None пользователем"""
        message = Mock()
        message.from_user = None
        message.answer = AsyncMock()

        async def mock_admin_handler(message: Message):
            if not message.from_user:
                await message.answer("❌ Ошибка: пользователь не определен")
                return

            user_id = message.from_user.id
            if not env_config.is_admin(user_id):
                await message.answer("❌ Доступ запрещен")
                return

            await message.answer("Admin content")

        await mock_admin_handler(message)

        # Проверяем что ошибка была обработана
        message.answer.assert_called_once_with("❌ Ошибка: пользователь не определен")



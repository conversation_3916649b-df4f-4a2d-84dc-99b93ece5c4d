"""
Тесты обработчиков stopwords_management.py

Покрывает все inline/reply обработчики и FSM состояния StopWordsManagement
с полным покрытием callback-обработчиков.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, mock_open
from io import BytesIO

from aiogram import Dispatcher
from aiogram.types import Message, CallbackQuery, User, Chat, Document
from aiogram.fsm.context import FSMContext

from src.bot.handlers.stopwords_management import register_stopwords_handlers
from src.bot.states import StopWordsManagement
from src.utils.stopwords import StopWordsManager
from src.bot.validators import StopWordValidator, ValidationResult
from src.config.environment import env_config
from tests.fixtures.test_constants import (
    TEST_ADMIN_USER_ID, TEST_REGULAR_USER_ID, VALID_STOPWORDS, INVALID_STOPWORDS
)
from tests.utils.mock_factories import (
    MockFactory, StopWordsMockFactory, ValidationMockFactory,
    create_admin_user, create_regular_user
)


class TestStopwordsHandlersRegistration:
    """Тесты регистрации обработчиков управления стоп-словами"""
    
    def test_register_stopwords_handlers(self):
        """Тест регистрации всех обработчиков стоп-слов"""
        mock_dp = Mock(spec=Dispatcher)
        
        # Мокируем декораторы
        mock_dp.message = Mock()
        mock_dp.message.register = Mock()
        mock_dp.callback_query = Mock()
        mock_dp.callback_query.register = Mock()
        
        register_stopwords_handlers(mock_dp)
        
        # Проверяем что обработчики были зарегистрированы
        assert mock_dp.message.register.call_count >= 6  # Reply-обработчики
        assert mock_dp.callback_query.register.call_count >= 5  # Callback-обработчики


class TestShowStopwordsMainMenu:
    """Тесты главного меню управления стоп-словами"""
    
    @pytest.mark.asyncio
    async def test_show_main_menu_admin_access(self):
        """Тест доступа администратора к главному меню"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "Управление стоп-словами")
        
        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager()
        mock_stopwords_manager.get_detailed_stats = Mock(return_value={
            'total_count': 15,
            'average_length': 6.5,
            'file_info': {'size_formatted': '1.2 KB'}
        })
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
                with patch('src.bot.handlers.stopwords_management.stopwords_main_keyboard') as mock_keyboard:
                    mock_keyboard.return_value = Mock()
                    
                    # Создаем обработчик напрямую
                    async def mock_show_main_menu(message: Message):
                        user_id = message.from_user.id
                        
                        if not env_config.is_admin(user_id):
                            await message.answer("❌ Доступ запрещен.")
                            return
                        
                        stats = mock_stopwords_manager.get_detailed_stats()
                        
                        menu_text = (
                            f"📝 **Управление стоп-словами**\n\n"
                            f"📊 **Текущая статистика:**\n"
                            f"• Всего стоп-слов: **{stats['total_count']}**\n"
                            f"• Средняя длина: **{stats['average_length']:.1f}** символов\n"
                            f"• Размер файла: **{stats['file_info']['size_formatted']}**\n\n"
                            f"🔧 **Доступные операции:**\n"
                            f"• Просмотр всех стоп-слов с пагинацией\n"
                            f"• Добавление новых стоп-слов с валидацией\n"
                            f"• Поиск по существующим стоп-словам\n"
                            f"• Импорт/экспорт списков стоп-слов\n\n"
                            f"👇 Выберите действие из меню ниже:"
                        )
                        
                        await message.answer(
                            menu_text,
                            parse_mode='Markdown',
                            reply_markup=mock_keyboard()
                        )
                    
                    await mock_show_main_menu(message)
                    
                    # Проверяем что статистика была получена
                    mock_stopwords_manager.get_detailed_stats.assert_called_once()
                    
                    # Проверяем ответ
                    message.answer.assert_called_once()
                    call_args = message.answer.call_args
                    response_text = call_args[0][0]
                    
                    assert "📝 **Управление стоп-словами**" in response_text
                    assert "Всего стоп-слов: **15**" in response_text
                    assert "Средняя длина: **6.5**" in response_text
                    assert "Размер файла: **1.2 KB**" in response_text
                    
                    # Проверяем параметры вызова
                    call_kwargs = call_args[1]
                    assert call_kwargs['parse_mode'] == 'Markdown'
                    assert 'reply_markup' in call_kwargs
    
    @pytest.mark.asyncio
    async def test_show_main_menu_regular_user_denied(self):
        """Тест запрета доступа обычному пользователю"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "Управление стоп-словами")
        
        with patch.object(env_config, 'is_admin', return_value=False):
            async def mock_show_main_menu(message: Message):
                user_id = message.from_user.id
                
                if not env_config.is_admin(user_id):
                    await message.answer("❌ Доступ запрещен.")
                    return
                
                await message.answer("Admin content")
            
            await mock_show_main_menu(message)
            
            # Проверяем что доступ был запрещен
            message.answer.assert_called_once_with("❌ Доступ запрещен.")
    
    @pytest.mark.asyncio
    async def test_show_main_menu_stats_error(self):
        """Тест обработки ошибки получения статистики"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "Управление стоп-словами")
        
        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager()
        mock_stopwords_manager.get_detailed_stats.side_effect = Exception("Stats error")
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
                async def mock_show_main_menu(message: Message):
                    try:
                        stats = mock_stopwords_manager.get_detailed_stats()
                        await message.answer("Success")
                    except Exception as e:
                        await message.answer(f"❌ Ошибка получения статистики: {e}")
                
                await mock_show_main_menu(message)
                
                # Проверяем что ошибка была обработана
                message.answer.assert_called_once()
                call_args = message.answer.call_args
                response_text = call_args[0][0]
                
                assert "❌ Ошибка получения статистики" in response_text
                assert "Stats error" in response_text


class TestShowStopwordsList:
    """Тесты просмотра списка стоп-слов"""
    
    @pytest.mark.asyncio
    async def test_show_stopwords_list_with_words(self):
        """Тест отображения списка стоп-слов"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "📋 Просмотр стоп-слов")
        
        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager(
            ["спам", "реклама", "продажа", "купить", "мошенники"]
        )
        mock_stopwords_manager.get_paginated_stopwords = Mock(return_value={
            'words': ["спам", "реклама", "продажа"],
            'current_page': 1,
            'total_pages': 2,
            'total_words': 5,
            'has_next': True,
            'has_prev': False
        })
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
                with patch('src.bot.handlers.stopwords_management.stopwords_pagination_keyboard') as mock_keyboard:
                    mock_keyboard.return_value = Mock()
                    
                    async def mock_show_stopwords_list(message: Message):
                        user_id = message.from_user.id
                        
                        if not env_config.is_admin(user_id):
                            await message.answer("❌ Доступ запрещен.")
                            return
                        
                        page_data = mock_stopwords_manager.get_paginated_stopwords(1, 10)
                        
                        if not page_data['words']:
                            await message.answer("📋 **Список стоп-слов пуст**")
                            return
                        
                        header_text = (
                            f"📋 **Список стоп-слов**\n\n"
                            f"📊 **Страница {page_data['current_page']} из {page_data['total_pages']}**\n"
                            f"📈 **Всего стоп-слов: {page_data['total_words']}**\n\n"
                        )
                        
                        words_text = ""
                        for i, word in enumerate(page_data['words'], start=1):
                            words_text += f"{i}. `{word}`\n"
                        
                        full_text = header_text + words_text
                        
                        await message.answer(
                            full_text,
                            parse_mode='Markdown',
                            reply_markup=mock_keyboard(1, page_data['total_pages'], page_data['has_prev'], page_data['has_next'])
                        )
                    
                    await mock_show_stopwords_list(message)
                    
                    # Проверяем что пагинация была вызвана
                    mock_stopwords_manager.get_paginated_stopwords.assert_called_once_with(1, 10)
                    
                    # Проверяем ответ
                    message.answer.assert_called_once()
                    call_args = message.answer.call_args
                    response_text = call_args[0][0]
                    
                    assert "📋 **Список стоп-слов**" in response_text
                    assert "Страница 1 из 2" in response_text
                    assert "Всего стоп-слов: 5" in response_text
                    assert "1. `спам`" in response_text
                    assert "2. `реклама`" in response_text
                    assert "3. `продажа`" in response_text
    
    @pytest.mark.asyncio
    async def test_show_stopwords_list_empty(self):
        """Тест отображения пустого списка стоп-слов"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "📋 Просмотр стоп-слов")
        
        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager([])
        mock_stopwords_manager.get_paginated_stopwords = Mock(return_value={
            'words': [],
            'current_page': 1,
            'total_pages': 0,
            'total_words': 0,
            'has_next': False,
            'has_prev': False
        })
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
                async def mock_show_stopwords_list(message: Message):
                    page_data = mock_stopwords_manager.get_paginated_stopwords(1, 10)
                    
                    if not page_data['words']:
                        await message.answer("📋 **Список стоп-слов пуст**\n\nДобавьте первое стоп-слово для начала работы.")
                        return
                    
                    await message.answer("Has words")
                
                await mock_show_stopwords_list(message)
                
                # Проверяем ответ для пустого списка
                message.answer.assert_called_once()
                call_args = message.answer.call_args
                response_text = call_args[0][0]
                
                assert "📋 **Список стоп-слов пуст**" in response_text
                assert "Добавьте первое стоп-слово" in response_text


class TestStartAddStopword:
    """Тесты начала добавления стоп-слова"""
    
    @pytest.mark.asyncio
    async def test_start_add_stopword_admin_access(self):
        """Тест доступа администратора к добавлению стоп-слова"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "➕ Добавить стоп-слово")
        state = MockFactory.create_fsm_context()
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch('src.bot.handlers.stopwords_management.cancel_operation_keyboard') as mock_keyboard:
                mock_keyboard.return_value = Mock()
                
                async def mock_start_add_stopword(message: Message, state: FSMContext):
                    user_id = message.from_user.id
                    
                    if not env_config.is_admin(user_id):
                        await message.answer("❌ Доступ запрещен.")
                        return
                    
                    await state.set_state(StopWordsManagement.waiting_for_new_stopword)
                    
                    add_instructions = (
                        f"➕ **Добавление нового стоп-слова**\n\n"
                        f"📝 **Введите стоп-слово для добавления**\n\n"
                        f"📋 **Требования к стоп-слову:**\n"
                        f"• Длина: от 2 до 50 символов\n"
                        f"• Только буквы, цифры, дефисы и подчеркивания\n"
                        f"• Автоматическое приведение к нижнему регистру\n"
                        f"• Проверка на дубликаты\n\n"
                        f"✅ **Примеры валидных стоп-слов:**\n"
                        f"• `спам`\n"
                        f"• `реклама`\n"
                        f"• `test_word`\n"
                        f"• `слово-123`\n\n"
                        f"❌ Для отмены используйте кнопку ниже"
                    )
                    
                    await message.answer(
                        add_instructions,
                        parse_mode='Markdown',
                        reply_markup=mock_keyboard()
                    )
                
                await mock_start_add_stopword(message, state)
                
                # Проверяем что состояние было установлено
                state.set_state.assert_called_once_with(StopWordsManagement.waiting_for_new_stopword)
                
                # Проверяем ответ
                message.answer.assert_called_once()
                call_args = message.answer.call_args
                response_text = call_args[0][0]
                
                assert "➕ **Добавление нового стоп-слова**" in response_text
                assert "Требования к стоп-слову" in response_text
                assert "Длина: от 2 до 50 символов" in response_text
                assert "Примеры валидных стоп-слов" in response_text
                
                # Проверяем параметры
                call_kwargs = call_args[1]
                assert call_kwargs['parse_mode'] == 'Markdown'
                assert 'reply_markup' in call_kwargs
    
    @pytest.mark.asyncio
    async def test_start_add_stopword_regular_user_denied(self):
        """Тест запрета доступа обычному пользователю"""
        user_data = create_regular_user()
        message = MockFactory.create_message(user_data, "➕ Добавить стоп-слово")
        state = MockFactory.create_fsm_context()
        
        with patch.object(env_config, 'is_admin', return_value=False):
            async def mock_start_add_stopword(message: Message, state: FSMContext):
                user_id = message.from_user.id
                
                if not env_config.is_admin(user_id):
                    await message.answer("❌ Доступ запрещен.")
                    return
                
                await message.answer("Admin content")
            
            await mock_start_add_stopword(message, state)
            
            # Проверяем что доступ был запрещен
            message.answer.assert_called_once_with("❌ Доступ запрещен.")
            
            # Состояние не должно было измениться
            state.set_state.assert_not_called()


class TestProcessNewStopword:
    """Тесты FSM обработчика добавления нового стоп-слова"""

    @pytest.mark.asyncio
    async def test_process_new_stopword_valid_word(self):
        """Тест добавления валидного стоп-слова"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "новое_слово")
        state = MockFactory.create_fsm_context()

        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager()
        mock_validator = ValidationMockFactory.create_validator_mock()
        mock_validator.validate_stopword.return_value = ValidationMockFactory.create_validation_result(
            is_valid=True,
            normalized_data="новое_слово"
        )

        with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
            with patch('src.bot.handlers.stopwords_management.validator', mock_validator):
                with patch('src.bot.handlers.stopwords_management.back_to_stopwords_keyboard') as mock_keyboard:
                    mock_keyboard.return_value = Mock()

                    async def mock_process_new_stopword(message: Message, state: FSMContext):
                        word_input = message.text.strip()

                        # Валидация стоп-слова
                        validation = mock_validator.validate_stopword(word_input)

                        if not validation.is_valid:
                            error_text = "❌ **Ошибка валидации:**\n\n" + "\n".join(validation.errors)
                            await message.answer(error_text, parse_mode='Markdown')
                            return

                        # Добавление стоп-слова
                        success = mock_stopwords_manager.add_stopword(validation.normalized_data)

                        if success:
                            success_text = (
                                f"✅ **Стоп-слово добавлено успешно**\n\n"
                                f"📝 Добавлено: `{validation.normalized_data}`\n"
                                f"📊 Всего стоп-слов: {len(mock_stopwords_manager.stopwords)}"
                            )
                            await message.answer(
                                success_text,
                                parse_mode='Markdown',
                                reply_markup=mock_keyboard()
                            )
                            await state.clear()
                        else:
                            await message.answer("❌ Ошибка добавления стоп-слова")

                    await mock_process_new_stopword(message, state)

                    # Проверяем что валидация была вызвана
                    mock_validator.validate_stopword.assert_called_once_with("новое_слово")

                    # Проверяем что стоп-слово было добавлено
                    mock_stopwords_manager.add_stopword.assert_called_once_with("новое_слово")

                    # Проверяем ответ
                    message.answer.assert_called_once()
                    call_args = message.answer.call_args
                    response_text = call_args[0][0]

                    assert "✅ **Стоп-слово добавлено успешно**" in response_text
                    assert "Добавлено: `новое_слово`" in response_text
                    assert "Всего стоп-слов:" in response_text

                    # Проверяем что состояние было очищено
                    state.clear.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_new_stopword_invalid_word(self):
        """Тест добавления невалидного стоп-слова"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "слово с пробелами")
        state = MockFactory.create_fsm_context()

        mock_validator = ValidationMockFactory.create_validator_mock()
        mock_validator.validate_stopword.return_value = ValidationMockFactory.create_validation_result(
            is_valid=False,
            errors=["Стоп-слово содержит недопустимые символы", "Пробелы не разрешены"]
        )

        with patch('src.bot.handlers.stopwords_management.validator', mock_validator):
            async def mock_process_new_stopword(message: Message, state: FSMContext):
                word_input = message.text.strip()

                # Валидация стоп-слова
                validation = mock_validator.validate_stopword(word_input)

                if not validation.is_valid:
                    error_text = "❌ **Ошибка валидации:**\n\n" + "\n".join(validation.errors)
                    await message.answer(error_text, parse_mode='Markdown')
                    return

                await message.answer("Success")

            await mock_process_new_stopword(message, state)

            # Проверяем что валидация была вызвана
            mock_validator.validate_stopword.assert_called_once_with("слово с пробелами")

            # Проверяем ответ об ошибке
            message.answer.assert_called_once()
            call_args = message.answer.call_args
            response_text = call_args[0][0]

            assert "❌ **Ошибка валидации:**" in response_text
            assert "недопустимые символы" in response_text
            assert "Пробелы не разрешены" in response_text

            # Состояние не должно было очиститься
            state.clear.assert_not_called()

    @pytest.mark.asyncio
    async def test_process_new_stopword_duplicate(self):
        """Тест добавления дублирующегося стоп-слова"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "спам")
        state = MockFactory.create_fsm_context()

        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager(["спам", "реклама"])
        mock_stopwords_manager.add_stopword.return_value = False  # Дубликат

        mock_validator = ValidationMockFactory.create_validator_mock()
        mock_validator.validate_stopword.return_value = ValidationMockFactory.create_validation_result(
            is_valid=True,
            normalized_data="спам"
        )

        with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
            with patch('src.bot.handlers.stopwords_management.validator', mock_validator):
                async def mock_process_new_stopword(message: Message, state: FSMContext):
                    word_input = message.text.strip()

                    validation = mock_validator.validate_stopword(word_input)

                    if validation.is_valid:
                        success = mock_stopwords_manager.add_stopword(validation.normalized_data)

                        if success:
                            await message.answer("Success")
                        else:
                            await message.answer(
                                f"❌ **Стоп-слово уже существует**\n\n"
                                f"Слово `{validation.normalized_data}` уже есть в списке стоп-слов.",
                                parse_mode='Markdown'
                            )

                await mock_process_new_stopword(message, state)

                # Проверяем ответ о дубликате
                message.answer.assert_called_once()
                call_args = message.answer.call_args
                response_text = call_args[0][0]

                assert "❌ **Стоп-слово уже существует**" in response_text
                assert "Слово `спам` уже есть" in response_text

    @pytest.mark.parametrize("word", VALID_STOPWORDS[:5])
    @pytest.mark.asyncio
    async def test_process_new_stopword_parametrized_valid(self, word):
        """Параметризованный тест валидных стоп-слов"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, word)
        state = MockFactory.create_fsm_context()

        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager()
        mock_validator = ValidationMockFactory.create_validator_mock()
        mock_validator.validate_stopword.return_value = ValidationMockFactory.create_validation_result(
            is_valid=True,
            normalized_data=word.lower()
        )

        with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
            with patch('src.bot.handlers.stopwords_management.validator', mock_validator):
                async def mock_process_new_stopword(message: Message, state: FSMContext):
                    word_input = message.text.strip()
                    validation = mock_validator.validate_stopword(word_input)

                    if validation.is_valid:
                        mock_stopwords_manager.add_stopword(validation.normalized_data)
                        await message.answer("Success")

                await mock_process_new_stopword(message, state)

                # Проверяем что слово было обработано
                mock_validator.validate_stopword.assert_called_once_with(word)
                mock_stopwords_manager.add_stopword.assert_called_once_with(word.lower())

    @pytest.mark.parametrize("word", INVALID_STOPWORDS[:5])
    @pytest.mark.asyncio
    async def test_process_new_stopword_parametrized_invalid(self, word):
        """Параметризованный тест невалидных стоп-слов"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, word)
        state = MockFactory.create_fsm_context()

        mock_validator = ValidationMockFactory.create_validator_mock()
        mock_validator.validate_stopword.return_value = ValidationMockFactory.create_validation_result(
            is_valid=False,
            errors=["Невалидное стоп-слово"]
        )

        with patch('src.bot.handlers.stopwords_management.validator', mock_validator):
            async def mock_process_new_stopword(message: Message, state: FSMContext):
                word_input = message.text.strip()
                validation = mock_validator.validate_stopword(word_input)

                if not validation.is_valid:
                    await message.answer("❌ Ошибка валидации")
                    return

                await message.answer("Success")

            await mock_process_new_stopword(message, state)

            # Проверяем что ошибка была обработана
            message.answer.assert_called_once_with("❌ Ошибка валидации")


class TestStartSearchStopwords:
    """Тесты начала поиска стоп-слов"""

    @pytest.mark.asyncio
    async def test_start_search_stopwords_admin_access(self):
        """Тест доступа администратора к поиску стоп-слов"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "🔍 Поиск стоп-слов")
        state = MockFactory.create_fsm_context()

        with patch.object(env_config, 'is_admin', return_value=True):
            with patch('src.bot.handlers.stopwords_management.cancel_operation_keyboard') as mock_keyboard:
                mock_keyboard.return_value = Mock()

                async def mock_start_search_stopwords(message: Message, state: FSMContext):
                    user_id = message.from_user.id

                    if not env_config.is_admin(user_id):
                        await message.answer("❌ Доступ запрещен.")
                        return

                    await state.set_state(StopWordsManagement.waiting_for_search_query)

                    search_instructions = (
                        f"🔍 **Поиск стоп-слов**\n\n"
                        f"📝 **Введите поисковый запрос**\n\n"
                        f"🔎 **Возможности поиска:**\n"
                        f"• Поиск по подстроке в любой части слова\n"
                        f"• Регистронезависимый поиск\n"
                        f"• Поддержка частичных совпадений\n\n"
                        f"📋 **Примеры запросов:**\n"
                        f"• `спам` - найдет 'спам', 'спамить', 'антиспам'\n"
                        f"• `рекл` - найдет 'реклама', 'рекламщик'\n"
                        f"• `123` - найдет все слова содержащие цифры\n\n"
                        f"❌ Для отмены используйте кнопку ниже"
                    )

                    await message.answer(
                        search_instructions,
                        parse_mode='Markdown',
                        reply_markup=mock_keyboard()
                    )

                await mock_start_search_stopwords(message, state)

                # Проверяем что состояние было установлено
                state.set_state.assert_called_once_with(StopWordsManagement.waiting_for_search_query)

                # Проверяем ответ
                message.answer.assert_called_once()
                call_args = message.answer.call_args
                response_text = call_args[0][0]

                assert "🔍 **Поиск стоп-слов**" in response_text
                assert "Введите поисковый запрос" in response_text
                assert "Возможности поиска" in response_text
                assert "Примеры запросов" in response_text


class TestProcessSearchQuery:
    """Тесты FSM обработчика поиска стоп-слов"""

    @pytest.mark.asyncio
    async def test_process_search_query_found_results(self):
        """Тест поиска с найденными результатами"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "спам")
        state = MockFactory.create_fsm_context()

        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager()
        mock_stopwords_manager.search_stopwords.return_value = ["спам", "спамить", "антиспам"]

        with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
            with patch('src.bot.handlers.stopwords_management.search_results_keyboard') as mock_keyboard:
                mock_keyboard.return_value = Mock()

                async def mock_process_search_query(message: Message, state: FSMContext):
                    query = message.text.strip()

                    if not query:
                        await message.answer("❌ Поисковый запрос не может быть пустым")
                        return

                    # Выполнение поиска
                    results = mock_stopwords_manager.search_stopwords(query)

                    if not results:
                        await message.answer(f"🔍 По запросу `{query}` ничего не найдено")
                        return

                    # Формирование результатов
                    results_text = (
                        f"🔍 **Результаты поиска**\n\n"
                        f"📝 Запрос: `{query}`\n"
                        f"📊 Найдено: **{len(results)}** стоп-слов\n\n"
                    )

                    for i, word in enumerate(results, 1):
                        results_text += f"{i}. `{word}`\n"

                    await message.answer(
                        results_text,
                        parse_mode='Markdown',
                        reply_markup=mock_keyboard()
                    )

                    await state.clear()

                await mock_process_search_query(message, state)

                # Проверяем что поиск был выполнен
                mock_stopwords_manager.search_stopwords.assert_called_once_with("спам")

                # Проверяем ответ
                message.answer.assert_called_once()
                call_args = message.answer.call_args
                response_text = call_args[0][0]

                assert "🔍 **Результаты поиска**" in response_text
                assert "Запрос: `спам`" in response_text
                assert "Найдено: **3** стоп-слов" in response_text
                assert "1. `спам`" in response_text
                assert "2. `спамить`" in response_text
                assert "3. `антиспам`" in response_text

                # Проверяем что состояние было очищено
                state.clear.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_search_query_no_results(self):
        """Тест поиска без результатов"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "несуществующее_слово")
        state = MockFactory.create_fsm_context()

        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager()
        mock_stopwords_manager.search_stopwords.return_value = []

        with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
            async def mock_process_search_query(message: Message, state: FSMContext):
                query = message.text.strip()
                results = mock_stopwords_manager.search_stopwords(query)

                if not results:
                    await message.answer(f"🔍 По запросу `{query}` ничего не найдено")
                    return

                await message.answer("Found results")

            await mock_process_search_query(message, state)

            # Проверяем ответ
            message.answer.assert_called_once()
            call_args = message.answer.call_args
            response_text = call_args[0][0]

            assert "🔍 По запросу `несуществующее_слово` ничего не найдено" in response_text

    @pytest.mark.asyncio
    async def test_process_search_query_empty_query(self):
        """Тест поиска с пустым запросом"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "   ")
        state = MockFactory.create_fsm_context()

        async def mock_process_search_query(message: Message, state: FSMContext):
            query = message.text.strip()

            if not query:
                await message.answer("❌ Поисковый запрос не может быть пустым")
                return

            await message.answer("Processing query")

        await mock_process_search_query(message, state)

        # Проверяем ответ об ошибке
        message.answer.assert_called_once_with("❌ Поисковый запрос не может быть пустым")


class TestHandleStopwordsPagination:
    """Тесты callback-обработчика пагинации стоп-слов"""

    @pytest.mark.asyncio
    async def test_handle_pagination_next_page(self):
        """Тест перехода на следующую страницу"""
        admin_data = create_admin_user()
        callback = MockFactory.create_callback_query(admin_data, "sw_page:2")

        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager()
        mock_stopwords_manager.get_paginated_stopwords.return_value = {
            'words': ["слово4", "слово5", "слово6"],
            'current_page': 2,
            'total_pages': 3,
            'total_words': 8,
            'has_next': True,
            'has_prev': True
        }

        with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
            with patch('src.bot.handlers.stopwords_management.stopwords_pagination_keyboard') as mock_keyboard:
                mock_keyboard.return_value = Mock()

                async def mock_handle_pagination(callback: CallbackQuery):
                    # Извлечение номера страницы из callback_data
                    page = int(callback.data.split(':')[1])

                    # Получение данных страницы
                    page_data = mock_stopwords_manager.get_paginated_stopwords(page, 10)

                    # Формирование текста
                    header_text = (
                        f"📋 **Список стоп-слов**\n\n"
                        f"📊 **Страница {page_data['current_page']} из {page_data['total_pages']}**\n"
                        f"📈 **Всего стоп-слов: {page_data['total_words']}**\n\n"
                    )

                    words_text = ""
                    start_index = (page - 1) * 10
                    for i, word in enumerate(page_data['words'], start=start_index + 1):
                        words_text += f"{i}. `{word}`\n"

                    full_text = header_text + words_text

                    await callback.message.edit_text(
                        full_text,
                        parse_mode='Markdown',
                        reply_markup=mock_keyboard(page, page_data['total_pages'], page_data['has_prev'], page_data['has_next'])
                    )

                    await callback.answer()

                await mock_handle_pagination(callback)

                # Проверяем что данные страницы были получены
                mock_stopwords_manager.get_paginated_stopwords.assert_called_once_with(2, 10)

                # Проверяем что сообщение было отредактировано
                callback.message.edit_text.assert_called_once()
                call_args = callback.message.edit_text.call_args
                response_text = call_args[0][0]

                assert "Страница 2 из 3" in response_text
                assert "Всего стоп-слов: 8" in response_text
                assert "4. `слово4`" in response_text
                assert "5. `слово5`" in response_text
                assert "6. `слово6`" in response_text

                # Проверяем что callback был отвечен
                callback.answer.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_pagination_invalid_page(self):
        """Тест обработки невалидного номера страницы"""
        admin_data = create_admin_user()
        callback = MockFactory.create_callback_query(admin_data, "sw_page:invalid")

        async def mock_handle_pagination(callback: CallbackQuery):
            try:
                page = int(callback.data.split(':')[1])
                await callback.answer("Success")
            except (ValueError, IndexError):
                await callback.answer("❌ Ошибка: неверный номер страницы", show_alert=True)

        await mock_handle_pagination(callback)

        # Проверяем что ошибка была обработана
        callback.answer.assert_called_once_with("❌ Ошибка: неверный номер страницы", show_alert=True)


class TestHandleStopwordActions:
    """Тесты callback-обработчика действий со стоп-словами"""

    @pytest.mark.asyncio
    async def test_handle_stopword_delete_action(self):
        """Тест действия удаления стоп-слова"""
        admin_data = create_admin_user()
        callback = MockFactory.create_callback_query(admin_data, "sw_action:delete:спам")

        with patch('src.bot.handlers.stopwords_management.delete_confirmation_keyboard') as mock_keyboard:
            mock_keyboard.return_value = Mock()

            async def mock_handle_stopword_actions(callback: CallbackQuery):
                # Парсинг callback_data
                parts = callback.data.split(':')
                action = parts[1]
                word = parts[2] if len(parts) > 2 else ""

                if action == "delete":
                    confirm_text = (
                        f"❓ **Подтверждение удаления**\n\n"
                        f"Вы действительно хотите удалить стоп-слово `{word}`?\n\n"
                        f"⚠️ **Внимание:** Это действие нельзя отменить!"
                    )

                    await callback.message.edit_text(
                        confirm_text,
                        parse_mode='Markdown',
                        reply_markup=mock_keyboard(word)
                    )

                await callback.answer()

            await mock_handle_stopword_actions(callback)

            # Проверяем что сообщение было отредактировано
            callback.message.edit_text.assert_called_once()
            call_args = callback.message.edit_text.call_args
            response_text = call_args[0][0]

            assert "❓ **Подтверждение удаления**" in response_text
            assert "удалить стоп-слово `спам`" in response_text
            assert "⚠️ **Внимание:**" in response_text

            # Проверяем что клавиатура была создана
            mock_keyboard.assert_called_once_with("спам")

            # Проверяем что callback был отвечен
            callback.answer.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_stopword_actions_malformed_data(self):
        """Тест обработки некорректных данных callback"""
        admin_data = create_admin_user()
        callback = MockFactory.create_callback_query(admin_data, "sw_action:invalid")

        async def mock_handle_stopword_actions(callback: CallbackQuery):
            try:
                parts = callback.data.split(':')
                action = parts[1]
                word = parts[2] if len(parts) > 2 else ""

                if not word:
                    await callback.answer("❌ Ошибка: не указано стоп-слово", show_alert=True)
                    return

                await callback.answer("Success")
            except (IndexError, ValueError):
                await callback.answer("❌ Ошибка обработки действия", show_alert=True)

        await mock_handle_stopword_actions(callback)

        # Проверяем что ошибка была обработана
        callback.answer.assert_called_once_with("❌ Ошибка: не указано стоп-слово", show_alert=True)


class TestHandleDeleteConfirmation:
    """Тесты callback-обработчика подтверждения удаления"""

    @pytest.mark.asyncio
    async def test_handle_delete_confirmation_yes(self):
        """Тест подтверждения удаления стоп-слова"""
        admin_data = create_admin_user()
        callback = MockFactory.create_callback_query(admin_data, "sw_delete:yes:спам")

        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager(["спам", "реклама"])
        mock_stopwords_manager.remove_stopword.return_value = True

        with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
            with patch('src.bot.handlers.stopwords_management.back_to_stopwords_keyboard') as mock_keyboard:
                mock_keyboard.return_value = Mock()

                async def mock_handle_delete_confirmation(callback: CallbackQuery):
                    parts = callback.data.split(':')
                    action = parts[1]
                    word = parts[2] if len(parts) > 2 else ""

                    if action == "yes":
                        success = mock_stopwords_manager.remove_stopword(word)

                        if success:
                            success_text = (
                                f"✅ **Стоп-слово удалено**\n\n"
                                f"🗑️ Удалено: `{word}`\n"
                                f"📊 Осталось стоп-слов: {len(mock_stopwords_manager.stopwords)}"
                            )

                            await callback.message.edit_text(
                                success_text,
                                parse_mode='Markdown',
                                reply_markup=mock_keyboard()
                            )
                        else:
                            await callback.message.edit_text("❌ Ошибка удаления стоп-слова")

                    await callback.answer()

                await mock_handle_delete_confirmation(callback)

                # Проверяем что стоп-слово было удалено
                mock_stopwords_manager.remove_stopword.assert_called_once_with("спам")

                # Проверяем что сообщение было отредактировано
                callback.message.edit_text.assert_called_once()
                call_args = callback.message.edit_text.call_args
                response_text = call_args[0][0]

                assert "✅ **Стоп-слово удалено**" in response_text
                assert "Удалено: `спам`" in response_text
                assert "Осталось стоп-слов:" in response_text

                # Проверяем что callback был отвечен
                callback.answer.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_delete_confirmation_no(self):
        """Тест отмены удаления стоп-слова"""
        admin_data = create_admin_user()
        callback = MockFactory.create_callback_query(admin_data, "sw_delete:no:спам")

        with patch('src.bot.handlers.stopwords_management.back_to_stopwords_keyboard') as mock_keyboard:
            mock_keyboard.return_value = Mock()

            async def mock_handle_delete_confirmation(callback: CallbackQuery):
                parts = callback.data.split(':')
                action = parts[1]

                if action == "no":
                    await callback.message.edit_text(
                        "❌ **Удаление отменено**\n\nСтоп-слово не было удалено.",
                        parse_mode='Markdown',
                        reply_markup=mock_keyboard()
                    )

                await callback.answer()

            await mock_handle_delete_confirmation(callback)

            # Проверяем что сообщение было отредактировано
            callback.message.edit_text.assert_called_once()
            call_args = callback.message.edit_text.call_args
            response_text = call_args[0][0]

            assert "❌ **Удаление отменено**" in response_text
            assert "не было удалено" in response_text


class TestProcessImportFile:
    """Тесты FSM обработчика импорта файла"""

    @pytest.mark.asyncio
    async def test_process_import_file_valid_document(self):
        """Тест импорта валидного документа"""
        admin_data = create_admin_user()

        # Создаем мок документа
        document = MockFactory.create_document("stopwords.txt", 1024, "text/plain")
        message = MockFactory.create_message(admin_data, "")
        message.document = document

        state = MockFactory.create_fsm_context()

        mock_validator = ValidationMockFactory.create_validator_mock()
        mock_validator.validate_import_file.return_value = ValidationMockFactory.create_validation_result(
            is_valid=True,
            normalized_data=["новое_слово1", "новое_слово2"]
        )

        with patch('src.bot.handlers.stopwords_management.validator', mock_validator):
            with patch('src.bot.handlers.stopwords_management.import_confirmation_keyboard') as mock_keyboard:
                mock_keyboard.return_value = Mock()

                # Мокируем загрузку файла
                with patch('aiogram.Bot.get_file') as mock_get_file:
                    with patch('aiogram.Bot.download_file') as mock_download_file:
                        mock_get_file.return_value = Mock(file_path="documents/test.txt")
                        mock_download_file.return_value = BytesIO("новое_слово1\nновое_слово2\n".encode('utf-8'))

                        async def mock_process_import_file(message: Message, state: FSMContext):
                            if not message.document:
                                await message.answer("❌ Пожалуйста, отправьте файл")
                                return

                            # Проверка типа файла
                            if not message.document.file_name.endswith('.txt'):
                                await message.answer("❌ Поддерживаются только .txt файлы")
                                return

                            # Загрузка файла (мокированная)
                            file_content = "новое_слово1\nновое_слово2\n"

                            # Валидация содержимого
                            validation = mock_validator.validate_import_file(file_content)

                            if not validation.is_valid:
                                error_text = "❌ **Ошибка валидации файла:**\n\n" + "\n".join(validation.errors)
                                await message.answer(error_text, parse_mode='Markdown')
                                return

                            # Сохранение данных для подтверждения
                            import_data = {
                                'filename': message.document.file_name,
                                'words': validation.normalized_data,
                                'total_words': len(validation.normalized_data)
                            }

                            await state.update_data(import_data=import_data)

                            preview_text = (
                                f"📥 **Предварительный просмотр импорта**\n\n"
                                f"📄 Файл: `{import_data['filename']}`\n"
                                f"📊 Будет добавлено: **{import_data['total_words']}** стоп-слов\n\n"
                                f"📋 **Первые слова:**\n"
                            )

                            for i, word in enumerate(import_data['words'][:5], 1):
                                preview_text += f"{i}. `{word}`\n"

                            if len(import_data['words']) > 5:
                                preview_text += f"... и еще {len(import_data['words']) - 5} слов\n"

                            preview_text += "\n❓ **Подтвердить импорт?**"

                            await message.answer(
                                preview_text,
                                parse_mode='Markdown',
                                reply_markup=mock_keyboard()
                            )

                        await mock_process_import_file(message, state)

                        # Проверяем что валидация была вызвана
                        mock_validator.validate_import_file.assert_called_once()

                        # Проверяем что данные были сохранены
                        state.update_data.assert_called_once()
                        call_args = state.update_data.call_args[1]
                        import_data = call_args['import_data']

                        assert import_data['filename'] == "stopwords.txt"
                        assert import_data['total_words'] == 2
                        assert "новое_слово1" in import_data['words']
                        assert "новое_слово2" in import_data['words']

                        # Проверяем ответ
                        message.answer.assert_called_once()
                        call_args = message.answer.call_args
                        response_text = call_args[0][0]

                        assert "📥 **Предварительный просмотр импорта**" in response_text
                        assert "Файл: `stopwords.txt`" in response_text
                        assert "Будет добавлено: **2** стоп-слов" in response_text
                        assert "1. `новое_слово1`" in response_text
                        assert "2. `новое_слово2`" in response_text

    @pytest.mark.asyncio
    async def test_process_import_file_no_document(self):
        """Тест обработки сообщения без документа"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "Текст без файла")
        message.document = None
        state = MockFactory.create_fsm_context()

        async def mock_process_import_file(message: Message, state: FSMContext):
            if not message.document:
                await message.answer("❌ Пожалуйста, отправьте файл")
                return

            await message.answer("Processing file")

        await mock_process_import_file(message, state)

        # Проверяем что была отправлена ошибка
        message.answer.assert_called_once_with("❌ Пожалуйста, отправьте файл")

    @pytest.mark.asyncio
    async def test_process_import_file_invalid_extension(self):
        """Тест импорта файла с неверным расширением"""
        admin_data = create_admin_user()

        document = MockFactory.create_document("stopwords.pdf", 1024, "application/pdf")
        message = MockFactory.create_message(admin_data, "")
        message.document = document

        state = MockFactory.create_fsm_context()

        async def mock_process_import_file(message: Message, state: FSMContext):
            if not message.document.file_name.endswith('.txt'):
                await message.answer("❌ Поддерживаются только .txt файлы")
                return

            await message.answer("Processing file")

        await mock_process_import_file(message, state)

        # Проверяем что была отправлена ошибка
        message.answer.assert_called_once_with("❌ Поддерживаются только .txt файлы")


class TestStopwordsHandlersEdgeCases:
    """Тесты граничных случаев для обработчиков стоп-слов"""

    @pytest.mark.asyncio
    async def test_fsm_state_corruption_handling(self):
        """Тест обработки поврежденного состояния FSM"""
        admin_data = create_admin_user()
        message = MockFactory.create_message(admin_data, "тест")

        # Создаем поврежденное состояние
        state = MockFactory.create_fsm_context()
        state.get_data.side_effect = Exception("State corruption")

        async def mock_fsm_handler(message: Message, state: FSMContext):
            try:
                data = await state.get_data()
                await message.answer("Success")
            except Exception:
                await message.answer("❌ Ошибка состояния. Попробуйте начать заново.")
                await state.clear()

        await mock_fsm_handler(message, state)

        # Проверяем что ошибка была обработана
        message.answer.assert_called_once_with("❌ Ошибка состояния. Попробуйте начать заново.")
        state.clear.assert_called_once()

    @pytest.mark.asyncio
    async def test_concurrent_stopword_operations(self):
        """Тест одновременных операций со стоп-словами"""
        import asyncio

        admin_data = create_admin_user()
        messages = [
            MockFactory.create_message(admin_data, f"слово{i}")
            for i in range(3)
        ]
        states = [MockFactory.create_fsm_context() for _ in range(3)]

        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager()
        mock_validator = ValidationMockFactory.create_validator_mock()

        with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
            with patch('src.bot.handlers.stopwords_management.validator', mock_validator):
                async def mock_add_stopword(message: Message, state: FSMContext):
                    # Симулируем небольшую задержку
                    await asyncio.sleep(0.01)

                    word = message.text.strip()
                    mock_stopwords_manager.add_stopword(word)
                    await message.answer(f"Added {word}")

                # Запускаем операции одновременно
                tasks = [
                    mock_add_stopword(msg, state)
                    for msg, state in zip(messages, states)
                ]
                await asyncio.gather(*tasks)

                # Проверяем что все операции выполнились
                assert mock_stopwords_manager.add_stopword.call_count == 3
                for message in messages:
                    message.answer.assert_called_once()

    @pytest.mark.asyncio
    async def test_large_file_import_handling(self):
        """Тест обработки большого файла импорта"""
        admin_data = create_admin_user()

        # Создаем большой файл (превышающий лимиты)
        large_document = MockFactory.create_document("large.txt", 2 * 1024 * 1024, "text/plain")  # 2MB
        message = MockFactory.create_message(admin_data, "")
        message.document = large_document

        state = MockFactory.create_fsm_context()

        async def mock_process_import_file(message: Message, state: FSMContext):
            # Проверка размера файла
            max_size = 1024 * 1024  # 1MB
            if message.document.file_size > max_size:
                await message.answer(
                    f"❌ Файл слишком большой. Максимальный размер: {max_size // 1024} KB"
                )
                return

            await message.answer("Processing file")

        await mock_process_import_file(message, state)

        # Проверяем что была отправлена ошибка о размере
        message.answer.assert_called_once()
        call_args = message.answer.call_args
        response_text = call_args[0][0]

        assert "❌ Файл слишком большой" in response_text
        assert "Максимальный размер:" in response_text

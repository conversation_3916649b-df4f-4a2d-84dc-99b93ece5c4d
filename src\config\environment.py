"""
Управление переменными окружения
"""

import os
from typing import List
from dotenv import load_dotenv
from .settings import ENV_FILE

# Загрузка переменных окружения
load_dotenv(ENV_FILE)

class EnvironmentConfig:
    """Класс для управления переменными окружения"""
    
    def __init__(self):
        self._load_variables()
    
    def _load_variables(self):
        """Загрузка всех переменных окружения"""
        # Telegram Bot настройки
        self.BOT_TOKEN = os.getenv('BOT_TOKEN')
        
        # Telegram API настройки
        self.API_ID = self._get_int_env('API_ID')
        self.API_HASH = os.getenv('API_HASH')
        
        # Администраторы (поддержка нескольких через запятую)
        self.ADMIN_CHAT_IDS = self._parse_admin_ids()
    
    def _get_int_env(self, key: str, default: int = 0) -> int:
        """Получение целочисленной переменной окружения"""
        try:
            return int(os.getenv(key, default))
        except (ValueError, TypeError):
            return default
    
    def _parse_admin_ids(self) -> List[int]:
        """Парсинг ID администраторов"""
        admin_ids_str = os.getenv('ADMIN_CHAT_ID', '0')
        try:
            return [int(id.strip()) for id in admin_ids_str.split(',') if id.strip()]
        except ValueError:
            return [0]
    
    def is_admin(self, user_id: int) -> bool:
        """Проверка является ли пользователь администратором"""
        return user_id in self.ADMIN_CHAT_IDS
    
    def validate(self) -> tuple[bool, List[str]]:
        """Проверка всех необходимых переменных окружения"""
        required_vars = {
            'BOT_TOKEN': self.BOT_TOKEN,
            'API_ID': self.API_ID,
            'API_HASH': self.API_HASH,
            'ADMIN_CHAT_ID': len(self.ADMIN_CHAT_IDS) > 0 and self.ADMIN_CHAT_IDS[0] != 0
        }
        
        missing_vars = []
        for var_name, var_value in required_vars.items():
            if not var_value:
                missing_vars.append(var_name)
        
        return len(missing_vars) == 0, missing_vars

# Глобальный экземпляр конфигурации
env_config = EnvironmentConfig()

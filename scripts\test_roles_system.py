#!/usr/bin/env python3
"""
Тест новой системы ролей
"""

import sys
from pathlib import Path

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.roles_manager import roles_manager
from src.config.environment import env_config

def test_roles_system():
    """Тест системы ролей"""
    print("🧪 Тестирование новой системы ролей")
    print("=" * 60)
    
    # Тестовые данные
    test_admin_id = 1290467190  # Из .env
    test_user_id = 999888777   # Тестовый пользователь
    test_teamlead_id = 555444333  # Тестовый тимлид
    
    print(f"👨‍💼 Тестовый администратор: {test_admin_id}")
    print(f"👤 Тестовый пользователь: {test_user_id}")
    print(f"👥 Тестовый тимлид: {test_teamlead_id}")
    
    # Тест 1: Проверка администраторов из .env
    print(f"\n1️⃣ Проверка администраторов из .env...")
    
    admin_ids = env_config.ADMIN_CHAT_IDS
    print(f"   Администраторы из .env: {admin_ids}")
    
    for admin_id in admin_ids:
        role = roles_manager.get_user_role(admin_id)
        is_admin = roles_manager.is_admin(admin_id)
        has_access = roles_manager.has_access(admin_id)
        
        print(f"   • {admin_id}: роль={role}, админ={is_admin}, доступ={has_access}")
        
        # Проверяем корректность
        assert role == "admin", f"Администратор {admin_id} должен иметь роль 'admin'"
        assert is_admin == True, f"is_admin должен возвращать True для {admin_id}"
        assert has_access == True, f"has_access должен возвращать True для {admin_id}"
    
    print(f"   ✅ Все администраторы из .env работают корректно")
    
    # Тест 2: Проверка пользователя без роли
    print(f"\n2️⃣ Проверка пользователя без роли...")
    
    role = roles_manager.get_user_role(test_user_id)
    is_admin = roles_manager.is_admin(test_user_id)
    is_teamlead = roles_manager.is_teamlead(test_user_id)
    has_access = roles_manager.has_access(test_user_id)
    
    print(f"   Пользователь {test_user_id}:")
    print(f"   • Роль: {role}")
    print(f"   • Админ: {is_admin}")
    print(f"   • Тимлид: {is_teamlead}")
    print(f"   • Доступ: {has_access}")
    
    # Проверяем корректность
    assert role == "none", f"Новый пользователь должен иметь роль 'none'"
    assert is_admin == False, f"is_admin должен возвращать False для нового пользователя"
    assert is_teamlead == False, f"is_teamlead должен возвращать False для нового пользователя"
    assert has_access == False, f"has_access должен возвращать False для нового пользователя"
    
    print(f"   ✅ Пользователь без роли работает корректно")
    
    # Тест 3: Назначение роли тимлида
    print(f"\n3️⃣ Назначение роли тимлида...")
    
    success = roles_manager.set_user_role(test_teamlead_id, "teamlead", test_admin_id)
    print(f"   Назначение роли тимлида: {'✅ успешно' if success else '❌ ошибка'}")
    
    if success:
        # Проверяем назначенную роль
        role = roles_manager.get_user_role(test_teamlead_id)
        is_admin = roles_manager.is_admin(test_teamlead_id)
        is_teamlead = roles_manager.is_teamlead(test_teamlead_id)
        has_access = roles_manager.has_access(test_teamlead_id)
        
        print(f"   Тимлид {test_teamlead_id}:")
        print(f"   • Роль: {role}")
        print(f"   • Админ: {is_admin}")
        print(f"   • Тимлид: {is_teamlead}")
        print(f"   • Доступ: {has_access}")
        
        # Проверяем корректность
        assert role == "teamlead", f"Назначенный тимлид должен иметь роль 'teamlead'"
        assert is_admin == False, f"is_admin должен возвращать False для тимлида"
        assert is_teamlead == True, f"is_teamlead должен возвращать True для тимлида"
        assert has_access == True, f"has_access должен возвращать True для тимлида"
        
        print(f"   ✅ Роль тимлида назначена корректно")
    
    # Тест 4: Попытка назначения роли не-администратором
    print(f"\n4️⃣ Попытка назначения роли не-администратором...")
    
    # Пытаемся назначить роль от имени обычного пользователя
    success = roles_manager.set_user_role(test_user_id, "teamlead", test_user_id)
    print(f"   Назначение роли не-администратором: {'❌ разрешено (ошибка!)' if success else '✅ запрещено'}")
    
    assert success == False, "Не-администратор не должен иметь возможность назначать роли"
    
    # Тест 5: Получение пользователей по ролям
    print(f"\n5️⃣ Получение пользователей по ролям...")
    
    admins = roles_manager.get_users_by_role("admin")
    teamleads = roles_manager.get_users_by_role("teamlead")
    none_users = roles_manager.get_users_by_role("none")
    
    print(f"   Администраторы: {admins}")
    print(f"   Тимлиды: {teamleads}")
    print(f"   Без роли: {none_users}")
    
    # Проверяем, что администраторы из .env присутствуют
    for admin_id in env_config.ADMIN_CHAT_IDS:
        assert admin_id in admins, f"Администратор {admin_id} должен быть в списке администраторов"
    
    # Проверяем, что назначенный тимлид присутствует
    if success:  # Если назначение прошло успешно
        assert test_teamlead_id in teamleads, f"Назначенный тимлид {test_teamlead_id} должен быть в списке тимлидов"
    
    print(f"   ✅ Получение пользователей по ролям работает корректно")
    
    # Тест 6: Получение всех пользователей с ролями
    print(f"\n6️⃣ Получение всех пользователей с ролями...")
    
    all_users = roles_manager.get_all_users_with_roles()
    print(f"   Всего пользователей с ролями: {len(all_users)}")
    
    for user_id, user_data in all_users.items():
        role = user_data.get('role')
        source = user_data.get('source', 'config')
        print(f"   • {user_id}: {role} (источник: {source})")
    
    # Проверяем, что администраторы из .env присутствуют
    for admin_id in env_config.ADMIN_CHAT_IDS:
        assert str(admin_id) in all_users, f"Администратор {admin_id} должен быть в общем списке"
        assert all_users[str(admin_id)]['role'] == 'admin', f"Администратор {admin_id} должен иметь роль admin"
    
    print(f"   ✅ Получение всех пользователей работает корректно")
    
    # Тест 7: Статистика
    print(f"\n7️⃣ Статистика ролей...")
    
    stats = roles_manager.get_statistics()
    print(f"   Статистика:")
    print(f"   • Всего администраторов: {stats.get('total_admins', 0)}")
    print(f"   • Всего тимлидов: {stats.get('total_teamleads', 0)}")
    print(f"   • Всего пользователей с доступом: {stats.get('total_users_with_access', 0)}")
    print(f"   • Последнее обновление: {stats.get('last_updated', 'Неизвестно')}")
    print(f"   • Обновлено пользователем: {stats.get('updated_by', 'Неизвестно')}")
    
    # Проверяем корректность статистики
    expected_admins = len(env_config.ADMIN_CHAT_IDS)
    # На этом этапе тимлид уже назначен (success=True из теста 3)
    expected_teamleads = 1
    expected_total = expected_admins + expected_teamleads
    
    assert stats['total_admins'] == expected_admins, f"Количество администраторов должно быть {expected_admins}"
    assert stats['total_teamleads'] == expected_teamleads, f"Количество тимлидов должно быть {expected_teamleads}"
    assert stats['total_users_with_access'] == expected_total, f"Общее количество должно быть {expected_total}"
    
    print(f"   ✅ Статистика корректна")
    
    # Тест 8: Удаление роли
    print(f"\n8️⃣ Удаление роли...")
    
    if success:  # Если роль была назначена
        remove_success = roles_manager.remove_user_role(test_teamlead_id, test_admin_id)
        print(f"   Удаление роли тимлида: {'✅ успешно' if remove_success else '❌ ошибка'}")
        
        if remove_success:
            # Проверяем, что роль удалена
            role = roles_manager.get_user_role(test_teamlead_id)
            is_teamlead = roles_manager.is_teamlead(test_teamlead_id)
            has_access = roles_manager.has_access(test_teamlead_id)
            
            print(f"   После удаления роли:")
            print(f"   • Роль: {role}")
            print(f"   • Тимлид: {is_teamlead}")
            print(f"   • Доступ: {has_access}")
            
            assert role == "none", "После удаления роли должна быть 'none'"
            assert is_teamlead == False, "is_teamlead должен возвращать False после удаления роли"
            assert has_access == False, "has_access должен возвращать False после удаления роли"
            
            print(f"   ✅ Роль удалена корректно")
    
    # Тест 9: Валидация некорректных ролей
    print(f"\n9️⃣ Валидация некорректных ролей...")
    
    invalid_role_success = roles_manager.set_user_role(test_user_id, "invalid_role", test_admin_id)
    print(f"   Назначение некорректной роли: {'❌ разрешено (ошибка!)' if invalid_role_success else '✅ запрещено'}")
    
    assert invalid_role_success == False, "Некорректная роль не должна быть назначена"
    
    print(f"   ✅ Валидация ролей работает корректно")
    
    # Итоги
    print(f"\n" + "=" * 60)
    print(f"🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
    print(f"=" * 60)
    
    final_stats = roles_manager.get_statistics()
    print(f"\n📊 Финальная статистика:")
    print(f"• Администраторов: {final_stats.get('total_admins', 0)}")
    print(f"• Тимлидов: {final_stats.get('total_teamleads', 0)}")
    print(f"• Всего с доступом: {final_stats.get('total_users_with_access', 0)}")
    
    print(f"\n✅ Система ролей работает корректно и готова к использованию!")
    
    return True

if __name__ == "__main__":
    try:
        success = test_roles_system()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ ОШИБКА ТЕСТИРОВАНИЯ: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

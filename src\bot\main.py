"""
Основной модуль Telegram бота
"""

import asyncio
from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.fsm.storage.memory import MemoryStorage

from src.config.environment import env_config
from src.monitoring.telethon_manager import TelethonManager
from src.monitoring.response_tracker import ResponseTracker
from src.utils.logging import setup_logging
from .handlers import register_handlers
from .alerts import AlertManager
from .middleware import MessageUsersMiddleware, CallbackUsersMiddleware

logger = setup_logging(__name__, 'bot.log')

class PMSearcherBot:
    """Основной класс бота PM Searcher"""
    
    def __init__(self):
        self.bot = Bot(token=env_config.BOT_TOKEN)
        self.dp = Dispatcher(storage=MemoryStorage())
        self.alert_manager = AlertManager(self.bot)
        self.telethon_manager = TelethonManager(self.alert_manager)

        # Инициализация ResponseTracker с callback для алертов
        async def response_time_alert_callback(phone: str, alert_text: str):
            await self.alert_manager.send_response_time_alert(phone, alert_text)

        self.response_tracker = ResponseTracker(response_time_alert_callback)

        # Связываем ResponseTracker с TelethonManager
        self.telethon_manager.set_response_tracker(self.response_tracker)

        # Регистрация middleware для автоматического сохранения пользователей
        self.dp.message.middleware(MessageUsersMiddleware())
        self.dp.callback_query.middleware(CallbackUsersMiddleware())

        # Регистрация обработчиков
        register_handlers(self.dp, self.telethon_manager, self.alert_manager, self.response_tracker)
    
    async def start(self):
        """Запуск бота"""
        logger.info("Запуск PM Searcher бота...")
        try:
            # Инициализируем TelethonManager
            logger.info("Инициализация TelethonManager...")
            await self.telethon_manager.initialize()
            logger.info("TelethonManager инициализирован")

            # Запускаем ResponseTracker если мониторинг времени ответа включен
            from ..config.response_time_manager import response_time_manager
            if response_time_manager.enabled:
                logger.info("Запуск мониторинга времени ответа...")
                await self.response_tracker.start_monitoring()
                logger.info("Мониторинг времени ответа запущен")

            await self.dp.start_polling(self.bot)
        except Exception as e:
            logger.error(f"Ошибка запуска бота: {e}")
            raise
        finally:
            logger.info("Бот остановлен")
    
    async def stop(self):
        """Остановка бота"""
        logger.info("Остановка бота...")

        # Останавливаем ResponseTracker
        if self.response_tracker and self.response_tracker._running:
            logger.info("Остановка мониторинга времени ответа...")
            await self.response_tracker.stop_monitoring()
            logger.info("Мониторинг времени ответа остановлен")

        # Останавливаем мониторинг здоровья соединений TelethonManager
        if self.telethon_manager:
            logger.info("Остановка мониторинга здоровья соединений...")
            await self.telethon_manager.stop_health_monitoring()
            logger.info("Мониторинг здоровья соединений остановлен")

        await self.bot.session.close()

async def run_bot():
    """Функция запуска бота"""
    bot = PMSearcherBot()
    try:
        await bot.start()
    except KeyboardInterrupt:
        logger.info("Получен сигнал остановки")
    finally:
        await bot.stop()

if __name__ == "__main__":
    # Исправление для Windows - используем SelectorEventLoop
    import sys
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(run_bot())

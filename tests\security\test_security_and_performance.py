"""
Тесты безопасности и производительности PM Searcher Bot

Покрывает защиту от SQL-инъекций, XSS-атак, path traversal,
тесты производительности с большими файлами и конкурентность.
"""

import pytest
import time
import asyncio
import tempfile
import threading
from pathlib import Path
from unittest.mock import Mock, patch, mock_open
from concurrent.futures import ThreadPoolExecutor, as_completed

from src.utils.stopwords import StopWordsManager
from src.bot.validators import StopWordValidator, ValidationResult
from src.monitoring.telethon_manager import TelethonManager
from tests.fixtures.test_constants import (
    MALICIOUS_INPUTS, PERFORMANCE_LIMITS, SQL_INJECTION_PAYLOADS,
    XSS_PAYLOADS, PATH_TRAVERSAL_PAYLOADS
)
from tests.utils.mock_factories import StopWordsMockFactory, ValidationMockFactory


class TestSecurityValidation:
    """Тесты безопасности валидации входных данных"""
    
    @pytest.mark.parametrize("malicious_input", SQL_INJECTION_PAYLOADS)
    def test_sql_injection_protection(self, malicious_input):
        """Тест защиты от SQL-инъекций"""
        validator = StopWordValidator()
        
        # Тестируем валидацию стоп-слов
        result = validator.validate_stopword(malicious_input)
        
        # Все SQL-инъекции должны быть отклонены
        assert result.is_valid is False
        assert len(result.errors) > 0
        
        # Проверяем что в ошибках упоминаются недопустимые символы
        error_text = " ".join(result.errors).lower()
        assert any(keyword in error_text for keyword in ["символ", "недопустим", "запрещен"])
    
    @pytest.mark.parametrize("xss_payload", XSS_PAYLOADS)
    def test_xss_protection(self, xss_payload):
        """Тест защиты от XSS-атак"""
        validator = StopWordValidator()
        
        # Тестируем валидацию стоп-слов с XSS-пейлоадами
        result = validator.validate_stopword(xss_payload)
        
        # Все XSS-пейлоады должны быть отклонены
        assert result.is_valid is False
        assert len(result.errors) > 0
        
        # Проверяем что опасные символы не прошли валидацию
        if result.normalized_data:
            dangerous_chars = ['<', '>', '"', "'", '&', 'script', 'javascript']
            normalized_lower = result.normalized_data.lower()
            
            for char in dangerous_chars:
                assert char not in normalized_lower
    
    @pytest.mark.parametrize("path_payload", PATH_TRAVERSAL_PAYLOADS)
    def test_path_traversal_protection(self, path_payload):
        """Тест защиты от path traversal атак"""
        validator = StopWordValidator()
        
        # Тестируем валидацию имен файлов
        result = validator.validate_export_filename(path_payload)
        
        # Все path traversal попытки должны быть отклонены
        assert result.is_valid is False
        assert len(result.errors) > 0
        
        # Проверяем что опасные последовательности не прошли
        if result.normalized_data:
            dangerous_sequences = ['../', '..\\', '/etc/', '/root/', 'C:\\']
            normalized = result.normalized_data
            
            for sequence in dangerous_sequences:
                assert sequence not in normalized
    
    def test_file_upload_security(self):
        """Тест безопасности загрузки файлов"""
        validator = StopWordValidator()
        
        # Тест с подозрительным содержимым файла
        malicious_contents = [
            "<?php system($_GET['cmd']); ?>",
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "eval(base64_decode('malicious_code'))"
        ]
        
        for content in malicious_contents:
            result = validator.validate_import_file(content)
            
            # Файлы с подозрительным содержимым должны быть отклонены
            # или очищены от опасного содержимого
            if result.is_valid:
                # Если файл принят, проверяем что опасное содержимое удалено
                assert result.normalized_data is not None
                normalized_text = " ".join(result.normalized_data).lower()
                
                dangerous_keywords = ['script', 'php', 'eval', 'system', 'drop', 'table']
                for keyword in dangerous_keywords:
                    assert keyword not in normalized_text
            else:
                # Или файл должен быть полностью отклонен
                assert len(result.errors) > 0
    
    def test_input_length_limits(self):
        """Тест ограничений длины входных данных"""
        validator = StopWordValidator()
        
        # Тест очень длинных стоп-слов
        very_long_word = "a" * 10000
        result = validator.validate_stopword(very_long_word)
        
        assert result.is_valid is False
        assert any("длин" in error.lower() for error in result.errors)
        
        # Тест очень длинного содержимого файла
        very_long_content = "слово\n" * 100000
        result = validator.validate_import_file(very_long_content)
        
        # Должен быть отклонен или обрезан
        if result.is_valid:
            assert len(result.normalized_data) <= PERFORMANCE_LIMITS['max_stopwords_per_file']
        else:
            assert len(result.errors) > 0
    
    def test_encoding_attacks(self):
        """Тест атак через различные кодировки"""
        validator = StopWordValidator()
        
        # Тест с различными кодировками
        encoding_attacks = [
            "тест\x00null_byte",
            "тест\r\ncarriage_return",
            "тест\u0000unicode_null",
            "тест\ufeffbom_marker",
            "тест\u200bzero_width_space"
        ]
        
        for attack in encoding_attacks:
            result = validator.validate_stopword(attack)
            
            # Атаки через кодировки должны быть нейтрализованы
            if result.is_valid:
                # Опасные символы должны быть удалены
                assert '\x00' not in result.normalized_data
                assert '\r' not in result.normalized_data
                assert '\n' not in result.normalized_data
                assert '\ufeff' not in result.normalized_data
            else:
                assert len(result.errors) > 0
    
    def test_memory_exhaustion_protection(self):
        """Тест защиты от исчерпания памяти"""
        validator = StopWordValidator()
        
        # Тест с огромным количеством стоп-слов
        huge_word_list = []
        for i in range(50000):  # 50k слов
            huge_word_list.append(f"слово_{i}")
        
        huge_content = "\n".join(huge_word_list)
        
        # Валидация должна завершиться без исчерпания памяти
        start_time = time.time()
        result = validator.validate_import_file(huge_content)
        end_time = time.time()
        
        # Проверяем что валидация завершилась за разумное время
        assert end_time - start_time < 30  # Максимум 30 секунд
        
        # Результат должен быть ограничен
        if result.is_valid:
            assert len(result.normalized_data) <= PERFORMANCE_LIMITS['max_stopwords_per_file']


class TestPerformanceTests:
    """Тесты производительности"""
    
    def test_stopwords_manager_performance(self):
        """Тест производительности StopWordsManager"""
        with tempfile.TemporaryDirectory() as temp_dir:
            stopwords_file = Path(temp_dir) / "performance_test.txt"
            
            # Создаем файл с большим количеством стоп-слов
            large_word_count = 10000
            
            with open(stopwords_file, 'w', encoding='utf-8') as f:
                f.write("# Тест производительности\n")
                for i in range(large_word_count):
                    f.write(f"слово_{i:05d}\n")
            
            with patch('src.utils.stopwords.STOPWORDS_FILE', str(stopwords_file)):
                # Тест загрузки большого файла
                start_time = time.time()
                manager = StopWordsManager()
                load_time = time.time() - start_time
                
                assert len(manager.stopwords) == large_word_count
                assert load_time < 5.0  # Загрузка должна занимать менее 5 секунд
                
                # Тест поиска в большом списке
                start_time = time.time()
                contains, found = manager.contains_stopwords("Это сообщение содержит слово_05000 для тестирования")
                search_time = time.time() - start_time
                
                assert contains is True
                assert "слово_05000" in found
                assert search_time < 1.0  # Поиск должен занимать менее 1 секунды
                
                # Тест добавления множества слов
                start_time = time.time()
                for i in range(1000):
                    manager.add_stopword(f"новое_слово_{i}")
                add_time = time.time() - start_time
                
                assert add_time < 10.0  # Добавление 1000 слов должно занимать менее 10 секунд
    
    def test_validator_performance(self):
        """Тест производительности валидатора"""
        validator = StopWordValidator()
        
        # Тест валидации большого количества слов
        test_words = [f"тестовое_слово_{i}" for i in range(5000)]
        
        start_time = time.time()
        results = []
        for word in test_words:
            result = validator.validate_stopword(word)
            results.append(result)
        validation_time = time.time() - start_time
        
        # Проверяем производительность
        assert validation_time < 10.0  # 5000 валидаций за менее чем 10 секунд
        assert len(results) == 5000
        
        # Проверяем что все валидации прошли успешно
        valid_count = sum(1 for r in results if r.is_valid)
        assert valid_count == 5000
        
        # Тест валидации большого файла
        large_file_content = "\n".join(test_words)
        
        start_time = time.time()
        file_result = validator.validate_import_file(large_file_content)
        file_validation_time = time.time() - start_time
        
        assert file_validation_time < 15.0  # Валидация файла за менее чем 15 секунд
        assert file_result.is_valid is True
        assert len(file_result.normalized_data) == 5000
    
    def test_concurrent_operations_performance(self):
        """Тест производительности конкурентных операций"""
        def worker_task(worker_id: int, operations_count: int):
            """Рабочая функция для потока"""
            manager = StopWordsMockFactory.create_stopwords_manager()
            validator = StopWordValidator()
            
            results = []
            for i in range(operations_count):
                # Чередуем операции
                if i % 3 == 0:
                    # Валидация
                    result = validator.validate_stopword(f"слово_{worker_id}_{i}")
                    results.append(('validate', result.is_valid))
                elif i % 3 == 1:
                    # Добавление
                    result = manager.add_stopword(f"слово_{worker_id}_{i}")
                    results.append(('add', result))
                else:
                    # Поиск
                    result = manager.search_stopwords(f"слово_{worker_id}")
                    results.append(('search', len(result)))
            
            return results
        
        # Запускаем несколько потоков одновременно
        thread_count = 10
        operations_per_thread = 100
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            futures = []
            for worker_id in range(thread_count):
                future = executor.submit(worker_task, worker_id, operations_per_thread)
                futures.append(future)
            
            # Собираем результаты
            all_results = []
            for future in as_completed(futures):
                results = future.result()
                all_results.extend(results)
        
        total_time = time.time() - start_time
        
        # Проверяем производительность
        total_operations = thread_count * operations_per_thread
        assert len(all_results) == total_operations
        assert total_time < 30.0  # Все операции за менее чем 30 секунд
        
        # Проверяем что операции выполнились корректно
        validate_count = sum(1 for op, result in all_results if op == 'validate' and result)
        add_count = sum(1 for op, result in all_results if op == 'add' and result)
        search_count = sum(1 for op, result in all_results if op == 'search')
        
        assert validate_count > 0
        assert add_count > 0
        assert search_count > 0
    
    def test_memory_usage_optimization(self):
        """Тест оптимизации использования памяти"""
        import psutil
        import os
        
        # Получаем начальное использование памяти
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Создаем много объектов для тестирования утечек памяти
        managers = []
        validators = []
        
        for i in range(100):
            manager = StopWordsMockFactory.create_stopwords_manager()
            validator = StopWordValidator()
            
            # Выполняем операции
            for j in range(50):
                validator.validate_stopword(f"слово_{i}_{j}")
                manager.add_stopword(f"слово_{i}_{j}")
            
            managers.append(manager)
            validators.append(validator)
        
        # Проверяем использование памяти после создания объектов
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Удаляем объекты
        del managers
        del validators
        
        # Принудительная сборка мусора
        import gc
        gc.collect()
        
        # Проверяем использование памяти после очистки
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Проверяем что память освободилась
        memory_increase = peak_memory - initial_memory
        memory_after_cleanup = final_memory - initial_memory
        
        # Память должна частично освободиться
        assert memory_after_cleanup < memory_increase * 0.8  # Освободилось хотя бы 20%
        
        # Общее увеличение памяти не должно быть критичным
        assert memory_increase < 500  # Менее 500 MB для всех операций


class TestConcurrencyAndThreadSafety:
    """Тесты конкурентности и потокобезопасности"""
    
    def test_thread_safety_stopwords_manager(self):
        """Тест потокобезопасности StopWordsManager"""
        with tempfile.TemporaryDirectory() as temp_dir:
            stopwords_file = Path(temp_dir) / "thread_safety_test.txt"
            
            # Создаем начальный файл
            with open(stopwords_file, 'w', encoding='utf-8') as f:
                f.write("# Тест потокобезопасности\n")
                f.write("начальное_слово\n")
            
            with patch('src.utils.stopwords.STOPWORDS_FILE', str(stopwords_file)):
                manager = StopWordsManager()
                
                # Функция для выполнения в потоке
                def thread_worker(thread_id: int, operations: int):
                    results = []
                    for i in range(operations):
                        try:
                            # Добавляем слово
                            word = f"слово_{thread_id}_{i}"
                            result = manager.add_stopword(word)
                            results.append(('add', word, result))
                            
                            # Проверяем наличие
                            contains, found = manager.contains_stopwords(f"Текст со словом {word}")
                            results.append(('check', word, contains))
                            
                            # Удаляем слово
                            if i % 2 == 0:  # Удаляем каждое второе
                                remove_result = manager.remove_stopword(word)
                                results.append(('remove', word, remove_result))
                        except Exception as e:
                            results.append(('error', str(e), False))
                    
                    return results
                
                # Запускаем несколько потоков
                threads = []
                thread_count = 5
                operations_per_thread = 20
                
                for thread_id in range(thread_count):
                    thread = threading.Thread(
                        target=lambda tid=thread_id: thread_worker(tid, operations_per_thread)
                    )
                    threads.append(thread)
                    thread.start()
                
                # Ждем завершения всех потоков
                for thread in threads:
                    thread.join()
                
                # Проверяем финальное состояние
                final_count = len(manager.stopwords)
                assert final_count >= 1  # Минимум начальное слово
                
                # Проверяем что файл не поврежден
                manager.reload_stopwords()
                reloaded_count = len(manager.stopwords)
                assert reloaded_count == final_count
    
    @pytest.mark.asyncio
    async def test_async_operations_safety(self):
        """Тест безопасности асинхронных операций"""
        # Создаем множество асинхронных задач
        async def async_worker(worker_id: int):
            manager = StopWordsMockFactory.create_stopwords_manager()
            validator = StopWordValidator()
            
            results = []
            for i in range(50):
                # Небольшая задержка для имитации реальной работы
                await asyncio.sleep(0.001)
                
                # Валидация
                word = f"async_слово_{worker_id}_{i}"
                validation_result = validator.validate_stopword(word)
                results.append(('validate', validation_result.is_valid))
                
                # Добавление в менеджер
                if validation_result.is_valid:
                    add_result = manager.add_stopword(validation_result.normalized_data)
                    results.append(('add', add_result))
                
                # Поиск
                search_result = manager.search_stopwords(f"async_слово_{worker_id}")
                results.append(('search', len(search_result)))
            
            return results
        
        # Запускаем множество асинхронных задач
        tasks = []
        worker_count = 20
        
        for worker_id in range(worker_count):
            task = asyncio.create_task(async_worker(worker_id))
            tasks.append(task)
        
        # Ждем завершения всех задач
        all_results = await asyncio.gather(*tasks)
        
        # Проверяем результаты
        total_operations = sum(len(results) for results in all_results)
        assert total_operations > 0
        
        # Проверяем что не было критических ошибок
        for results in all_results:
            assert len(results) > 0  # Каждый воркер должен выполнить операции
    
    def test_race_condition_protection(self):
        """Тест защиты от состояний гонки"""
        with tempfile.TemporaryDirectory() as temp_dir:
            stopwords_file = Path(temp_dir) / "race_condition_test.txt"
            
            # Создаем файл
            with open(stopwords_file, 'w', encoding='utf-8') as f:
                f.write("# Тест состояний гонки\n")
                f.write("тест\n")
            
            with patch('src.utils.stopwords.STOPWORDS_FILE', str(stopwords_file)):
                # Создаем несколько менеджеров, работающих с одним файлом
                managers = [StopWordsManager() for _ in range(3)]
                
                # Функция для одновременного изменения файла
                def concurrent_modifier(manager: StopWordsManager, prefix: str):
                    for i in range(10):
                        word = f"{prefix}_{i}"
                        manager.add_stopword(word)
                        time.sleep(0.01)  # Небольшая задержка
                        
                        # Перезагружаем для синхронизации
                        manager.reload_stopwords()
                
                # Запускаем потоки одновременно
                threads = []
                for i, manager in enumerate(managers):
                    thread = threading.Thread(
                        target=concurrent_modifier,
                        args=(manager, f"prefix_{i}")
                    )
                    threads.append(thread)
                    thread.start()
                
                # Ждем завершения
                for thread in threads:
                    thread.join()
                
                # Проверяем финальное состояние
                final_manager = StopWordsManager()
                final_words = set(final_manager.stopwords)
                
                # Должны быть слова от всех потоков
                assert len(final_words) > 1  # Больше чем начальное "тест"
                
                # Проверяем что файл не поврежден
                with open(stopwords_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Файл должен содержать валидные данные
                assert "# Тест состояний гонки" in content
                assert len(content.split('\n')) > 2  # Заголовок + слова

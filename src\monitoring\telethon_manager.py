"""
Менеджер для управления Telethon клиентами (обновленная версия для общих аккаунтов)
"""

import asyncio
import json
import threading
import time
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path
from telethon import TelegramClient, events
from telethon.errors import (
    SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError,
    AuthKeyError, R<PERSON><PERSON>rror, ServerError, FloodError, UnauthorizedError
)
from telethon.tl.types import MessageMediaPhoto, MessageMediaDocument

from src.config.settings import SESSIONS_DIR, ACCOUNTS_METADATA_FILE
from src.config.environment import env_config
from src.config.monitoring_settings import monitoring_settings
from src.utils.logging import setup_logging
from src.utils.stopwords import StopWordsManager

logger = setup_logging(__name__, 'telethon_manager.log')


class SessionLockManager:
    """Менеджер блокировок для файлов сессий SQLite"""

    def __init__(self):
        self._locks: Dict[str, threading.Lock] = {}
        self._lock_creation_lock = threading.Lock()

    def get_lock(self, session_path: str) -> threading.Lock:
        """Получить блокировку для файла сессии"""
        with self._lock_creation_lock:
            if session_path not in self._locks:
                self._locks[session_path] = threading.Lock()
            return self._locks[session_path]

    def cleanup_lock(self, session_path: str):
        """Очистить блокировку для файла сессии"""
        with self._lock_creation_lock:
            if session_path in self._locks:
                del self._locks[session_path]


class NetworkErrorHandler:
    """Обработчик сетевых ошибок с классификацией и стратегиями восстановления"""

    def __init__(self):
        # Классификация ошибок по типам (используем встроенные исключения Python)
        self.network_errors = (ConnectionError, TimeoutError, OSError, ConnectionResetError, ConnectionAbortedError)
        self.auth_errors = (AuthKeyError, UnauthorizedError, SessionPasswordNeededError)
        self.rate_limit_errors = (FloodWaitError, FloodError)
        self.server_errors = (ServerError, RPCError)
        self.database_errors = (sqlite3.OperationalError,)

        # Стратегии восстановления для разных типов ошибок
        self.recovery_strategies = {
            'network': {'max_retries': 5, 'base_delay': 1.0, 'max_delay': 60.0},
            'database': {'max_retries': 3, 'base_delay': 0.5, 'max_delay': 10.0},
            'server': {'max_retries': 3, 'base_delay': 2.0, 'max_delay': 30.0},
            'rate_limit': {'max_retries': 1, 'base_delay': 0, 'max_delay': 0},  # Специальная обработка
            'auth': {'max_retries': 0, 'base_delay': 0, 'max_delay': 0}  # Не повторяем
        }

    def classify_error(self, error: Exception) -> str:
        """Классифицировать ошибку по типу"""
        if isinstance(error, self.network_errors):
            return 'network'
        elif isinstance(error, self.database_errors):
            if "database is locked" in str(error).lower():
                return 'database'
            else:
                return 'database'
        elif isinstance(error, self.auth_errors):
            return 'auth'
        elif isinstance(error, self.rate_limit_errors):
            return 'rate_limit'
        elif isinstance(error, self.server_errors):
            return 'server'
        else:
            return 'unknown'

    def should_retry(self, error: Exception, attempt: int) -> bool:
        """Определить, стоит ли повторять операцию"""
        error_type = self.classify_error(error)
        strategy = self.recovery_strategies.get(error_type, {'max_retries': 0})
        return attempt < strategy['max_retries']

    def get_retry_delay(self, error: Exception, attempt: int) -> float:
        """Получить задержку для повтора"""
        error_type = self.classify_error(error)
        strategy = self.recovery_strategies.get(error_type, {'base_delay': 1.0, 'max_delay': 60.0})

        if error_type == 'rate_limit' and isinstance(error, FloodWaitError):
            # Для FloodWait используем время из ошибки
            return min(error.seconds, 3600)  # Максимум 1 час

        # Экспоненциальная задержка с jitter
        base_delay = strategy['base_delay']
        max_delay = strategy['max_delay']
        delay = min(base_delay * (2 ** attempt), max_delay)

        # Добавляем небольшой jitter для предотвращения thundering herd
        import random
        jitter = random.uniform(0.1, 0.3) * delay
        return delay + jitter

    def get_error_description(self, error: Exception) -> str:
        """Получить описание ошибки для логирования"""
        error_type = self.classify_error(error)
        error_msg = str(error)

        descriptions = {
            'network': f"Сетевая ошибка: {error_msg}",
            'database': f"Ошибка базы данных: {error_msg}",
            'auth': f"Ошибка авторизации: {error_msg}",
            'rate_limit': f"Превышен лимит запросов: {error_msg}",
            'server': f"Ошибка сервера: {error_msg}",
            'unknown': f"Неизвестная ошибка: {error_msg}"
        }

        return descriptions.get(error_type, f"Ошибка: {error_msg}")


class ConnectionHealthMonitor:
    """Монитор здоровья соединений Telethon"""

    def __init__(self):
        self.connection_stats: Dict[str, Dict] = {}
        self.last_health_check: Dict[str, datetime] = {}
        self.reconnect_attempts: Dict[str, int] = {}
        self.max_reconnect_attempts = 5
        self.health_check_interval = 300  # 5 минут
        self.error_handler = NetworkErrorHandler()

    def record_connection_event(self, phone: str, event_type: str, error: str = None):
        """Записать событие соединения"""
        if phone not in self.connection_stats:
            self.connection_stats[phone] = {
                'connects': 0,
                'disconnects': 0,
                'errors': 0,
                'last_error': None,
                'last_connect': None,
                'last_disconnect': None
            }

        stats = self.connection_stats[phone]
        now = datetime.now()

        if event_type == 'connect':
            stats['connects'] += 1
            stats['last_connect'] = now
            # Сбрасываем счетчик попыток переподключения при успешном подключении
            self.reconnect_attempts[phone] = 0
        elif event_type == 'disconnect':
            stats['disconnects'] += 1
            stats['last_disconnect'] = now
        elif event_type == 'error':
            stats['errors'] += 1
            stats['last_error'] = error
            self.reconnect_attempts[phone] = self.reconnect_attempts.get(phone, 0) + 1

    def should_attempt_reconnect(self, phone: str) -> bool:
        """Проверить, стоит ли пытаться переподключиться"""
        attempts = self.reconnect_attempts.get(phone, 0)
        return attempts < self.max_reconnect_attempts

    def get_reconnect_delay(self, phone: str) -> float:
        """Получить задержку для переподключения (экспоненциальная)"""
        attempts = self.reconnect_attempts.get(phone, 0)
        # Экспоненциальная задержка: 1, 2, 4, 8, 16 секунд
        return min(2 ** attempts, 60)  # Максимум 60 секунд

    def is_connection_healthy(self, phone: str) -> bool:
        """Проверить здоровье соединения"""
        if phone not in self.connection_stats:
            return True  # Новое соединение считается здоровым

        stats = self.connection_stats[phone]
        now = datetime.now()

        # Если было много ошибок за последнее время
        if stats['errors'] > 10:
            last_error_time = stats.get('last_disconnect')
            if last_error_time and (now - last_error_time).seconds < 3600:  # За последний час
                return False

        return True


class TelethonManager:
    """Менеджер для управления Telethon клиентами (общие аккаунты)"""

    def __init__(self, alert_manager=None):
        self.clients: Dict[str, Dict] = {}  # phone: {'client': TelegramClient, 'monitoring': bool, 'session_file': str}
        self.stopwords_manager = StopWordsManager()
        self._initialization_done = False
        self.alert_manager = alert_manager
        self.response_tracker = None  # Будет инициализирован позже

        # Новые компоненты для улучшенного управления
        self.session_lock_manager = SessionLockManager()
        self.health_monitor = ConnectionHealthMonitor()
        self._reconnect_tasks: Dict[str, asyncio.Task] = {}  # Активные задачи переподключения
        self._health_monitor_task: Optional[asyncio.Task] = None  # Задача мониторинга здоровья

    async def _safe_session_operation(self, session_path: Path, operation: Callable, max_retries: int = 3) -> Any:
        """Безопасное выполнение операции с файлом сессии с улучшенной обработкой ошибок"""
        session_str = str(session_path)
        lock = self.session_lock_manager.get_lock(session_str)
        error_handler = self.health_monitor.error_handler

        for attempt in range(max_retries):
            try:
                with lock:
                    # Небольшая задержка для предотвращения race conditions
                    if attempt > 0:
                        await asyncio.sleep(0.1 * attempt)

                    return await operation()

            except Exception as e:
                # Используем новый обработчик ошибок
                error_type = error_handler.classify_error(e)
                error_description = error_handler.get_error_description(e)

                if error_handler.should_retry(e, attempt):
                    delay = error_handler.get_retry_delay(e, attempt)
                    logger.warning(f"Ошибка сессии {session_path}: {error_description}. "
                                 f"Повтор через {delay:.1f}s (попытка {attempt + 1}/{max_retries})")
                    await asyncio.sleep(delay)
                    continue
                else:
                    logger.error(f"Критическая ошибка сессии {session_path}: {error_description}")
                    raise

    async def _create_client_safely(self, session_path: Path, phone: str) -> TelegramClient:
        """Безопасное создание клиента Telethon"""
        async def create_operation():
            return TelegramClient(str(session_path), env_config.API_ID, env_config.API_HASH)

        try:
            client = await self._safe_session_operation(session_path, create_operation)
            logger.debug(f"[{phone}] Клиент Telethon создан для сессии {session_path}")
            return client
        except Exception as e:
            logger.error(f"[{phone}] Ошибка создания клиента: {e}")
            raise

    async def _connect_client_with_retry(self, client: TelegramClient, phone: str, max_retries: int = 5) -> bool:
        """Подключение клиента с улучшенной retry логикой и классификацией ошибок"""
        error_handler = self.health_monitor.error_handler

        for attempt in range(max_retries):
            try:
                await client.connect()
                self.health_monitor.record_connection_event(phone, 'connect')
                logger.info(f"[{phone}] Подключение к Telegram API успешно (попытка {attempt + 1})")
                return True

            except Exception as e:
                # Записываем ошибку в статистику
                self.health_monitor.record_connection_event(phone, 'error', str(e))

                # Классифицируем ошибку и определяем стратегию
                error_type = error_handler.classify_error(e)
                error_description = error_handler.get_error_description(e)

                # Проверяем, стоит ли повторять
                if error_handler.should_retry(e, attempt):
                    delay = error_handler.get_retry_delay(e, attempt)

                    logger.warning(f"[{phone}] {error_description} (попытка {attempt + 1}/{max_retries}). "
                                 f"Повтор через {delay:.1f}s")

                    await asyncio.sleep(delay)
                    continue
                else:
                    # Не повторяем или исчерпаны попытки
                    if error_type == 'auth':
                        logger.error(f"[{phone}] {error_description} - требуется повторная авторизация")
                    elif error_type == 'rate_limit':
                        logger.error(f"[{phone}] {error_description} - превышен лимит запросов")
                    else:
                        logger.error(f"[{phone}] {error_description} - все попытки исчерпаны")

                    raise

        return False

    async def initialize(self):
        """Инициализация менеджера"""
        if self._initialization_done:
            return

        await self._initialize_existing_accounts()

        # Запускаем мониторинг здоровья соединений
        self._health_monitor_task = asyncio.create_task(self._health_monitoring_loop())

        self._initialization_done = True
        logger.info("TelethonManager инициализирован с мониторингом здоровья соединений")

    def set_response_tracker(self, response_tracker):
        """Установка трекера времени ответа"""
        self.response_tracker = response_tracker
        logger.info("ResponseTracker установлен в TelethonManager")

    def _create_alert_callback(self, phone: str):
        """Создание callback для отправки алертов с маршрутизацией по тимлидам"""
        if not self.alert_manager:
            logger.warning(f"[{phone}] AlertManager не установлен, алерты не будут отправляться")
            return None

        async def alert_callback(alert_text):
            try:
                # Используем новую систему маршрутизации по тимлидам
                await self.alert_manager.send_alert_with_phone_routing(phone, alert_text)
                logger.info(f"[{phone}] Алерт отправлен с маршрутизацией по тимлидам")
            except Exception as e:
                logger.error(f"[{phone}] Критическая ошибка в alert_callback: {e}")

        return alert_callback

    def reload_monitoring_settings(self) -> bool:
        """
        Перезагрузка настроек мониторинга

        Returns:
            bool: Успешность операции
        """
        try:
            success = monitoring_settings.reload_settings()
            if success:
                logger.info("Настройки мониторинга перезагружены")
            return success
        except Exception as e:
            logger.error(f"Ошибка перезагрузки настроек мониторинга: {e}")
            return False

    def get_monitoring_status(self) -> Dict[str, Any]:
        """
        Получение статуса настроек мониторинга

        Returns:
            Dict[str, Any]: Словарь с настройками мониторинга
        """
        return monitoring_settings.get_monitoring_status()

    def _phone_to_session_name(self, phone: str) -> str:
        """Преобразование номера телефона в имя файла сессии"""
        # Убираем все символы кроме цифр
        clean_phone = ''.join(filter(str.isdigit, phone))
        return f"account_{clean_phone}"
    
    def _is_image_media(self, message) -> bool:
        """Проверка является ли медиа файл изображением"""
        try:
            # Проверяем фотографии
            if message.photo:
                return True
            
            # Проверяем документы (могут быть изображениями)
            if message.document:
                mime_type = getattr(message.document, 'mime_type', '')
                if mime_type:
                    # Поддерживаемые типы изображений
                    image_mime_types = [
                        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
                        'image/webp', 'image/bmp', 'image/tiff', 'image/svg+xml'
                    ]
                    return mime_type.lower() in image_mime_types
            
            return False
        except Exception as e:
            logger.error(f"Ошибка при проверке типа медиа: {e}")
            return False
    
    def _get_chat_type(self, chat) -> str:
        """
        Точное определение типа чата

        Args:
            chat: Объект чата от Telethon

        Returns:
            str: Тип чата ('private', 'group', 'supergroup', 'channel')
        """
        try:
            # Проверяем супергруппы (мегагруппы)
            if hasattr(chat, 'megagroup') and chat.megagroup:
                return 'supergroup'
            # Проверяем каналы
            elif hasattr(chat, 'broadcast') and chat.broadcast:
                return 'channel'
            # Проверяем обычные группы
            elif hasattr(chat, 'title'):
                return 'group'
            # Личные чаты
            else:
                return 'private'
        except Exception as e:
            logger.warning(f"Ошибка определения типа чата: {e}, используем fallback")
            # Fallback к старой логике
            return 'group' if hasattr(chat, 'title') else 'private'

    async def _get_sender_info(self, event) -> dict:
        """Получение информации об отправителе сообщения"""
        try:
            sender = await event.get_sender()
            chat = await event.get_chat()

            # Информация об отправителе
            sender_info = {
                'id': getattr(sender, 'id', 'Неизвестно'),
                'username': getattr(sender, 'username', None),
                'first_name': getattr(sender, 'first_name', None),
                'last_name': getattr(sender, 'last_name', None),
                'phone': getattr(sender, 'phone', None)
            }

            # Формируем отображаемое имя
            display_name_parts = []
            if sender_info['first_name']:
                display_name_parts.append(sender_info['first_name'])
            if sender_info['last_name']:
                display_name_parts.append(sender_info['last_name'])

            if display_name_parts:
                sender_info['display_name'] = ' '.join(display_name_parts)
            elif sender_info['username']:
                sender_info['display_name'] = f"@{sender_info['username']}"
            else:
                sender_info['display_name'] = f"ID: {sender_info['id']}"

            # Информация о чате с улучшенным определением типа
            chat_info = {
                'id': getattr(chat, 'id', 'Неизвестно'),
                'title': getattr(chat, 'title', getattr(chat, 'first_name', 'Неизвестный чат')),
                'type': self._get_chat_type(chat)
            }

            return {
                'sender': sender_info,
                'chat': chat_info
            }

        except Exception as e:
            error_msg = str(e).lower()
            if "database is locked" in error_msg:
                logger.error(f"Ошибка при получении информации об отправителе: database is locked")
            else:
                logger.error(f"Ошибка при получении информации об отправителе: {e}")
            return {
                'sender': {
                    'id': 'Неизвестно',
                    'display_name': 'Неизвестный пользователь'
                },
                'chat': {
                    'id': 'Неизвестно',
                    'title': 'Неизвестный чат',
                    'type': 'unknown'
                }
            }

    def _is_bot_sender(self, sender) -> bool:
        """
        Проверка, является ли отправитель ботом

        Args:
            sender: Объект отправителя из Telethon

        Returns:
            bool: True если отправитель является ботом
        """
        try:
            # Проверяем флаг bot в объекте пользователя
            if hasattr(sender, 'bot') and sender.bot:
                return True

            # Дополнительная проверка по username (если есть)
            if hasattr(sender, 'username') and sender.username:
                username = sender.username.lower()
                # Проверяем окончание на "_bot"
                if username.endswith('_bot'):
                    return True
                # Проверяем другие распространенные паттерны ботов
                bot_patterns = ['bot', 'support', 'helper', 'assistant']
                if any(pattern in username for pattern in bot_patterns):
                    # Дополнительная проверка: если username содержит bot-паттерн
                    # и не является обычным пользователем (проверяем наличие флага bot)
                    return getattr(sender, 'bot', False)

            return False

        except Exception as e:
            logger.warning(f"Ошибка при проверке типа отправителя: {e}")
            # В случае ошибки считаем, что это не бот (безопасный fallback)
            return False

    async def start_auth(self, user_id: int, phone: str, force_reauth: bool = False) -> bool:
        """Начало процесса авторизации с улучшенной обработкой ошибок"""
        try:
            # Создаем имя файла сессии на основе номера телефона
            session_name = self._phone_to_session_name(phone)
            session_path = SESSIONS_DIR / f'{session_name}.session'

            # Если требуется принудительная переавторизация, удаляем старую сессию
            if force_reauth and session_path.exists():
                logger.info(f"[{phone}] Принудительная переавторизация: удаляем старую сессию")
                # Безопасное удаление с блокировкой
                lock = self.session_lock_manager.get_lock(str(session_path))
                with lock:
                    if session_path.exists():
                        session_path.unlink()
                        # Также удаляем journal файл если есть
                        journal_path = session_path.with_suffix('.session-journal')
                        if journal_path.exists():
                            journal_path.unlink()

            # Создаем клиент безопасно
            client = await self._create_client_safely(session_path, phone)

            # Подключение с улучшенной retry логикой
            connected = await self._connect_client_with_retry(client, phone)
            if not connected:
                raise Exception("Не удалось подключиться к Telegram API")

            # Проверяем авторизацию
            async def check_auth_operation():
                return await client.is_user_authorized()

            is_authorized = await self._safe_session_operation(session_path, check_auth_operation)

            if not is_authorized or force_reauth:
                async def send_code_operation():
                    return await client.send_code_request(phone)

                await self._safe_session_operation(session_path, send_code_operation)
                logger.info(f"[{phone}] Код отправлен на номер {phone}")
            else:
                logger.info(f"[{phone}] Аккаунт уже авторизован")

            self.clients[phone] = {
                'client': client,
                'monitoring': False,
                'session_file': f'{session_name}.session'
            }

            # Сохраняем метаданные
            self._save_accounts_metadata()

            return True

        except FloodWaitError as e:
            # Очищаем клиент при FloodWait
            if phone in self.clients:
                try:
                    await self.clients[phone]['client'].disconnect()
                except:
                    pass
                del self.clients[phone]

            # Форматируем время ожидания в удобочитаемом виде
            hours = e.seconds // 3600
            minutes = (e.seconds % 3600) // 60
            seconds = e.seconds % 60

            if hours > 0:
                time_str = f"{hours} ч {minutes} мин"
            elif minutes > 0:
                time_str = f"{minutes} мин {seconds} сек"
            else:
                time_str = f"{seconds} сек"

            logger.error(f"[{phone}] FloodWait ошибка: нужно подождать {e.seconds} секунд ({time_str})")
            raise Exception(f"Слишком много запросов. Попробуйте через {time_str}")
        except Exception as e:
            logger.error(f"[{phone}] Ошибка start_auth: {e}")
            raise

    async def complete_auth(self, user_id: int, phone: str, code: str, alert_callback: Optional[Callable] = None) -> bool:
        """Завершение авторизации и запуск мониторинга"""
        try:
            client_data = self.clients.get(phone)
            if not client_data:
                raise Exception('Клиент не найден. Начните процесс заново.')
            
            client = client_data['client']
            
            try:
                await client.sign_in(phone, code)
                logger.info(f"[{phone}] Авторизация успешна для номера {phone}")
            except SessionPasswordNeededError:
                raise Exception("Требуется двухфакторная аутентификация. Эта функция пока не поддерживается.")
            except PhoneCodeInvalidError:
                raise Exception("Неверный код подтверждения")
            except Exception as auth_error:
                # Проверяем на истечение кода
                error_msg = str(auth_error).lower()
                if "expired" in error_msg or "истек" in error_msg:
                    raise Exception("Код подтверждения истек. Запросите новый код.")
                elif "invalid" in error_msg or "неверный" in error_msg:
                    raise Exception("Неверный код подтверждения")
                else:
                    raise Exception(f"Ошибка авторизации: {auth_error}")
            
            # Запуск мониторинга
            await self.start_monitoring(phone, alert_callback)
            
            # Обновление статуса
            self.clients[phone]['monitoring'] = True

            # Сохраняем метаданные
            self._save_accounts_metadata()

            return True

        except Exception as e:
            logger.error(f"[{phone}] Ошибка complete_auth: {e}")
            # Очистка клиента при ошибке с таймаутом
            if phone in self.clients:
                try:
                    client = self.clients[phone]['client']
                    await asyncio.wait_for(client.disconnect(), timeout=5.0)
                except asyncio.TimeoutError:
                    logger.warning(f"[{phone}] Таймаут при отключении клиента в complete_auth")
                except Exception as disconnect_error:
                    logger.warning(f"[{phone}] Ошибка отключения в complete_auth: {disconnect_error}")
                finally:
                    # Удаляем из памяти в любом случае
                    del self.clients[phone]
            raise

    async def start_monitoring(self, phone: str, alert_callback: Optional[Callable] = None):
        """Запуск мониторинга исходящих сообщений и входящих фотографий"""
        # Проверяем, что alert_manager инициализирован для мониторинга
        if not self.alert_manager:
            logger.error(f"[{phone}] Критическая ошибка: alert_manager не установлен, алерты не будут отправляться!")
            raise Exception("AlertManager не инициализирован для мониторингового аккаунта. Алерты невозможны.")
        # Конец проверки alert_manager
        try:
            client_data = self.clients.get(phone)
            if not client_data:
                raise Exception('Клиент не найден')

            client = client_data['client']

            if not alert_callback:
                alert_callback = self._create_alert_callback(phone)

            # Обработчик исходящих сообщений
            @client.on(events.NewMessage(outgoing=True))
            async def message_handler(event):
                try:
                    text = event.message.message
                    if not text:
                        return

                    # Получение информации о чате
                    chat = await event.get_chat()
                    chat_title = getattr(chat, 'title', getattr(chat, 'first_name', 'Неизвестный чат'))

                    # Определение типа чата с улучшенной логикой
                    chat_type = self._get_chat_type(chat)

                    # Логирование всех исходящих сообщений
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    log_message = f"[{timestamp}] [{phone}] -> [{chat_title}] ({chat_type}): {text}"
                    logger.info(log_message)

                    # Проверка настроек мониторинга для данного типа чата
                    if not monitoring_settings.should_monitor_chat(chat_type):
                        logger.debug(f"[{phone}] Мониторинг для {chat_type} чатов отключен, пропускаем сообщение из чата {chat_title}")
                        return

                    # Проверка на стоп-слова
                    has_stopwords, found_words = self.stopwords_manager.contains_stopwords(text)

                    if has_stopwords:
                        chat_type_emoji = "👤" if chat_type == 'private' else "👥"
                        chat_type_text = "Личный чат" if chat_type == 'private' else "Групповой чат"

                        alert_text = (
                            f"🚨 АЛЕРТ: Обнаружены стоп-слова!\n\n"
                            f"👤 Аккаунт: {phone}\n"
                            f"{chat_type_emoji} Чат: {chat_title} ({chat_type_text})\n"
                            f"📝 Сообщение: {text}\n"
                            f"⚠️ Найденные стоп-слова: {', '.join(found_words)}\n"
                            f"🕐 Время: {timestamp}"
                        )

                        logger.warning(f"[{phone}] СТОП-СЛОВА ОБНАРУЖЕНЫ: {found_words} в сообщении: {text}")

                        # Отправка алерта через callback
                        if alert_callback:
                            try:
                                await alert_callback(alert_text)
                            except Exception as e:
                                logger.error(f"[{phone}] Ошибка отправки алерта: {e}")

                    # Отслеживание исходящих сообщений для времени ответа
                    if self.response_tracker:
                        try:
                            outgoing_message_data = {
                                'account_phone': phone,
                                'chat_id': chat.id,
                                'message_id': event.message.id,
                                'text': text
                            }
                            self.response_tracker.track_outgoing_message(outgoing_message_data)
                        except Exception as e:
                            logger.error(f"[{phone}] Ошибка отслеживания исходящего сообщения: {e}")

                except Exception as e:
                    logger.error(f"[{phone}] Ошибка в обработчике сообщений: {e}")

            # Обработчик входящих сообщений с фотографиями
            @client.on(events.NewMessage(incoming=True))
            async def incoming_photo_handler(event):
                try:
                    # Проверяем, содержит ли сообщение изображение
                    if not self._is_image_media(event.message):
                        return

                    # Получаем информацию об отправителе и чате
                    info = await self._get_sender_info(event)
                    sender_info = info['sender']
                    chat_info = info['chat']

                    # Проверка настроек мониторинга для данного типа чата
                    if not monitoring_settings.should_monitor_chat(chat_info['type']):
                        logger.debug(f"[{phone}] Мониторинг для {chat_info['type']} чатов отключен, пропускаем фотографию из чата {chat_info['title']}")
                        return

                    # Проверка фильтрации ботов
                    sender = await event.get_sender()
                    is_bot = self._is_bot_sender(sender)

                    if monitoring_settings.bot_filtering_enabled and is_bot:
                        logger.info(f"[{phone}] 🤖 ФИЛЬТРАЦИЯ БОТОВ: Пропускаем фото алерт от бота {sender_info['display_name']} (ID: {sender_info['id']}) в чате {chat_info['title']}")
                        return
                    elif is_bot:
                        logger.debug(f"[{phone}] 🤖 Обнаружен бот {sender_info['display_name']} (ID: {sender_info['id']}), но фильтрация отключена - отправляем алерт")

                    # Временная метка
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    # Логирование получения фотографии
                    logger.info(f"[{phone}] Получена фотография от {sender_info['display_name']} в чате {chat_info['title']} (тип: {chat_info['type']})")

                    # Скачиваем фотографию
                    try:
                        photo_bytes = await event.message.download_media(file=bytes)

                        if photo_bytes:
                            # Формируем текст алерта
                            alert_caption = (
                                f"📸 ФОТО АЛЕРТ\n\n"
                                f"👤 Аккаунт: {phone}\n"
                                f"📤 От: {sender_info['display_name']}\n"
                                f"🆔 ID отправителя: {sender_info['id']}\n"
                                f"💬 Чат: {chat_info['title']}\n"
                                f"📱 Тип чата: {chat_info['type']}\n"
                                f"🕐 Время: {timestamp}"
                            )

                            # Добавляем username если есть
                            if sender_info['username']:
                                alert_caption += f"\n👤 Username: @{sender_info['username']}"

                            # Добавляем номер телефона если есть
                            if sender_info['phone']:
                                alert_caption += f"\n📞 Телефон: {sender_info['phone']}"

                            # Отправка фото алерта с маршрутизацией по тимлидам
                            if self.alert_manager and hasattr(self.alert_manager, 'send_photo_alert_with_phone_routing'):
                                try:
                                    await self.alert_manager.send_photo_alert_with_phone_routing(phone, photo_bytes, alert_caption)
                                    logger.info(f"[{phone}] Фото алерт отправлен с маршрутизацией по тимлидам")
                                except Exception as e:
                                    logger.error(f"[{phone}] Ошибка отправки фото алерта: {e}")
                                    # Fallback на текстовый алерт
                                    if alert_callback:
                                        await alert_callback(alert_caption)
                            elif alert_callback:
                                # Fallback на текстовый алерт
                                await alert_callback(alert_caption)
                                logger.warning(f"[{phone}] Использован fallback для фото алерта")
                        else:
                            logger.warning(f"[{phone}] Не удалось скачать фотографию")

                    except Exception as download_error:
                        logger.error(f"[{phone}] Ошибка скачивания фотографии: {download_error}")

                        # Отправляем текстовый алерт о том, что была попытка отправить фото
                        if alert_callback:
                            try:
                                fallback_alert = (
                                    f"📸 ФОТО АЛЕРТ (ошибка загрузки)\n\n"
                                    f"👤 Аккаунт: {phone}\n"
                                    f"📤 От: {sender_info['display_name']}\n"
                                    f"🆔 ID отправителя: {sender_info['id']}\n"
                                    f"💬 Чат: {chat_info['title']}\n"
                                    f"🕐 Время: {timestamp}\n"
                                    f"❌ Ошибка: Не удалось загрузить изображение"
                                )
                                await alert_callback(fallback_alert)
                            except Exception as fallback_error:
                                logger.error(f"[{phone}] Ошибка отправки fallback алерта: {fallback_error}")

                except Exception as e:
                    logger.error(f"[{phone}] Ошибка в обработчике входящих фотографий: {e}")

            # Обработчик входящих сообщений для отслеживания времени ответа
            @client.on(events.NewMessage(incoming=True))
            async def incoming_message_response_tracker(event):
                try:
                    # Проверяем, включен ли трекер времени ответа
                    if not self.response_tracker:
                        return

                    # Получаем информацию об отправителе и чате
                    info = await self._get_sender_info(event)
                    sender_info = info['sender']
                    chat_info = info['chat']

                    # Проверяем, что это сообщение от внешнего пользователя (не от самого менеджера)
                    me = await client.get_me()
                    is_from_external = sender_info['id'] != me.id

                    # Проверяем фильтрацию ботов
                    sender = await event.get_sender()
                    is_bot = self._is_bot_sender(sender)

                    # Подготавливаем данные для трекера
                    message_data = {
                        'account_phone': phone,
                        'message_id': event.message.id,
                        'sender_id': sender_info['id'],
                        'sender_name': sender_info['display_name'],
                        'chat_id': chat_info['id'],
                        'chat_title': chat_info['title'],
                        'text': event.message.text or '[Медиа сообщение]',
                        'is_from_external': is_from_external,
                        'chat_type': chat_info['type'],
                        'is_bot': is_bot
                    }

                    # Отправляем в трекер для отслеживания
                    self.response_tracker.track_incoming_message(message_data)

                except Exception as e:
                    error_msg = str(e).lower()
                    if "database is locked" in error_msg:
                        logger.error(f"[{phone}] Ошибка в обработчике времени ответа: database is locked")
                        # Записываем событие для мониторинга здоровья
                        self.health_monitor.record_connection_event(phone, 'error', 'database_locked')
                    else:
                        logger.error(f"[{phone}] Ошибка в обработчике времени ответа: {e}")

            # Запуск клиента в фоновом режиме
            if not client.is_connected():
                await client.start()

            monitoring_features = "исходящие сообщения + входящие фотографии"
            if self.response_tracker:
                monitoring_features += " + время ответа"
            logger.info(f"[{phone}] Мониторинг запущен ({monitoring_features})")

            # Запуск клиента в отдельной задаче
            asyncio.create_task(self._keep_client_running(phone))

        except Exception as e:
            logger.error(f"[{phone}] Ошибка start_monitoring: {e}")
            raise

    async def _keep_client_running(self, phone: str):
        """Поддержание работы клиента"""
        try:
            client_data = self.clients.get(phone)
            if not client_data:
                return

            client = client_data['client']
            await client.run_until_disconnected()

        except Exception as e:
            logger.error(f"[{phone}] Клиент отключился: {e}")
            # Попытка переподключения
            await self._reconnect_client(phone)

    async def _reconnect_client(self, phone: str):
        """Улучшенное переподключение клиента с мониторингом здоровья"""
        # Проверяем, не выполняется ли уже переподключение
        if phone in self._reconnect_tasks:
            existing_task = self._reconnect_tasks[phone]
            if not existing_task.done():
                logger.debug(f"[{phone}] Переподключение уже выполняется, ожидаем...")
                return
            else:
                # Удаляем завершенную задачу
                del self._reconnect_tasks[phone]

        # Проверяем, стоит ли пытаться переподключиться
        if not self.health_monitor.should_attempt_reconnect(phone):
            logger.error(f"[{phone}] Превышено максимальное количество попыток переподключения")
            self.clients[phone]['monitoring'] = False
            return

        # Создаем задачу переподключения
        self._reconnect_tasks[phone] = asyncio.create_task(self._perform_reconnect(phone))

    async def _perform_reconnect(self, phone: str):
        """Выполнение переподключения"""
        try:
            client_data = self.clients.get(phone)
            if not client_data:
                logger.warning(f"[{phone}] Клиент не найден при переподключении")
                return

            # Получаем задержку для переподключения
            delay = self.health_monitor.get_reconnect_delay(phone)
            if delay > 0:
                logger.info(f"[{phone}] Ожидание {delay}s перед переподключением...")
                await asyncio.sleep(delay)

            logger.info(f"[{phone}] Попытка переподключения...")
            client = client_data['client']

            # Пытаемся переподключиться с улучшенной логикой
            connected = await self._connect_client_with_retry(client, phone, max_retries=3)

            if connected:
                # Проверяем авторизацию
                session_name = self._phone_to_session_name(phone)
                session_path = SESSIONS_DIR / f'{session_name}.session'

                async def check_auth_operation():
                    return await client.is_user_authorized()

                is_authorized = await self._safe_session_operation(session_path, check_auth_operation)

                if is_authorized:
                    logger.info(f"[{phone}] Переподключение успешно")
                    # Перезапускаем клиент
                    asyncio.create_task(self._keep_client_running(phone))
                else:
                    logger.warning(f"[{phone}] Требуется повторная авторизация")
                    self.clients[phone]['monitoring'] = False
            else:
                logger.error(f"[{phone}] Не удалось переподключиться")
                self.clients[phone]['monitoring'] = False

        except Exception as e:
            logger.error(f"[{phone}] Ошибка переподключения: {e}")
            self.health_monitor.record_connection_event(phone, 'error', str(e))
            self.clients[phone]['monitoring'] = False
        finally:
            # Удаляем задачу из списка активных
            if phone in self._reconnect_tasks:
                del self._reconnect_tasks[phone]

    def get_connected_accounts(self) -> List[Dict]:
        """Получение списка подключенных аккаунтов (включая неавторизованные)"""
        accounts = []

        # Добавляем активные аккаунты из self.clients
        for phone, data in self.clients.items():
            accounts.append({
                'user_id': phone,  # Для обратной совместимости
                'phone': phone,
                'monitoring': data['monitoring'],
                'session_file': data['session_file'],
                'authorized': True
            })

        # Добавляем неавторизованные аккаунты из метаданных
        try:
            if ACCOUNTS_METADATA_FILE.exists():
                with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

                for phone, account_data in metadata.items():
                    # Проверяем, что аккаунт еще не добавлен
                    if not any(acc['phone'] == phone for acc in accounts):
                        accounts.append({
                            'user_id': phone,  # Для обратной совместимости
                            'phone': phone,
                            'monitoring': False,  # Неавторизованные аккаунты не могут мониторить
                            'session_file': account_data.get('session_file', ''),
                            'authorized': False,
                            'needs_reauth': account_data.get('needs_reauth', True)
                        })
        except Exception as e:
            logger.error(f"Ошибка загрузки метаданных для get_connected_accounts: {e}")

        return accounts

    def _save_accounts_metadata(self):
        """Сохранение метаданных аккаунтов"""
        try:
            metadata = {}
            for phone, data in self.clients.items():
                metadata[phone] = {
                    'session_file': data['session_file'],
                    'monitoring': data['monitoring'],
                    'last_updated': datetime.now().isoformat()
                }

            # Создаем директорию если не существует
            ACCOUNTS_METADATA_FILE.parent.mkdir(parents=True, exist_ok=True)

            with open(ACCOUNTS_METADATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

            logger.info(f"Метаданные сохранены для {len(metadata)} аккаунтов")

        except Exception as e:
            logger.error(f"Ошибка сохранения метаданных: {e}")

    async def _initialize_existing_accounts(self):
        """Инициализация существующих аккаунтов при запуске"""
        try:
            # Загружаем метаданные
            metadata = {}
            if ACCOUNTS_METADATA_FILE.exists():
                with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                logger.info(f"Загружены метаданные для {len(metadata)} аккаунтов")
            else:
                logger.info("Файл метаданных не найден, создаем новый")
                return

            # Восстанавливаем аккаунты
            restored_count = 0

            for phone, account_data in metadata.items():
                try:
                    session_file = account_data.get('session_file')
                    if not session_file:
                        logger.warning(f"Не найден файл сессии для аккаунта {phone}")
                        continue

                    session_path = SESSIONS_DIR / session_file
                    if not session_path.exists():
                        logger.warning(f"Файл сессии не найден: {session_path}")
                        continue

                    # Создаем клиент безопасно
                    client = await self._create_client_safely(session_path, phone)

                    # Подключение с улучшенной retry логикой
                    connected = await self._connect_client_with_retry(client, phone)

                    if not connected:
                        logger.warning(f"Не удалось подключиться к аккаунту {phone}, пропускаем")
                        continue

                    # Проверяем авторизацию безопасно
                    async def check_auth_operation():
                        return await client.is_user_authorized()

                    is_authorized = await self._safe_session_operation(session_path, check_auth_operation)

                    if is_authorized:
                        # Восстанавливаем аккаунт
                        self.clients[phone] = {
                            'client': client,
                            'session_file': session_file,
                            'monitoring': False  # Мониторинг запустится отдельно
                        }

                        logger.info(f"Восстановлен аккаунт {phone}")
                        restored_count += 1

                        # Запускаем мониторинг если он был активен
                        if account_data.get('monitoring', False):
                            try:
                                # Создаем alert_callback для восстановленного аккаунта
                                alert_callback = self._create_alert_callback(phone)
                                await self.start_monitoring(phone, alert_callback)
                                self.clients[phone]['monitoring'] = True
                                logger.info(f"Мониторинг запущен для аккаунта {phone}")
                            except Exception as e:
                                logger.error(f"Ошибка запуска мониторинга для {phone}: {e}")
                    else:
                        logger.warning(f"Сессия для аккаунта {phone} не авторизована")
                        await client.disconnect()

                except Exception as e:
                    logger.error(f"Ошибка восстановления аккаунта {phone}: {e}")

            logger.info(f"Восстановлено {restored_count} аккаунтов")

        except Exception as e:
            logger.error(f"Критическая ошибка инициализации аккаунтов: {e}")

    def reload_stopwords(self) -> int:
        """Перезагрузка стоп-слов"""
        return self.stopwords_manager.reload_stopwords()

    async def get_connection_health_report(self) -> Dict[str, Any]:
        """Получить отчет о здоровье соединений"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_clients': len(self.clients),
            'active_monitoring': sum(1 for client_data in self.clients.values()
                                   if client_data.get('monitoring', False)),
            'connection_stats': {},
            'reconnect_attempts': dict(self.health_monitor.reconnect_attempts),
            'active_reconnect_tasks': len(self._reconnect_tasks),
            'health_summary': {
                'healthy': 0,
                'unhealthy': 0,
                'unknown': 0
            }
        }

        for phone in self.clients.keys():
            stats = self.health_monitor.connection_stats.get(phone, {})
            is_healthy = self.health_monitor.is_connection_healthy(phone)

            report['connection_stats'][phone] = {
                'connects': stats.get('connects', 0),
                'disconnects': stats.get('disconnects', 0),
                'errors': stats.get('errors', 0),
                'last_error': stats.get('last_error'),
                'last_connect': stats.get('last_connect'),
                'last_disconnect': stats.get('last_disconnect'),
                'is_healthy': is_healthy,
                'reconnect_attempts': self.health_monitor.reconnect_attempts.get(phone, 0)
            }

            if is_healthy:
                report['health_summary']['healthy'] += 1
            else:
                report['health_summary']['unhealthy'] += 1

        return report

    async def cleanup_stale_connections(self):
        """Очистка устаревших соединений"""
        cleaned_count = 0

        for phone in list(self.clients.keys()):
            try:
                client_data = self.clients.get(phone)
                if not client_data:
                    continue

                client = client_data['client']

                # Проверяем, подключен ли клиент
                if not client.is_connected():
                    logger.warning(f"[{phone}] Клиент не подключен, попытка переподключения...")
                    await self._reconnect_client(phone)
                    cleaned_count += 1

            except Exception as e:
                logger.error(f"[{phone}] Ошибка при очистке соединения: {e}")

        logger.info(f"Очищено {cleaned_count} устаревших соединений")
        return cleaned_count

    async def _health_monitoring_loop(self):
        """Цикл мониторинга здоровья соединений"""
        logger.info("Запущен мониторинг здоровья соединений")

        while True:
            try:
                await asyncio.sleep(self.health_monitor.health_check_interval)

                # Проверяем здоровье всех соединений
                unhealthy_clients = []

                for phone in list(self.clients.keys()):
                    try:
                        client_data = self.clients.get(phone)
                        if not client_data:
                            continue

                        client = client_data['client']

                        # Проверяем подключение
                        if not client.is_connected():
                            logger.warning(f"[{phone}] Клиент не подключен")
                            unhealthy_clients.append(phone)
                            continue

                        # Проверяем здоровье соединения
                        if not self.health_monitor.is_connection_healthy(phone):
                            logger.warning(f"[{phone}] Соединение нездорово")
                            unhealthy_clients.append(phone)
                            continue

                        # Обновляем время последней проверки
                        self.health_monitor.last_health_check[phone] = datetime.now()

                    except Exception as e:
                        logger.error(f"[{phone}] Ошибка проверки здоровья: {e}")
                        unhealthy_clients.append(phone)

                # Пытаемся восстановить нездоровые соединения
                for phone in unhealthy_clients:
                    if phone not in self._reconnect_tasks or self._reconnect_tasks[phone].done():
                        logger.info(f"[{phone}] Запуск автоматического восстановления соединения")
                        await self._reconnect_client(phone)

                # Логируем статистику
                if unhealthy_clients:
                    logger.info(f"Проверка здоровья: {len(unhealthy_clients)} нездоровых соединений из {len(self.clients)}")
                else:
                    logger.debug(f"Проверка здоровья: все {len(self.clients)} соединений здоровы")

            except asyncio.CancelledError:
                logger.info("Мониторинг здоровья соединений остановлен")
                break
            except Exception as e:
                logger.error(f"Ошибка в цикле мониторинга здоровья: {e}")
                # Продолжаем работу даже при ошибках
                await asyncio.sleep(60)  # Короткая пауза при ошибке

    async def stop_health_monitoring(self):
        """Остановка мониторинга здоровья соединений"""
        if self._health_monitor_task and not self._health_monitor_task.done():
            self._health_monitor_task.cancel()
            try:
                await self._health_monitor_task
            except asyncio.CancelledError:
                pass
            logger.info("Мониторинг здоровья соединений остановлен")

    async def get_detailed_connection_status(self) -> Dict[str, Any]:
        """Получить детальный статус всех соединений"""
        status = {
            'timestamp': datetime.now().isoformat(),
            'total_clients': len(self.clients),
            'monitoring_active': self._health_monitor_task is not None and not self._health_monitor_task.done(),
            'clients': {},
            'summary': {
                'connected': 0,
                'disconnected': 0,
                'monitoring': 0,
                'errors_last_hour': 0,
                'reconnecting': len(self._reconnect_tasks)
            }
        }

        current_time = datetime.now()

        for phone, client_data in self.clients.items():
            try:
                client = client_data['client']
                stats = self.health_monitor.connection_stats.get(phone, {})

                # Подсчитываем ошибки за последний час
                last_disconnect = stats.get('last_disconnect')
                errors_last_hour = 0
                if last_disconnect and isinstance(last_disconnect, datetime):
                    if (current_time - last_disconnect).seconds < 3600:
                        errors_last_hour = stats.get('errors', 0)

                client_status = {
                    'phone': phone,
                    'connected': client.is_connected() if hasattr(client, 'is_connected') else False,
                    'monitoring': client_data.get('monitoring', False),
                    'session_file': client_data.get('session_file', ''),
                    'health': {
                        'is_healthy': self.health_monitor.is_connection_healthy(phone),
                        'reconnect_attempts': self.health_monitor.reconnect_attempts.get(phone, 0),
                        'last_connect': stats.get('last_connect'),
                        'last_disconnect': stats.get('last_disconnect'),
                        'last_error': stats.get('last_error'),
                        'total_connects': stats.get('connects', 0),
                        'total_disconnects': stats.get('disconnects', 0),
                        'total_errors': stats.get('errors', 0),
                        'errors_last_hour': errors_last_hour
                    },
                    'reconnecting': phone in self._reconnect_tasks and not self._reconnect_tasks[phone].done()
                }

                status['clients'][phone] = client_status

                # Обновляем сводку
                if client_status['connected']:
                    status['summary']['connected'] += 1
                else:
                    status['summary']['disconnected'] += 1

                if client_status['monitoring']:
                    status['summary']['monitoring'] += 1

                status['summary']['errors_last_hour'] += errors_last_hour

            except Exception as e:
                logger.error(f"Ошибка получения статуса для {phone}: {e}")
                status['clients'][phone] = {
                    'phone': phone,
                    'error': str(e),
                    'connected': False,
                    'monitoring': False
                }

        return status

    async def force_reconnect_all(self) -> Dict[str, Any]:
        """Принудительное переподключение всех клиентов"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'total_clients': len(self.clients),
            'reconnect_results': {},
            'summary': {
                'success': 0,
                'failed': 0,
                'skipped': 0
            }
        }

        for phone in list(self.clients.keys()):
            try:
                # Проверяем, не выполняется ли уже переподключение
                if phone in self._reconnect_tasks and not self._reconnect_tasks[phone].done():
                    result['reconnect_results'][phone] = {
                        'status': 'skipped',
                        'reason': 'reconnection already in progress'
                    }
                    result['summary']['skipped'] += 1
                    continue

                # Запускаем переподключение
                logger.info(f"[{phone}] Принудительное переподключение")
                await self._reconnect_client(phone)

                result['reconnect_results'][phone] = {
                    'status': 'initiated',
                    'reason': 'forced reconnection started'
                }
                result['summary']['success'] += 1

            except Exception as e:
                logger.error(f"[{phone}] Ошибка принудительного переподключения: {e}")
                result['reconnect_results'][phone] = {
                    'status': 'failed',
                    'reason': str(e)
                }
                result['summary']['failed'] += 1

        logger.info(f"Принудительное переподключение: {result['summary']['success']} успешно, "
                   f"{result['summary']['failed']} ошибок, {result['summary']['skipped']} пропущено")

        return result

    async def get_error_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """Получить статистику ошибок за указанный период"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        stats = {
            'period_hours': hours,
            'cutoff_time': cutoff_time.isoformat(),
            'clients': {},
            'summary': {
                'total_errors': 0,
                'database_locked_errors': 0,
                'network_errors': 0,
                'auth_errors': 0,
                'other_errors': 0
            }
        }

        for phone, client_stats in self.health_monitor.connection_stats.items():
            client_errors = {
                'total_errors': client_stats.get('errors', 0),
                'last_error': client_stats.get('last_error'),
                'last_disconnect': client_stats.get('last_disconnect'),
                'error_types': {
                    'database_locked': 0,
                    'network': 0,
                    'auth': 0,
                    'other': 0
                }
            }

            # Классифицируем последнюю ошибку если она есть
            last_error = client_stats.get('last_error')
            if last_error:
                try:
                    # Создаем временное исключение для классификации
                    if 'database is locked' in last_error.lower():
                        client_errors['error_types']['database_locked'] = 1
                        stats['summary']['database_locked_errors'] += 1
                    elif any(keyword in last_error.lower() for keyword in ['connection', 'timeout', 'network']):
                        client_errors['error_types']['network'] = 1
                        stats['summary']['network_errors'] += 1
                    elif any(keyword in last_error.lower() for keyword in ['auth', 'unauthorized', 'key']):
                        client_errors['error_types']['auth'] = 1
                        stats['summary']['auth_errors'] += 1
                    else:
                        client_errors['error_types']['other'] = 1
                        stats['summary']['other_errors'] += 1
                except Exception:
                    client_errors['error_types']['other'] = 1
                    stats['summary']['other_errors'] += 1

            stats['clients'][phone] = client_errors
            stats['summary']['total_errors'] += client_errors['total_errors']

        return stats

    async def delete_account(self, phone: str) -> dict:
        """
        Полное удаление аккаунта из системы

        Выполняет следующие действия:
        1. Останавливает мониторинг
        2. Отключает Telethon клиент
        3. Удаляет файл сессии
        4. Удаляет запись из метаданных

        Args:
            phone (str): Номер телефона аккаунта для удаления

        Returns:
            dict: Результат операции с детальной информацией
        """
        result = {
            'success': False,
            'phone': phone,
            'steps_completed': [],
            'errors': [],
            'warnings': []
        }

        try:
            logger.info(f"[{phone}] Начинаем удаление аккаунта")

            # Получаем информацию об аккаунте
            client_data = self.clients.get(phone)
            if not client_data:
                logger.warning(f"[{phone}] Аккаунт не найден в активных клиентах")
                result['warnings'].append("Аккаунт не найден в активных клиентах")

            # Шаг 1: Остановка мониторинга
            try:
                if phone in self.clients:
                    self.clients[phone]['monitoring'] = False
                    logger.info(f"[{phone}] Мониторинг остановлен")
                    result['steps_completed'].append("Мониторинг остановлен")
                else:
                    result['warnings'].append("Мониторинг уже был неактивен")
            except Exception as e:
                error_msg = f"Ошибка остановки мониторинга: {e}"
                logger.error(f"[{phone}] {error_msg}")
                result['errors'].append(error_msg)

            # Шаг 2: Отключение Telethon клиента
            try:
                if phone in self.clients:
                    client = self.clients[phone]['client']
                    if client and client.is_connected():
                        await asyncio.wait_for(client.disconnect(), timeout=15.0)
                        logger.info(f"[{phone}] Telethon клиент отключен")
                        result['steps_completed'].append("Telethon клиент отключен")
                    else:
                        result['warnings'].append("Клиент уже был отключен")

                    # Удаляем из памяти
                    del self.clients[phone]
                    logger.info(f"[{phone}] Клиент удален из памяти")
                    result['steps_completed'].append("Клиент удален из памяти")
                else:
                    result['warnings'].append("Клиент не найден в памяти")

            except asyncio.TimeoutError:
                error_msg = "Таймаут при отключении клиента (15 сек)"
                logger.warning(f"[{phone}] {error_msg}")
                result['warnings'].append(error_msg)
                # Принудительно удаляем из памяти
                if phone in self.clients:
                    del self.clients[phone]
                    result['steps_completed'].append("Клиент принудительно удален из памяти")
            except Exception as e:
                error_msg = f"Ошибка отключения клиента: {e}"
                logger.error(f"[{phone}] {error_msg}")
                result['errors'].append(error_msg)
                # Принудительно удаляем из памяти
                if phone in self.clients:
                    del self.clients[phone]
                    result['warnings'].append("Клиент принудительно удален из памяти")

            # Шаг 3: Удаление файла сессии
            try:
                session_name = self._phone_to_session_name(phone)
                session_path = SESSIONS_DIR / f'{session_name}.session'

                if session_path.exists():
                    # Ждем немного для освобождения файла
                    await asyncio.sleep(2)

                    # Несколько попыток удаления
                    for attempt in range(5):
                        try:
                            session_path.unlink()
                            logger.info(f"[{phone}] Файл сессии удален: {session_path}")
                            result['steps_completed'].append("Файл сессии удален")
                            break
                        except PermissionError as e:
                            if attempt < 4:
                                logger.warning(f"[{phone}] Попытка {attempt + 1}: файл заблокирован, ждем...")
                                await asyncio.sleep(3)
                            else:
                                error_msg = f"Не удалось удалить файл сессии после 5 попыток: {e}"
                                logger.error(f"[{phone}] {error_msg}")
                                result['errors'].append(error_msg)
                        except Exception as e:
                            error_msg = f"Ошибка удаления файла сессии: {e}"
                            logger.error(f"[{phone}] {error_msg}")
                            result['errors'].append(error_msg)
                            break
                else:
                    result['warnings'].append("Файл сессии не найден")

            except Exception as e:
                error_msg = f"Критическая ошибка при удалении файла сессии: {e}"
                logger.error(f"[{phone}] {error_msg}")
                result['errors'].append(error_msg)

            # Шаг 4: Обновление метаданных
            try:
                self._save_accounts_metadata()
                logger.info(f"[{phone}] Метаданные аккаунтов обновлены")
                result['steps_completed'].append("Метаданные обновлены")
            except Exception as e:
                error_msg = f"Ошибка обновления метаданных: {e}"
                logger.error(f"[{phone}] {error_msg}")
                result['errors'].append(error_msg)

            # Определяем успешность операции
            critical_steps = ["Telethon клиент отключен", "Клиент удален из памяти"]
            success_count = sum(1 for step in critical_steps if step in result['steps_completed'])

            if success_count >= 1 and len(result['errors']) == 0:
                result['success'] = True
                logger.info(f"[{phone}] Аккаунт успешно удален")
            elif success_count >= 1:
                result['success'] = True  # Частичный успех
                logger.warning(f"[{phone}] Аккаунт удален с предупреждениями")
            else:
                logger.error(f"[{phone}] Не удалось удалить аккаунт")

            return result

        except Exception as e:
            error_msg = f"Критическая ошибка удаления аккаунта: {e}"
            logger.error(f"[{phone}] {error_msg}")
            result['errors'].append(error_msg)
            return result

    async def validate_system_integrity(self) -> dict:
        """
        Проверка целостности системы общих аккаунтов

        Returns:
            dict: Отчет о состоянии системы
        """
        report = {
            'status': 'healthy',
            'accounts_count': 0,
            'active_monitoring': 0,
            'session_files_found': 0,
            'metadata_entries': 0,
            'issues': [],
            'recommendations': []
        }

        try:
            # Проверяем метаданные
            metadata = {}
            if ACCOUNTS_METADATA_FILE.exists():
                with open(ACCOUNTS_METADATA_FILE, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                report['metadata_entries'] = len(metadata)
            else:
                report['issues'].append("Файл метаданных не найден")
                report['status'] = 'warning'

            # Проверяем файлы сессий
            session_files = []
            if SESSIONS_DIR.exists():
                session_files = list(SESSIONS_DIR.glob('account_*.session'))
                report['session_files_found'] = len(session_files)
            else:
                report['issues'].append("Директория сессий не найдена")
                report['status'] = 'error'

            # Проверяем соответствие метаданных и файлов сессий
            for phone, data in metadata.items():
                session_file = data.get('session_file')
                if session_file:
                    session_path = SESSIONS_DIR / session_file
                    if not session_path.exists():
                        report['issues'].append(f"Файл сессии не найден для аккаунта {phone}: {session_file}")
                        report['status'] = 'warning'

            # Проверяем активные клиенты
            report['accounts_count'] = len(self.clients)
            report['active_monitoring'] = sum(1 for client_data in self.clients.values()
                                            if client_data.get('monitoring', False))

            # Проверяем орфанные файлы сессий
            metadata_session_files = {data.get('session_file') for data in metadata.values()
                                    if data.get('session_file')}
            actual_session_files = {f.name for f in session_files}

            orphaned_files = actual_session_files - metadata_session_files
            if orphaned_files:
                report['issues'].append(f"Найдены орфанные файлы сессий: {list(orphaned_files)}")
                report['recommendations'].append("Рассмотрите возможность очистки орфанных файлов")

            # Проверяем отсутствующие файлы
            missing_files = metadata_session_files - actual_session_files
            if missing_files:
                report['issues'].append(f"Отсутствуют файлы сессий: {list(missing_files)}")
                report['status'] = 'error'

            # Рекомендации
            if report['accounts_count'] == 0:
                report['recommendations'].append("Система не содержит активных аккаунтов")

            if report['active_monitoring'] == 0 and report['accounts_count'] > 0:
                report['recommendations'].append("Мониторинг не активен ни для одного аккаунта")

            logger.info(f"Проверка целостности системы завершена: статус={report['status']}, "
                       f"аккаунтов={report['accounts_count']}, проблем={len(report['issues'])}")

            return report

        except Exception as e:
            logger.error(f"Ошибка при проверке целостности системы: {e}")
            report['status'] = 'error'
            report['issues'].append(f"Критическая ошибка: {e}")
            return report

"""
Обработчики управления стоп-словами для PM Searcher Bot

Модуль содержит полноценную систему обработчиков для интерактивного управления стоп-словами:
- Reply-обработчики для основной навигации
- Inline-обработчики для операций и пагинации  
- FSM-обработчики для ввода данных
- Вспомогательные функции для форматирования и обработки ошибок

Интегрируется с:
- StopWordsManager для бизнес-логики
- StopWordValidator для валидации данных
- StopWordsManagement состояниями FSM
- Системой клавиатур для управления стоп-словами

Автор: PM Searcher Bot System
Дата создания: 26.07.2025
"""

import logging
import os
import tempfile
from typing import List, Optional, Dict, Any
from io import BytesIO
from datetime import datetime

from aiogram import Dispatcher, types, F
from aiogram.filters import Command, StateFilter
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import default_state
from aiogram.types import ReplyKeyboardMarkup, InlineKeyboardMarkup, BufferedInputFile

# Импорты компонентов системы
from src.config.environment import env_config
from src.utils.logging import setup_logging
from src.utils.stopwords import StopWordsManager
from ..states import StopWordsManagement
from ..validators import StopWordValidator, ValidationResult
from ..keyboards import (
    stopwords_main_keyboard,
    stopwords_pagination_keyboard,
    stopword_actions_keyboard,
    search_results_keyboard,
    delete_confirmation_keyboard,
    import_confirmation_keyboard,
    export_options_keyboard,
    back_to_stopwords_keyboard,
    cancel_operation_keyboard
)

# Инициализация логгера для модуля обработчиков стоп-слов
logger = setup_logging(__name__, 'bot.log')

# Глобальные экземпляры для работы с системой
stopwords_manager = StopWordsManager()
validator = StopWordValidator()


def get_stopwords_manager() -> StopWordsManager:
    """Получение глобального экземпляра StopWordsManager"""
    return stopwords_manager


def register_stopwords_handlers(dp: Dispatcher) -> None:
    """
    Регистрация всех обработчиков управления стоп-словами в диспетчере

    Регистрирует:
    - Reply-обработчики для основной навигации
    - Inline-обработчики для операций с кнопками
    - FSM-обработчики для состояний ввода данных

    Args:
        dp (Dispatcher): Экземпляр диспетчера aiogram для регистрации обработчиков
    """
    logger.info("Регистрация обработчиков управления стоп-словами")

    # Регистрация Reply-обработчиков (основная навигация) с использованием декораторов
    @dp.message(lambda m: m.text == "Управление стоп-словами")
    async def handle_stopwords_main_menu(message: types.Message, state: FSMContext):
        await show_stopwords_main_menu(message, state)

    @dp.message(lambda m: m.text == "📋 Просмотр стоп-слов")
    async def handle_stopwords_list(message: types.Message, state: FSMContext):
        await show_stopwords_list(message, state)

    @dp.message(lambda m: m.text == "➕ Добавить стоп-слово")
    async def handle_add_stopword(message: types.Message, state: FSMContext):
        await start_add_stopword(message, state)

    @dp.message(lambda m: m.text == "🔍 Поиск стоп-слов")
    async def handle_search_stopwords(message: types.Message, state: FSMContext):
        await start_search_stopwords(message, state)

    @dp.message(lambda m: m.text == "📥 Импорт из файла")
    async def handle_import_stopwords(message: types.Message, state: FSMContext):
        await start_import_stopwords(message, state)

    @dp.message(lambda m: m.text == "📤 Экспорт в файл")
    async def handle_export_stopwords(message: types.Message, state: FSMContext):
        await start_export_stopwords(message, state)

    @dp.message(lambda m: m.text == "🔙 Назад в админку")
    async def handle_back_to_admin(message: types.Message, state: FSMContext):
        await back_to_admin_menu(message, state)

    # Регистрация Inline-обработчиков (операции с кнопками)
    @dp.callback_query(lambda c: c.data.startswith("sw_page:"))
    async def handle_pagination(callback: types.CallbackQuery, state: FSMContext):
        logger.info(f"🔍 DEBUG: Сработал обработчик sw_page для пользователя {callback.from_user.id}")
        await handle_stopwords_pagination(callback, state)

    @dp.callback_query(lambda c: c.data.startswith("sw_action:"))
    async def handle_actions(callback: types.CallbackQuery, state: FSMContext):
        logger.info(f"🔍 DEBUG: Сработал обработчик sw_action для пользователя {callback.from_user.id}")
        await handle_stopword_actions(callback, state)

    @dp.callback_query(lambda c: c.data.startswith("sw_delete:"))
    async def handle_delete(callback: types.CallbackQuery, state: FSMContext):
        logger.info(f"🔍 DEBUG: Сработал обработчик sw_delete для пользователя {callback.from_user.id}")
        await handle_delete_confirmation(callback, state)

    @dp.callback_query(lambda c: c.data.startswith("sw_import:"))
    async def handle_import(callback: types.CallbackQuery, state: FSMContext):
        logger.info(f"🔍 DEBUG: Сработал обработчик sw_import для пользователя {callback.from_user.id}")
        await handle_import_confirmation(callback, state)

    @dp.callback_query(lambda c: c.data.startswith("sw_export:"))
    async def handle_export(callback: types.CallbackQuery, state: FSMContext):
        logger.info(f"🔍 DEBUG: Сработал обработчик sw_export для пользователя {callback.from_user.id}")
        await handle_export_options(callback, state)

    @dp.callback_query(lambda c: c.data == "cancel_current_operation")
    async def handle_cancel_operation(callback: types.CallbackQuery, state: FSMContext):
        logger.info(f"🔍 DEBUG: Сработал обработчик отмены операции для пользователя {callback.from_user.id}")
        await handle_cancel_current_operation(callback, state)

    @dp.callback_query(lambda c: c.data == "back_to_stopwords_main")
    async def handle_back_to_stopwords_main(callback: types.CallbackQuery, state: FSMContext):
        logger.info(f"🔍 DEBUG: Сработал обработчик возврата к главному меню стоп-слов для пользователя {callback.from_user.id}")
        await handle_back_to_stopwords_main_menu(callback, state)

    @dp.message(StopWordsManagement.waiting_for_stopword_selection)
    async def handle_stopword_selection(message: types.Message, state: FSMContext):
        """Обработчик выбора стоп-слова по номеру"""
        logger.info(f"🔍 DEBUG: Сработал обработчик выбора стоп-слова для пользователя {message.from_user.id}")
        await handle_stopword_number_selection(message, state)

    @dp.callback_query(lambda c: c.data.startswith("sw_action:copy:"))
    async def handle_copy_stopword(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик копирования стоп-слова"""
        logger.info(f"🔍 DEBUG: Сработал обработчик копирования стоп-слова для пользователя {callback.from_user.id}")
        await handle_copy_stopword_action(callback, state)

    @dp.callback_query(lambda c: c.data.startswith("sw_action:find_similar:"))
    async def handle_find_similar_stopwords(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик поиска похожих стоп-слов"""
        logger.info(f"🔍 DEBUG: Сработал обработчик поиска похожих стоп-слов для пользователя {callback.from_user.id}")
        await handle_find_similar_stopwords_action(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_action:cancel")
    async def handle_cancel_stopword_action(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик отмены действия со стоп-словом"""
        logger.info(f"🔍 DEBUG: Сработал обработчик отмены действия со стоп-словом для пользователя {callback.from_user.id}")
        await handle_cancel_stopword_action_handler(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_start_search")
    async def handle_start_search_from_pagination(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик кнопки поиска из пагинации"""
        logger.info(f"🔍 DEBUG: Сработал обработчик кнопки поиска из пагинации для пользователя {callback.from_user.id}")
        await handle_start_search_from_pagination_action(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_add_new")
    async def handle_add_new_from_pagination(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик кнопки добавления из пагинации"""
        logger.info(f"🔍 DEBUG: Сработал обработчик кнопки добавления из пагинации для пользователя {callback.from_user.id}")
        await handle_add_new_from_pagination_action(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_page_info")
    async def handle_page_info(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик информации о странице"""
        logger.info(f"🔍 DEBUG: Сработал обработчик информации о странице для пользователя {callback.from_user.id}")
        await handle_page_info_action(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_new_search")
    async def handle_new_search(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик нового поиска"""
        logger.info(f"🔍 DEBUG: Сработал обработчик нового поиска для пользователя {callback.from_user.id}")
        await handle_new_search_action(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_export_search_results")
    async def handle_export_search_results(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик экспорта результатов поиска"""
        logger.info(f"🔍 DEBUG: Сработал обработчик экспорта результатов поиска для пользователя {callback.from_user.id}")
        await handle_export_search_results_action(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_delete_search_results")
    async def handle_delete_search_results(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик удаления всех найденных стоп-слов"""
        logger.info(f"🔍 DEBUG: Сработал обработчик удаления всех найденных стоп-слов для пользователя {callback.from_user.id}")
        await handle_delete_search_results_action(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_show_all_stopwords")
    async def handle_show_all_stopwords(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик показа всех стоп-слов"""
        logger.info(f"🔍 DEBUG: Сработал обработчик показа всех стоп-слов для пользователя {callback.from_user.id}")
        await handle_show_all_stopwords_action(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_confirm_mass_delete")
    async def handle_confirm_mass_delete(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик подтверждения массового удаления"""
        logger.info(f"🔍 DEBUG: Сработал обработчик подтверждения массового удаления для пользователя {callback.from_user.id}")
        await handle_confirm_mass_delete_action(callback, state)

    @dp.callback_query(lambda c: c.data == "sw_cancel_mass_delete")
    async def handle_cancel_mass_delete(callback: types.CallbackQuery, state: FSMContext):
        """Обработчик отмены массового удаления"""
        logger.info(f"🔍 DEBUG: Сработал обработчик отмены массового удаления для пользователя {callback.from_user.id}")
        await handle_cancel_mass_delete_action(callback, state)

    # Регистрация FSM-обработчиков (ввод данных в состояниях)
    @dp.message(StopWordsManagement.waiting_for_new_stopword)
    async def handle_new_stopword(message: types.Message, state: FSMContext):
        await process_new_stopword(message, state)

    @dp.message(StopWordsManagement.waiting_for_search_query)
    async def handle_search_query(message: types.Message, state: FSMContext):
        await process_search_query(message, state)

    @dp.message(StopWordsManagement.waiting_for_import_file)
    async def handle_import_file(message: types.Message, state: FSMContext):
        await process_import_file(message, state)

    @dp.message(StopWordsManagement.waiting_for_export_filename)
    async def handle_export_filename(message: types.Message, state: FSMContext):
        await process_export_filename(message, state)

    logger.info("Все обработчики управления стоп-словами успешно зарегистрированы")


# ============================================================================
# REPLY-ОБРАБОТЧИКИ (ОСНОВНАЯ НАВИГАЦИЯ)
# ============================================================================

async def show_stopwords_main_menu(message: types.Message, state: FSMContext) -> None:
    """
    Показ главного меню управления стоп-словами

    Отображает основное меню с функциями управления стоп-словами и статистикой.
    Доступ только для администраторов.

    Args:
        message (types.Message): Сообщение пользователя
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id

    # Проверка прав администратора
    from src.config.roles_manager import roles_manager
    if not roles_manager.is_admin(user_id):
        logger.warning(f"Попытка доступа к управлению стоп-словами от неадминистратора: {user_id}")
        await message.answer("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
        return
    
    # Очистка состояния FSM при входе в главное меню
    await state.clear()
    
    try:
        # Получение статистики стоп-слов
        stats = stopwords_manager.get_detailed_stats()
        
        # Формирование текста меню с актуальной статистикой
        menu_text = (
            f"📝 Управление стоп-словами\n\n"
            f"📊 Текущая статистика:\n"
            f"• Всего стоп-слов: {stats['total_count']}\n"
            f"• Средняя длина: {stats['average_length']:.1f} символов\n"
            f"• Размер файла: {stats['file_info']['size_formatted']}\n\n"
            f"🔧 Доступные операции:\n"
            f"• Просмотр всех стоп-слов с пагинацией\n"
            f"• Добавление новых стоп-слов с валидацией\n"
            f"• Поиск по существующим стоп-словам\n"
            f"• Импорт/экспорт списков стоп-слов\n\n"
            f"👇 Выберите действие из меню ниже:"
        )

        await message.answer(
            menu_text,
            reply_markup=stopwords_main_keyboard()
        )
        
        logger.info(f"Администратор {user_id} открыл главное меню управления стоп-словами")
        
    except Exception as e:
        logger.error(f"Ошибка при показе главного меню стоп-слов: {e}")
        await handle_errors(message, "Ошибка загрузки меню управления стоп-словами")


async def show_stopwords_list(message: types.Message, state: FSMContext) -> None:
    """
    Просмотр списка стоп-слов с пагинацией
    
    Отображает первую страницу списка всех стоп-слов с возможностью навигации.
    Переводит пользователя в состояние browsing_stopwords для интерактивной работы.
    
    Args:
        message (types.Message): Сообщение пользователя
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id
    
    # Проверка прав администратора
    if not env_config.is_admin(user_id):
        await message.answer("❌ Доступ запрещен.")
        return
    
    try:
        # Переход в состояние ожидания выбора стоп-слова
        await state.set_state(StopWordsManagement.waiting_for_stopword_selection)
        
        # Отображение первой страницы
        page_data = format_stopwords_page(1)
        
        if page_data['has_words']:
            if page_data['keyboard']:
                await message.answer(
                    page_data['text'],
                    parse_mode='Markdown',
                    reply_markup=page_data['keyboard']
                )
            else:
                await message.answer(
                    page_data['text'],
                    parse_mode='Markdown'
                )
            
            # Сохранение текущей страницы в состоянии
            await state.update_data(current_page=1, search_query=None)
            
            logger.info(f"Пользователь {user_id} начал просмотр стоп-слов, страница 1")
        else:
            await message.answer(
                "📋 **Список стоп-слов пуст**\n\n"
                "Добавьте первое стоп-слово используя кнопку \"➕ Добавить стоп-слово\"",
                parse_mode='Markdown'
            )
            
    except Exception as e:
        logger.error(f"Ошибка при показе списка стоп-слов: {e}")
        await handle_errors(message, "Ошибка загрузки списка стоп-слов")


async def start_add_stopword(message: types.Message, state: FSMContext) -> None:
    """
    Начало процесса добавления нового стоп-слова
    
    Переводит пользователя в состояние ожидания ввода нового стоп-слова
    с инструкциями по вводу и требованиями к формату.
    
    Args:
        message (types.Message): Сообщение пользователя
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id
    
    # Проверка прав администратора
    if not env_config.is_admin(user_id):
        await message.answer("❌ Доступ запрещен.")
        return
    
    # Переход в состояние ожидания ввода нового стоп-слова
    await state.set_state(StopWordsManagement.waiting_for_new_stopword)
    
    instructions_text = (
        f"➕ Добавление нового стоп-слова\n\n"
        f"📝 Введите стоп-слово для добавления:\n\n"
        f"✅ Требования к стоп-слову:\n"
        f"• Длина: от 2 до 50 символов (исключение: символ @ разрешен как одиночное стоп-слово)\n"
        f"• Разрешены: буквы, цифры, дефисы, подчеркивания, точки, звездочки, символ @ и другие безопасные символы\n"
        f"• Автоматическое приведение к нижнему регистру\n"
        f"• Проверка на дубликаты\n\n"
        f"💡 Примеры: спам, реклама, bad-word, test123, @, l_866_4656*fhfg\n\n"
        f"❌ Для отмены используйте кнопку ниже"
    )

    await message.answer(
        instructions_text,
        reply_markup=cancel_operation_keyboard()
    )
    
    logger.info(f"Пользователь {user_id} начал процесс добавления стоп-слова")


async def start_search_stopwords(message: types.Message, state: FSMContext) -> None:
    """
    Начало процесса поиска стоп-слов
    
    Переводит пользователя в состояние ожидания поискового запроса
    с инструкциями по использованию поиска.
    
    Args:
        message (types.Message): Сообщение пользователя
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id
    
    # Проверка прав администратора
    if not env_config.is_admin(user_id):
        await message.answer("❌ Доступ запрещен.")
        return
    
    # Переход в состояние ожидания поискового запроса
    await state.set_state(StopWordsManagement.waiting_for_search_query)
    
    search_instructions = (
        f"🔍 Поиск стоп-слов\n\n"
        f"📝 Введите запрос для поиска:\n\n"
        f"🔍 Возможности поиска:\n"
        f"• Поиск по подстроке: спам найдет спам, антиспам\n"
        f"• Поиск точного совпадения: введите полное слово\n"
        f"• Регистр не важен: СПАМ = спам\n"
        f"• Минимальная длина запроса: 1 символ\n\n"
        f"💡 Примеры запросов:\n"
        f"• рекл - найдет все слова содержащие 'рекл'\n"
        f"• test - найдет все слова содержащие 'test'\n"
        f"• а - найдет все слова с буквой 'а'\n\n"
        f"❌ Для отмены используйте кнопку ниже"
    )

    await message.answer(
        search_instructions,
        reply_markup=cancel_operation_keyboard()
    )
    
    logger.info(f"Пользователь {user_id} начал процесс поиска стоп-слов")


async def start_import_stopwords(message: types.Message, state: FSMContext) -> None:
    """
    Начало процесса импорта стоп-слов из файла

    Переводит пользователя в состояние ожидания файла для импорта
    с подробными инструкциями по формату файла.

    Args:
        message (types.Message): Сообщение пользователя
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id

    # Проверка прав администратора
    if not env_config.is_admin(user_id):
        await message.answer("❌ Доступ запрещен.")
        return

    # Переход в состояние ожидания файла для импорта
    await state.set_state(StopWordsManagement.waiting_for_import_file)

    import_instructions = (
        f"📥 **Импорт стоп-слов из файла**\n\n"
        f"📄 **Отправьте текстовый файл (.txt) со стоп-словами**\n\n"
        f"📋 **Требования к файлу:**\n"
        f"• Максимальный размер: 1 МБ\n"
        f"• Максимум стоп-слов: 1000\n"
        f"• Формат: одно слово на строку\n"
        f"• Поддержка комментариев (строки начинающиеся с #)\n"
        f"• Автоматическая фильтрация дубликатов\n\n"
        f"📝 **Пример содержимого файла:**\n"
        f"```\n"
        f"# Список стоп-слов\n"
        f"спам\n"
        f"реклама\n"
        f"мошенники\n"
        f"```\n\n"
        f"⚠️ **Важно:** Перед импортом автоматически создается резервная копия\n\n"
        f"❌ Для отмены используйте кнопку ниже"
    )

    await message.answer(
        import_instructions,
        parse_mode='Markdown',
        reply_markup=cancel_operation_keyboard()
    )

    logger.info(f"Пользователь {user_id} начал процесс импорта стоп-слов")


async def start_export_stopwords(message: types.Message, state: FSMContext) -> None:
    """
    Начало процесса экспорта стоп-слов в файл

    Переводит пользователя в состояние ожидания имени файла для экспорта
    с вариантами экспорта и инструкциями.

    Args:
        message (types.Message): Сообщение пользователя
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id

    # Проверка прав администратора
    if not env_config.is_admin(user_id):
        await message.answer("❌ Доступ запрещен.")
        return

    try:
        # Получение статистики для отображения в меню экспорта
        stats = stopwords_manager.get_detailed_stats()

        export_menu_text = (
            f"📤 **Экспорт стоп-слов в файл**\n\n"
            f"📊 **Доступно для экспорта:**\n"
            f"• Всего стоп-слов: **{stats['total_count']}**\n"
            f"• Размер данных: **{stats['file_info']['size_formatted']}**\n\n"
            f"📋 **Выберите тип экспорта:**\n"
            f"• Все стоп-слова - полный экспорт всех записей\n"
            f"• Результаты поиска - только найденные слова (если есть)\n\n"
            f"📄 **Формат файла:** текстовый (.txt) с комментариями и метаданными\n\n"
            f"👇 Выберите вариант экспорта:"
        )

        await message.answer(
            export_menu_text,
            parse_mode='Markdown',
            reply_markup=export_options_keyboard()
        )

        logger.info(f"Пользователь {user_id} открыл меню экспорта стоп-слов")

    except Exception as e:
        logger.error(f"Ошибка при показе меню экспорта: {e}")
        await handle_errors(message, "Ошибка загрузки меню экспорта")


# ============================================================================
# INLINE-ОБРАБОТЧИКИ (ОПЕРАЦИИ С КНОПКАМИ)
# ============================================================================

async def handle_stopwords_pagination(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка навигации по страницам списка стоп-слов

    Обрабатывает нажатия на кнопки пагинации (◀️ Пред / След ▶️)
    и обновляет отображаемую страницу списка стоп-слов.

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()  # Убираем индикатор загрузки

    try:
        # Извлечение номера страницы из callback_data: "sw_page:3"
        page = int(callback.data.split(":")[1])

        # Получение данных о текущем состоянии поиска
        data = await state.get_data()
        search_query = data.get('search_query')

        # Форматирование страницы (обычный список или результаты поиска)
        if search_query:
            page_data = format_search_results(search_query, page)
        else:
            page_data = format_stopwords_page(page)

        # Обновление сообщения с новой страницей
        if page_data['has_words']:
            if page_data['keyboard']:
                await callback.message.edit_text(
                    page_data['text'],
                    parse_mode='Markdown',
                    reply_markup=page_data['keyboard']
                )
            else:
                await callback.message.edit_text(
                    page_data['text'],
                    parse_mode='Markdown'
                )

            # Обновление номера текущей страницы в состоянии
            await state.update_data(current_page=page)

            logger.info(f"Пользователь перешел на страницу {page} {'поиска' if search_query else 'списка стоп-слов'}")
        else:
            await callback.message.edit_text(
                "📋 Стоп-слова не найдены"
            )

    except (ValueError, IndexError) as e:
        logger.error(f"Ошибка парсинга данных пагинации: {e}")
        await callback.message.edit_text(
            "❌ Ошибка навигации по страницам"
        )
    except Exception as e:
        logger.error(f"Ошибка в обработке пагинации: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке страницы"
        )


async def handle_stopword_actions(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка действий с конкретным стоп-словом

    Обрабатывает выбор действия со стоп-словом (удаление, редактирование и т.д.)
    и отображает соответствующее меню подтверждения.

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        # Парсинг callback_data: "sw_action:delete:слово"
        parts = callback.data.split(":", 2)
        action = parts[1]
        stopword = parts[2]

        if action == "delete":
            # Отображение подтверждения удаления
            confirmation_text = (
                f"🗑️ **Подтверждение удаления**\n\n"
                f"Вы действительно хотите удалить стоп-слово:\n"
                f"**\"{stopword}\"**\n\n"
                f"⚠️ Это действие нельзя отменить!"
            )

            await callback.message.edit_text(
                confirmation_text,
                parse_mode='Markdown',
                reply_markup=delete_confirmation_keyboard(stopword)
            )

            logger.info(f"Запрос подтверждения удаления стоп-слова: {stopword}")

        else:
            logger.warning(f"Неизвестное действие со стоп-словом: {action}")
            await callback.message.edit_text(
                "❌ Неизвестное действие",
                reply_markup=back_to_stopwords_keyboard()
            )

    except (ValueError, IndexError) as e:
        logger.error(f"Ошибка парсинга действия со стоп-словом: {e}")
        await callback.message.edit_text(
            "❌ Ошибка обработки действия",
            reply_markup=back_to_stopwords_keyboard()
        )
    except Exception as e:
        logger.error(f"Ошибка в обработке действий со стоп-словом: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при выполнении действия",
            reply_markup=back_to_stopwords_keyboard()
        )


async def handle_delete_confirmation(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка подтверждения удаления стоп-слова

    Выполняет окончательное удаление стоп-слова после подтверждения пользователя
    или отменяет операцию при отказе.

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        # Парсинг callback_data: "sw_delete:confirm:слово" или "sw_delete:cancel"
        parts = callback.data.split(":", 2)
        action = parts[1]

        if action == "confirm":
            stopword = parts[2]

            # Попытка удаления стоп-слова
            success = stopwords_manager.remove_stopword(stopword)

            if success:
                # Успешное удаление
                success_text = (
                    f"✅ **Стоп-слово удалено успешно**\n\n"
                    f"Удалено: **\"{stopword}\"**\n\n"
                    f"📊 Текущее количество стоп-слов: **{len(stopwords_manager.stopwords)}**"
                )

                await callback.message.edit_text(
                    success_text,
                    parse_mode='Markdown'
                )

                logger.info(f"Стоп-слово '{stopword}' успешно удалено")

                # Обновление валидатора
                validator.update_existing_stopwords(set(stopwords_manager.stopwords))

            else:
                # Ошибка удаления
                await callback.message.edit_text(
                    f"❌ **Ошибка удаления**\n\n"
                    f"Не удалось удалить стоп-слово: **\"{stopword}\"**\n"
                    f"Возможно, слово уже было удалено.",
                    parse_mode='Markdown',
                    reply_markup=back_to_stopwords_keyboard()
                )

                logger.warning(f"Не удалось удалить стоп-слово: {stopword}")

        elif action == "cancel":
            # Отмена удаления
            await callback.message.edit_text(
                "❌ **Удаление отменено**\n\n"
                "Стоп-слово не было удалено.",
                parse_mode='Markdown'
            )

            logger.info("Пользователь отменил удаление стоп-слова")

        else:
            logger.warning(f"Неизвестное действие подтверждения: {action}")
            await callback.message.edit_text(
                "❌ Ошибка обработки подтверждения",
                reply_markup=back_to_stopwords_keyboard()
            )

    except (ValueError, IndexError) as e:
        logger.error(f"Ошибка парсинга подтверждения удаления: {e}")
        await callback.message.edit_text(
            "❌ Ошибка обработки подтверждения",
            reply_markup=back_to_stopwords_keyboard()
        )
    except Exception as e:
        logger.error(f"Ошибка в обработке подтверждения удаления: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при удалении стоп-слова",
            reply_markup=back_to_stopwords_keyboard()
        )


async def handle_import_confirmation(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка подтверждения импорта стоп-слов

    Выполняет окончательный импорт стоп-слов после подтверждения пользователя
    или отменяет операцию при отказе.

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        # Парсинг callback_data: "sw_import:confirm" или "sw_import:cancel"
        action = callback.data.split(":")[1]

        if action == "confirm":
            # Получение данных импорта из состояния
            data = await state.get_data()
            import_data = data.get('import_data')

            if not import_data:
                await callback.message.edit_text(
                    "❌ Данные для импорта не найдены",
                    reply_markup=back_to_stopwords_keyboard()
                )
                return

            # Создание резервной копии перед импортом
            backup_file = stopwords_manager.create_backup()

            # Выполнение импорта
            results = stopwords_manager.import_from_file(import_data['filepath'])

            # Формирование отчета о результатах импорта
            result_text = (
                f"✅ **Импорт завершен успешно**\n\n"
                f"📊 **Результаты импорта:**\n"
                f"• Добавлено новых слов: **{results['added']}**\n"
                f"• Пропущено дубликатов: **{results['skipped']}**\n"
                f"• Всего обработано: **{results['total_processed']}**\n\n"
                f"📄 Резервная копия: `{backup_file}`\n\n"
                f"📈 Общее количество стоп-слов: **{len(stopwords_manager.stopwords)}**"
            )

            await callback.message.edit_text(
                result_text,
                parse_mode='Markdown'
            )

            logger.info(f"Импорт завершен: добавлено {results['added']}, пропущено {results['skipped']}")

            # Обновление валидатора новыми стоп-словами
            validator.update_existing_stopwords(set(stopwords_manager.stopwords))

            # Очистка временных данных
            await state.update_data(import_data=None)

        elif action == "cancel":
            # Отмена импорта
            await callback.message.edit_text(
                "❌ **Импорт отменен**\n\n"
                "Файл не был импортирован, изменения не внесены.",
                parse_mode='Markdown'
            )

            # Очистка временных данных
            data = await state.get_data()
            import_data = data.get('import_data')
            if import_data and import_data.get('filepath'):
                # Удаление временного файла
                try:
                    os.remove(import_data['filepath'])
                except:
                    pass
            await state.update_data(import_data=None)

            logger.info("Пользователь отменил импорт стоп-слов")

    except (ValueError, IndexError) as e:
        logger.error(f"Ошибка парсинга подтверждения импорта: {e}")
        await callback.message.edit_text(
            "❌ Ошибка обработки подтверждения",
            reply_markup=back_to_stopwords_keyboard()
        )
    except Exception as e:
        logger.error(f"Ошибка в обработке подтверждения импорта: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при импорте стоп-слов",
            reply_markup=back_to_stopwords_keyboard()
        )


async def handle_export_options(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка выбора опций экспорта стоп-слов

    Обрабатывает выбор типа экспорта (все слова или результаты поиска)
    и переводит в состояние ввода имени файла.

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    logger.info(f"🔍 DEBUG: Получен callback экспорта - user_id: {callback.from_user.id}, data: '{callback.data}'")
    await callback.answer()

    try:
        # Парсинг callback_data: "sw_export:all" или "sw_export:search"
        export_type = callback.data.split(":")[1]
        logger.info(f"🔍 DEBUG: Тип экспорта: {export_type}")

        if export_type == "all":
            # Экспорт всех стоп-слов
            await state.update_data(export_type="all")

            filename_text = (
                f"📤 Экспорт всех стоп-слов\n\n"
                f"📝 Введите имя файла для экспорта:\n\n"
                f"📋 Требования к имени файла:\n"
                f"• Только буквы, цифры, дефисы и подчеркивания\n"
                f"• Длина: от 1 до 50 символов\n"
                f"• Расширение .txt добавится автоматически\n\n"
                f"💡 Примеры: stopwords, backup-2025, my_list\n\n"
                f"❌ Для отмены используйте кнопку ниже"
            )

        elif export_type == "search":
            # Экспорт результатов поиска
            data = await state.get_data()
            search_query = data.get('search_query')

            if not search_query:
                await callback.message.edit_text(
                    "❌ **Нет результатов поиска для экспорта**\n\n"
                    "Сначала выполните поиск стоп-слов, затем экспортируйте результаты.",
                    parse_mode='Markdown',
                    reply_markup=back_to_stopwords_keyboard()
                )
                return

            await state.update_data(export_type="search")

            filename_text = (
                f"📤 Экспорт результатов поиска\n\n"
                f"📝 Введите имя файла для экспорта:\n\n"
                f"📋 Требования к имени файла:\n"
                f"• Только буквы, цифры, дефисы и подчеркивания\n"
                f"• Длина: от 1 до 50 символов\n"
                f"• Расширение .txt добавится автоматически\n\n"
                f"💡 Примеры: search_results, found_words\n\n"
                f"❌ Для отмены используйте кнопку ниже"
            )

        elif export_type == "cancel":
            # Отмена экспорта
            await callback.message.edit_text(
                "❌ Экспорт отменен\n\n"
                "Операция экспорта была отменена."
            )
            return

        else:
            logger.warning(f"Неизвестный тип экспорта: {export_type}")
            await callback.message.edit_text(
                "❌ Неизвестный тип экспорта",
                reply_markup=back_to_stopwords_keyboard()
            )
            return

        # Переход в состояние ввода имени файла
        await state.set_state(StopWordsManagement.waiting_for_export_filename)

        await callback.message.edit_text(
            filename_text,
            reply_markup=cancel_operation_keyboard()
        )

        logger.info(f"Пользователь выбрал тип экспорта: {export_type}")

    except (ValueError, IndexError) as e:
        logger.error(f"Ошибка парсинга опций экспорта: {e}")
        await callback.message.edit_text(
            "❌ Ошибка обработки опций экспорта",
            reply_markup=back_to_stopwords_keyboard()
        )
    except Exception as e:
        logger.error(f"Ошибка в обработке опций экспорта: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при выборе опций экспорта",
            reply_markup=back_to_stopwords_keyboard()
        )


async def handle_cancel_current_operation(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка отмены текущей операции

    Универсальный обработчик для отмены любой текущей операции.
    Очищает состояние FSM и возвращает пользователя в главное меню стоп-слов.

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки отмены
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        # Получаем текущее состояние для логирования
        current_state = await state.get_state()
        logger.info(f"Отмена операции в состоянии: {current_state}")

        # Очищаем состояние FSM
        await state.clear()

        # Возвращаем в главное меню стоп-слов без кнопки
        await callback.message.edit_text(
            "❌ Операция отменена\n\n"
            "Вы вернулись в главное меню управления стоп-словами."
        )

        logger.info(f"Пользователь {callback.from_user.id} отменил операцию")

    except Exception as e:
        logger.error(f"Ошибка при отмене операции: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при отмене операции"
        )


async def handle_back_to_stopwords_main_menu(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка возврата в главное меню управления стоп-словами

    Очищает состояние FSM и показывает главное меню управления стоп-словами.

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        # Очищаем состояние FSM
        await state.clear()

        # Показываем главное меню стоп-слов
        user_id = callback.from_user.id

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка доступа к управлению стоп-словами от неадминистратора: {user_id}")
            await callback.message.edit_text("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
            return

        # Получение статистики стоп-слов
        total_stopwords = len(stopwords_manager.stopwords)

        menu_text = (
            f"🛠️ **Управление стоп-словами**\n\n"
            f"📊 **Текущая статистика:**\n"
            f"• Всего стоп-слов: **{total_stopwords}**\n\n"
            f"🔧 **Доступные функции:**\n"
            f"• 📋 Просмотр всех стоп-слов\n"
            f"• ➕ Добавление новых стоп-слов\n"
            f"• 🔍 Поиск по стоп-словам\n"
            f"• 📥 Импорт из файла\n"
            f"• 📤 Экспорт в файл\n\n"
            f"💡 Используйте кнопки меню для навигации"
        )

        await callback.message.edit_text(
            menu_text,
            parse_mode='Markdown',
            reply_markup=stopwords_main_keyboard()
        )

        logger.info(f"Пользователь {user_id} вернулся в главное меню стоп-слов")

    except Exception as e:
        logger.error(f"Ошибка при возврате в главное меню стоп-слов: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при возврате в меню"
        )


async def back_to_admin_menu(message: types.Message, state: FSMContext) -> None:
    """
    Обработка кнопки "🔙 Назад в админку"

    Возвращает пользователя в главное административное меню,
    очищая состояние FSM управления стоп-словами.

    Args:
        message (types.Message): Сообщение пользователя
        state (FSMContext): Контекст состояния FSM
    """
    try:
        user_id = message.from_user.id

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка доступа к админке от неадминистратора: {user_id}")
            await message.answer("❌ Доступ запрещен. Только администратор может использовать эту функцию.")
            return

        # Получаем текущее состояние для логирования
        current_state = await state.get_state()
        logger.info(f"Возврат в админку из состояния: {current_state}")

        # Очищаем состояние FSM
        await state.clear()

        # Возвращаем в главное административное меню
        from ..keyboards import get_keyboard

        welcome_text = (
            "👑 **Административная панель**\n\n"
            "Вы вернулись в главное меню администратора.\n\n"
            "Доступные функции:\n"
            "• Подключить аккаунт\n"
            "• Мои аккаунты\n"
            "• Управление стоп-словами\n"
            "• Все подключенные аккаунты\n"
            "• Статистика"
        )

        await message.answer(
            welcome_text,
            parse_mode='Markdown',
            reply_markup=get_keyboard(user_id)
        )

        logger.info(f"Пользователь {user_id} вернулся в административную панель")

    except Exception as e:
        logger.error(f"Ошибка при возврате в админку: {e}")
        await message.answer(
            "❌ Ошибка при возврате в административную панель"
        )


async def handle_stopword_number_selection(message: types.Message, state: FSMContext) -> None:
    """
    Обработка выбора стоп-слова по номеру для выполнения действий

    Args:
        message (types.Message): Сообщение с номером стоп-слова
        state (FSMContext): Контекст состояния FSM
    """
    try:
        user_id = message.from_user.id
        user_input = message.text.strip()

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка выбора стоп-слова от неадминистратора: {user_id}")
            await message.answer("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
            return

        # Игнорируем команды и кнопки, которые должны обрабатываться другими обработчиками
        ignored_inputs = [
            "🔙 Назад в админку",
            "📋 Просмотр стоп-слов",
            "➕ Добавить стоп-слово",
            "🔍 Поиск стоп-слов",
            "📥 Импорт из файла",
            "📤 Экспорт в файл",
            "Управление стоп-словами",
            "Подключить аккаунт",
            "Мои аккаунты",
            "Все подключенные аккаунты",
            "Статистика"
        ]

        if user_input in ignored_inputs or user_input.startswith('/'):
            logger.info(f"Игнорируем ввод '{user_input}' в состоянии waiting_for_stopword_selection")
            return

        # Получение данных из состояния
        data = await state.get_data()
        current_page = data.get('current_page', 1)
        search_query = data.get('search_query')

        # Парсинг номера стоп-слова
        try:
            stopword_number = int(user_input)
        except ValueError:
            await message.answer(
                "❌ **Неверный формат**\n\n"
                "Введите номер стоп-слова (число), например: 1\n\n"
                "💡 Или используйте кнопки меню для навигации.",
                parse_mode='Markdown'
            )
            return

        # Получение списка стоп-слов для текущей страницы
        if search_query:
            page_data = format_search_results_page(search_query, current_page)
        else:
            page_data = format_stopwords_page(current_page)

        # Проверка валидности номера
        if not page_data['has_words']:
            await message.answer("❌ Список стоп-слов пуст")
            return

        # Вычисление индекса стоп-слова
        per_page = 10
        start_index = (current_page - 1) * per_page + 1
        end_index = start_index + len(page_data['page_data']['words']) - 1

        if stopword_number < start_index or stopword_number > end_index:
            await message.answer(
                f"❌ **Неверный номер**\n\n"
                f"На текущей странице доступны стоп-слова с номерами {start_index}-{end_index}",
                parse_mode='Markdown'
            )
            return

        # Получение выбранного стоп-слова
        word_index = stopword_number - start_index
        selected_word = page_data['page_data']['words'][word_index]

        # Отображение меню действий для выбранного стоп-слова
        await message.answer(
            f"🎯 **Выбрано стоп-слово:** `{selected_word}`\n\n"
            f"Выберите действие:",
            parse_mode='Markdown',
            reply_markup=stopword_actions_keyboard(selected_word)
        )

        # Сохранение выбранного слова в состоянии
        await state.update_data(selected_stopword=selected_word)

        logger.info(f"Пользователь {user_id} выбрал стоп-слово: {selected_word}")

    except Exception as e:
        logger.error(f"Ошибка при выборе стоп-слова: {e}")
        await message.answer(
            "❌ Ошибка при выборе стоп-слова"
        )


async def handle_copy_stopword_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка копирования стоп-слова в буфер обмена

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        # Извлечение стоп-слова из callback_data
        word = callback.data.split(":", 2)[2]

        await callback.message.edit_text(
            f"📋 **Стоп-слово скопировано**\n\n"
            f"Слово `{word}` готово для вставки.\n\n"
            f"💡 Вы можете использовать его в других приложениях.",
            parse_mode='Markdown'
        )

        logger.info(f"Пользователь {callback.from_user.id} скопировал стоп-слово: {word}")

    except Exception as e:
        logger.error(f"Ошибка при копировании стоп-слова: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при копировании стоп-слова"
        )


async def handle_find_similar_stopwords_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка поиска похожих стоп-слов

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        # Извлечение стоп-слова из callback_data
        word = callback.data.split(":", 2)[2]

        # Поиск похожих стоп-слов (простая реализация по подстроке)
        all_stopwords = list(stopwords_manager.stopwords)
        similar_words = [w for w in all_stopwords if word.lower() in w.lower() or w.lower() in word.lower()]
        similar_words = [w for w in similar_words if w != word]  # Исключаем само слово

        if similar_words:
            similar_text = "\n".join([f"• `{w}`" for w in similar_words[:10]])  # Показываем максимум 10
            result_text = (
                f"🔍 **Похожие стоп-слова для:** `{word}`\n\n"
                f"{similar_text}"
            )
            if len(similar_words) > 10:
                result_text += f"\n\n... и еще {len(similar_words) - 10} слов"
        else:
            result_text = (
                f"🔍 **Поиск завершен**\n\n"
                f"Похожих стоп-слов для `{word}` не найдено."
            )

        await callback.message.edit_text(
            result_text,
            parse_mode='Markdown'
        )

        logger.info(f"Пользователь {callback.from_user.id} искал похожие слова для: {word}")

    except Exception as e:
        logger.error(f"Ошибка при поиске похожих стоп-слов: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при поиске похожих стоп-слов"
        )


async def handle_cancel_stopword_action_handler(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка отмены действия со стоп-словом

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        # Возврат к состоянию выбора стоп-слова
        await state.set_state(StopWordsManagement.waiting_for_stopword_selection)

        # Получение данных из состояния
        data = await state.get_data()
        current_page = data.get('current_page', 1)
        search_query = data.get('search_query')

        # Отображение текущей страницы стоп-слов
        if search_query:
            page_data = format_search_results_page(search_query, current_page)
        else:
            page_data = format_stopwords_page(current_page)

        if page_data['has_words']:
            if page_data['keyboard']:
                await callback.message.edit_text(
                    page_data['text'],
                    parse_mode='Markdown',
                    reply_markup=page_data['keyboard']
                )
            else:
                await callback.message.edit_text(
                    page_data['text'],
                    parse_mode='Markdown'
                )
        else:
            await callback.message.edit_text(
                "📋 Список стоп-слов пуст"
            )

        logger.info(f"Пользователь {callback.from_user.id} отменил действие со стоп-словом")

    except Exception as e:
        logger.error(f"Ошибка при отмене действия со стоп-словом: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при отмене действия"
        )


async def handle_start_search_from_pagination_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка кнопки поиска из пагинации стоп-слов

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        user_id = callback.from_user.id

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка доступа к поиску от неадминистратора: {user_id}")
            await callback.message.edit_text("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
            return

        # Переход в состояние ожидания поискового запроса
        await state.set_state(StopWordsManagement.waiting_for_search_query)

        search_instructions = (
            f"🔍 **Поиск стоп-слов**\n\n"
            f"📝 Введите запрос для поиска:\n\n"
            f"🔍 **Возможности поиска:**\n"
            f"• Поиск по подстроке: спам найдет спам, антиспам\n"
            f"• Поиск точного совпадения: введите полное слово\n"
            f"• Регистр не важен: СПАМ = спам\n"
            f"• Минимальная длина запроса: 1 символ\n\n"
            f"💡 **Примеры запросов:**\n"
            f"• рекл - найдет все слова содержащие 'рекл'\n"
            f"• test - найдет все слова содержащие 'test'\n"
            f"• а - найдет все слова с буквой 'а'\n\n"
            f"❌ Для отмены используйте кнопку ниже"
        )

        await callback.message.edit_text(
            search_instructions,
            parse_mode='Markdown',
            reply_markup=cancel_operation_keyboard()
        )

        logger.info(f"Пользователь {user_id} начал процесс поиска стоп-слов из пагинации")

    except Exception as e:
        logger.error(f"Ошибка при запуске поиска из пагинации: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при запуске поиска"
        )


async def handle_add_new_from_pagination_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка кнопки добавления стоп-слова из пагинации

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        user_id = callback.from_user.id

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка добавления стоп-слова от неадминистратора: {user_id}")
            await callback.message.edit_text("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
            return

        # Переход в состояние ожидания нового стоп-слова
        await state.set_state(StopWordsManagement.waiting_for_new_stopword)

        add_instructions = (
            f"➕ **Добавление стоп-слова**\n\n"
            f"📝 Введите новое стоп-слово:\n\n"
            f"📋 **Требования:**\n"
            f"• Минимальная длина: 1 символ\n"
            f"• Максимальная длина: 100 символов\n"
            f"• Только буквы, цифры, дефисы и подчеркивания\n"
            f"• Регистр не важен (будет приведен к нижнему)\n\n"
            f"💡 **Примеры:**\n"
            f"• спам\n"
            f"• реклама\n"
            f"• мошенники\n\n"
            f"❌ Для отмены используйте кнопку ниже"
        )

        await callback.message.edit_text(
            add_instructions,
            parse_mode='Markdown',
            reply_markup=cancel_operation_keyboard()
        )

        logger.info(f"Пользователь {user_id} начал процесс добавления стоп-слова из пагинации")

    except Exception as e:
        logger.error(f"Ошибка при запуске добавления из пагинации: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при запуске добавления"
        )


async def handle_page_info_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка кнопки информации о странице

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer("ℹ️ Информация о текущей странице", show_alert=True)


async def handle_new_search_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка кнопки нового поиска

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        user_id = callback.from_user.id

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка доступа к поиску от неадминистратора: {user_id}")
            await callback.message.edit_text("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
            return

        # Очистка предыдущих результатов поиска
        await state.update_data(search_query=None, search_results=None)

        # Переход в состояние ожидания поискового запроса
        await state.set_state(StopWordsManagement.waiting_for_search_query)

        search_instructions = (
            f"🔍 **Новый поиск стоп-слов**\n\n"
            f"📝 Введите запрос для поиска:\n\n"
            f"🔍 **Возможности поиска:**\n"
            f"• Поиск по подстроке: спам найдет спам, антиспам\n"
            f"• Поиск точного совпадения: введите полное слово\n"
            f"• Регистр не важен: СПАМ = спам\n"
            f"• Минимальная длина запроса: 1 символ\n\n"
            f"💡 **Примеры запросов:**\n"
            f"• рекл - найдет все слова содержащие 'рекл'\n"
            f"• test - найдет все слова содержащие 'test'\n"
            f"• а - найдет все слова с буквой 'а'\n\n"
            f"❌ Для отмены используйте кнопку ниже"
        )

        await callback.message.edit_text(
            search_instructions,
            parse_mode='Markdown',
            reply_markup=cancel_operation_keyboard()
        )

        logger.info(f"Пользователь {user_id} начал новый поиск стоп-слов")

    except Exception as e:
        logger.error(f"Ошибка при запуске нового поиска: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при запуске нового поиска"
        )


async def handle_export_search_results_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка экспорта результатов поиска

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        user_id = callback.from_user.id

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка экспорта от неадминистратора: {user_id}")
            await callback.message.edit_text("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
            return

        # Получение результатов поиска из состояния
        data = await state.get_data()
        search_results = data.get('search_results', [])
        search_query = data.get('search_query', '')

        if not search_results:
            await callback.message.edit_text(
                "❌ **Нет результатов для экспорта**\n\n"
                "Сначала выполните поиск стоп-слов.",
                parse_mode='Markdown'
            )
            return

        # Создание содержимого файла
        file_content = f"# Результаты поиска стоп-слов\n"
        file_content += f"# Запрос: {search_query}\n"
        file_content += f"# Найдено: {len(search_results)} слов\n"
        file_content += f"# Дата экспорта: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        for word in search_results:
            file_content += f"{word}\n"

        # Создание временного файла
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        # Отправка файла
        filename = f"search_results_{search_query}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        from aiogram.types import FSInputFile
        document = FSInputFile(temp_file_path, filename=filename)

        await callback.message.answer_document(
            document,
            caption=f"📤 **Экспорт результатов поиска**\n\n"
                   f"🔍 Запрос: `{search_query}`\n"
                   f"📊 Найдено: {len(search_results)} стоп-слов",
            parse_mode='Markdown'
        )

        # Удаление временного файла
        os.unlink(temp_file_path)

        await callback.message.edit_text(
            f"✅ **Экспорт завершен**\n\n"
            f"📤 Файл с результатами поиска отправлен\n"
            f"🔍 Запрос: `{search_query}`\n"
            f"📊 Экспортировано: {len(search_results)} стоп-слов",
            parse_mode='Markdown'
        )

        logger.info(f"Пользователь {user_id} экспортировал результаты поиска: {len(search_results)} слов")

    except Exception as e:
        logger.error(f"Ошибка при экспорте результатов поиска: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при экспорте результатов поиска"
        )


async def handle_delete_search_results_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка удаления всех найденных стоп-слов

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        user_id = callback.from_user.id

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка массового удаления от неадминистратора: {user_id}")
            await callback.message.edit_text("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
            return

        # Получение результатов поиска из состояния
        data = await state.get_data()
        search_results = data.get('search_results', [])
        search_query = data.get('search_query', '')

        if not search_results:
            await callback.message.edit_text(
                "❌ **Нет результатов для удаления**\n\n"
                "Сначала выполните поиск стоп-слов.",
                parse_mode='Markdown'
            )
            return

        # Подтверждение массового удаления
        confirmation_text = (
            f"🗑️ **МАССОВОЕ УДАЛЕНИЕ СТОП-СЛОВ**\n\n"
            f"⚠️ **ВНИМАНИЕ!** Вы собираетесь удалить **{len(search_results)}** стоп-слов:\n\n"
            f"🔍 Запрос: `{search_query}`\n"
            f"📊 К удалению: {len(search_results)} слов\n\n"
            f"❗ **Это действие НЕЛЬЗЯ отменить!**\n\n"
            f"Вы уверены, что хотите продолжить?"
        )

        # Создание клавиатуры подтверждения
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        confirm_keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=f"✅ Да, удалить {len(search_results)} слов",
                    callback_data="sw_confirm_mass_delete"
                )
            ],
            [
                InlineKeyboardButton(
                    text="❌ Отмена",
                    callback_data="sw_cancel_mass_delete"
                )
            ]
        ])

        await callback.message.edit_text(
            confirmation_text,
            parse_mode='Markdown',
            reply_markup=confirm_keyboard
        )

        logger.info(f"Пользователь {user_id} запросил массовое удаление {len(search_results)} стоп-слов")

    except Exception as e:
        logger.error(f"Ошибка при запросе массового удаления: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при запросе массового удаления"
        )


async def handle_show_all_stopwords_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка показа всех стоп-слов

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        user_id = callback.from_user.id

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка просмотра стоп-слов от неадминистратора: {user_id}")
            await callback.message.edit_text("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
            return

        # Очистка результатов поиска и переход к просмотру всех стоп-слов
        await state.update_data(search_query=None, search_results=None)
        await state.set_state(StopWordsManagement.waiting_for_stopword_selection)

        # Отображение первой страницы всех стоп-слов
        page_data = format_stopwords_page(1)

        if page_data['has_words']:
            await state.update_data(
                current_page=1,
                page_data=page_data['page_data']
            )

            if page_data['keyboard']:
                await callback.message.edit_text(
                    page_data['text'],
                    parse_mode='Markdown',
                    reply_markup=page_data['keyboard']
                )
            else:
                await callback.message.edit_text(
                    page_data['text'],
                    parse_mode='Markdown'
                )
        else:
            await callback.message.edit_text(
                "📋 **Список стоп-слов пуст**\n\n"
                "Добавьте первое стоп-слово для начала работы.",
                parse_mode='Markdown'
            )

        logger.info(f"Пользователь {user_id} перешел к просмотру всех стоп-слов")

    except Exception as e:
        logger.error(f"Ошибка при показе всех стоп-слов: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке списка стоп-слов"
        )


async def handle_confirm_mass_delete_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка подтверждения массового удаления стоп-слов

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        user_id = callback.from_user.id

        # Проверка прав администратора
        if not env_config.is_admin(user_id):
            logger.warning(f"Попытка массового удаления от неадминистратора: {user_id}")
            await callback.message.edit_text("❌ Доступ запрещен. Только администратор может управлять стоп-словами.")
            return

        # Получение результатов поиска из состояния
        data = await state.get_data()
        search_results = data.get('search_results', [])
        search_query = data.get('search_query', '')

        if not search_results:
            await callback.message.edit_text(
                "❌ **Нет результатов для удаления**\n\n"
                "Результаты поиска не найдены.",
                parse_mode='Markdown'
            )
            return

        # Выполнение массового удаления
        deleted_count = 0
        failed_count = 0

        for word in search_results:
            try:
                if stopwords_manager.remove_stopword(word):
                    deleted_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"Ошибка при удалении стоп-слова '{word}': {e}")
                failed_count += 1

        # Формирование результата
        result_text = f"✅ **Массовое удаление завершено**\n\n"
        result_text += f"🔍 Запрос: `{search_query}`\n"
        result_text += f"🗑️ Удалено: **{deleted_count}** стоп-слов\n"

        if failed_count > 0:
            result_text += f"❌ Ошибок: **{failed_count}**\n"

        result_text += f"\n📊 Всего стоп-слов осталось: **{len(stopwords_manager.stopwords)}**"

        await callback.message.edit_text(
            result_text,
            parse_mode='Markdown'
        )

        # Очистка результатов поиска
        await state.update_data(search_query=None, search_results=None)

        logger.info(f"Пользователь {user_id} выполнил массовое удаление: {deleted_count} удалено, {failed_count} ошибок")

    except Exception as e:
        logger.error(f"Ошибка при массовом удалении: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при массовом удалении стоп-слов"
        )


async def handle_cancel_mass_delete_action(callback: types.CallbackQuery, state: FSMContext) -> None:
    """
    Обработка отмены массового удаления стоп-слов

    Args:
        callback (types.CallbackQuery): Callback от inline-кнопки
        state (FSMContext): Контекст состояния FSM
    """
    await callback.answer()

    try:
        # Получение данных из состояния
        data = await state.get_data()
        search_query = data.get('search_query', '')
        current_page = data.get('current_page', 1)

        # Возврат к результатам поиска
        if search_query:
            page_data = format_search_results(search_query, current_page)

            if page_data['keyboard']:
                await callback.message.edit_text(
                    page_data['text'],
                    parse_mode='Markdown',
                    reply_markup=page_data['keyboard']
                )
            else:
                await callback.message.edit_text(
                    page_data['text'],
                    parse_mode='Markdown'
                )
        else:
            await callback.message.edit_text(
                "❌ **Массовое удаление отменено**\n\n"
                "Стоп-слова не были удалены.",
                parse_mode='Markdown'
            )

        logger.info(f"Пользователь {callback.from_user.id} отменил массовое удаление")

    except Exception as e:
        logger.error(f"Ошибка при отмене массового удаления: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при отмене операции"
        )


# ============================================================================
# FSM-ОБРАБОТЧИКИ (ВВОД ДАННЫХ В СОСТОЯНИЯХ)
# ============================================================================

async def process_new_stopword(message: types.Message, state: FSMContext) -> None:
    """
    Обработка ввода нового стоп-слова пользователем

    Валидирует введенное стоп-слово и добавляет его в систему при успешной валидации.
    Предоставляет детальную обратную связь о результате операции.

    Args:
        message (types.Message): Сообщение с введенным стоп-словом
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id
    input_text = message.text.strip()

    try:
        logger.info(f"Пользователь {user_id} ввел стоп-слово для добавления: '{input_text}'")

        # Валидация введенного стоп-слова
        validation_result = validator.validate_user_input(input_text)

        if not validation_result.is_valid:
            # Ошибки валидации
            error_text = (
                f"❌ **Ошибка валидации стоп-слова**\n\n"
                f"Введенное слово: `{input_text}`\n\n"
                f"**Ошибки:**\n"
            )

            for error in validation_result.errors:
                error_text += f"• {error}\n"

            error_text += (
                f"\n💡 **Попробуйте еще раз** или используйте кнопку отмены ниже."
            )

            await message.answer(
                error_text,
                parse_mode='Markdown',
                reply_markup=cancel_operation_keyboard()
            )
            return

        # Попытка добавления валидного стоп-слова
        normalized_word = validation_result.normalized_data
        success = stopwords_manager.add_stopword(normalized_word)

        if success:
            # Успешное добавление
            success_text = (
                f"✅ **Стоп-слово добавлено успешно**\n\n"
                f"Добавлено: **\"{normalized_word}\"**\n\n"
                f"📊 Общее количество стоп-слов: **{len(stopwords_manager.stopwords)}**"
            )

            # Добавление предупреждений если есть
            if validation_result.warnings:
                success_text += f"\n\n⚠️ **Предупреждения:**\n"
                for warning in validation_result.warnings:
                    success_text += f"• {warning}\n"

            await message.answer(
                success_text,
                parse_mode='Markdown'
            )

            # Обновление валидатора новым словом
            validator.update_existing_stopwords(set(stopwords_manager.stopwords))

            logger.info(f"Стоп-слово '{normalized_word}' успешно добавлено пользователем {user_id}")

        else:
            # Ошибка добавления (например, дубликат)
            await message.answer(
                f"❌ **Ошибка добавления**\n\n"
                f"Не удалось добавить стоп-слово: **\"{normalized_word}\"**\n"
                f"Возможно, такое слово уже существует.",
                parse_mode='Markdown'
            )

            logger.warning(f"Не удалось добавить стоп-слово '{normalized_word}' для пользователя {user_id}")

        # Очистка состояния после обработки
        await state.clear()

    except Exception as e:
        logger.error(f"Ошибка при обработке нового стоп-слова: {e}")
        await handle_errors(message, "Ошибка при добавлении стоп-слова")
        await state.clear()


async def process_search_query(message: types.Message, state: FSMContext) -> None:
    """
    Обработка поискового запроса пользователя

    Выполняет поиск стоп-слов по введенному запросу и отображает результаты
    с возможностью пагинации и дополнительными действиями.

    Args:
        message (types.Message): Сообщение с поисковым запросом
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id
    search_query = message.text.strip()

    try:
        logger.info(f"Пользователь {user_id} выполняет поиск: '{search_query}'")

        # Валидация поискового запроса
        if not search_query:
            await message.answer(
                "❌ **Пустой поисковый запрос**\n\n"
                "Введите слово или фразу для поиска.",
                parse_mode='Markdown',
                reply_markup=cancel_operation_keyboard()
            )
            return

        if len(search_query) > 100:
            await message.answer(
                "❌ **Слишком длинный запрос**\n\n"
                "Поисковый запрос не может быть длиннее 100 символов.",
                parse_mode='Markdown',
                reply_markup=cancel_operation_keyboard()
            )
            return

        # Выполнение поиска
        found_words = stopwords_manager.search_stopwords(search_query)

        if not found_words:
            # Результаты не найдены
            await message.answer(
                f"🔍 **Поиск завершен**\n\n"
                f"По запросу **\"{search_query}\"** ничего не найдено.\n\n"
                f"💡 **Попробуйте:**\n"
                f"• Изменить запрос\n"
                f"• Использовать часть слова\n"
                f"• Проверить правописание",
                parse_mode='Markdown',
                reply_markup=back_to_stopwords_keyboard()
            )

            await state.clear()
            return

        # Переход в состояние просмотра результатов поиска
        await state.set_state(StopWordsManagement.viewing_search_results)
        await state.update_data(search_query=search_query, search_results=found_words)

        # Отображение первой страницы результатов
        page_data = format_search_results(search_query, 1)

        if page_data['keyboard']:
            await message.answer(
                page_data['text'],
                parse_mode='Markdown',
                reply_markup=page_data['keyboard']
            )
        else:
            await message.answer(
                page_data['text'],
                parse_mode='Markdown'
            )

        logger.info(f"Поиск '{search_query}' выполнен: найдено {len(found_words)} результатов")

    except Exception as e:
        logger.error(f"Ошибка при обработке поискового запроса: {e}")
        await handle_errors(message, "Ошибка при выполнении поиска")
        await state.clear()


async def process_import_file(message: types.Message, state: FSMContext) -> None:
    """
    Обработка файла для импорта стоп-слов

    Принимает файл от пользователя, валидирует его содержимое и предлагает
    предварительный просмотр перед импортом.

    Args:
        message (types.Message): Сообщение с прикрепленным файлом
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id

    try:
        # Проверка наличия документа
        if not message.document:
            await message.answer(
                "❌ **Файл не найден**\n\n"
                "Пожалуйста, отправьте текстовый файл (.txt) со стоп-словами.",
                parse_mode='Markdown',
                reply_markup=cancel_operation_keyboard()
            )
            return

        document = message.document

        # Проверка типа файла
        if not document.file_name.lower().endswith('.txt'):
            await message.answer(
                "❌ **Неподдерживаемый формат файла**\n\n"
                "Поддерживаются только текстовые файлы (.txt).",
                parse_mode='Markdown',
                reply_markup=cancel_operation_keyboard()
            )
            return

        # Проверка размера файла
        if document.file_size > validator.MAX_IMPORT_FILE_SIZE:
            size_mb = document.file_size / (1024 * 1024)
            await message.answer(
                f"❌ **Файл слишком большой**\n\n"
                f"Размер файла: {size_mb:.1f} МБ\n"
                f"Максимальный размер: 1 МБ",
                parse_mode='Markdown',
                reply_markup=cancel_operation_keyboard()
            )
            return

        # Скачивание файла
        file_info = await message.bot.get_file(document.file_id)
        file_content = await message.bot.download_file(file_info.file_path)

        # Чтение содержимого файла
        content_text = file_content.read().decode('utf-8')

        # Валидация содержимого файла
        validation_result = validator.validate_import_file(content_text, document.file_name)

        if not validation_result.is_valid:
            # Ошибки валидации файла
            error_text = (
                f"❌ **Ошибка валидации файла**\n\n"
                f"Файл: `{document.file_name}`\n\n"
                f"**Ошибки:**\n"
            )

            for error in validation_result.errors:
                error_text += f"• {error}\n"

            await message.answer(
                error_text,
                parse_mode='Markdown',
                reply_markup=cancel_operation_keyboard()
            )
            return

        # Сохранение файла во временную директорию
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')
        temp_file.write(content_text)
        temp_file.close()

        # Подготовка данных для предварительного просмотра
        import_data = validation_result.normalized_data
        preview_text = (
            f"📥 **Предварительный просмотр импорта**\n\n"
            f"📄 **Файл:** `{document.file_name}`\n"
            f"📊 **Статистика:**\n"
            f"• Валидных слов: **{len(import_data['valid_words'])}**\n"
            f"• Дубликатов: **{len(import_data['duplicate_words'])}**\n"
            f"• Невалидных: **{len(import_data['invalid_words'])}**\n\n"
        )

        # Показ примеров валидных слов
        if import_data['valid_words']:
            preview_words = import_data['valid_words'][:10]
            preview_text += f"✅ **Будут добавлены (первые 10):**\n"
            for word in preview_words:
                preview_text += f"• `{word}`\n"

            if len(import_data['valid_words']) > 10:
                preview_text += f"• ... и еще {len(import_data['valid_words']) - 10} слов\n"

        # Показ предупреждений
        if validation_result.warnings:
            preview_text += f"\n⚠️ **Предупреждения:**\n"
            for warning in validation_result.warnings[:5]:  # Показываем только первые 5
                preview_text += f"• {warning}\n"

        preview_text += f"\n❓ **Продолжить импорт?**"

        # Сохранение данных импорта в состоянии
        await state.update_data(import_data={
            'filepath': temp_file.name,
            'filename': document.file_name,
            'valid_words': import_data['valid_words'],
            'stats': import_data
        })

        # Подготовка данных для клавиатуры подтверждения
        preview_data = {
            'new_words': len(import_data['valid_words']),
            'duplicates': len(import_data['duplicate_words']),
            'invalid': len(import_data['invalid_words'])
        }

        await message.answer(
            preview_text,
            parse_mode='Markdown',
            reply_markup=import_confirmation_keyboard(preview_data)
        )

        logger.info(f"Пользователь {user_id} загрузил файл для импорта: {document.file_name}")

    except Exception as e:
        logger.error(f"Ошибка при обработке файла импорта: {e}")
        await handle_errors(message, "Ошибка при обработке файла")
        await state.clear()


async def process_export_filename(message: types.Message, state: FSMContext) -> None:
    """
    Обработка имени файла для экспорта стоп-слов

    Валидирует введенное имя файла и выполняет экспорт стоп-слов
    в соответствии с выбранным типом экспорта.

    Args:
        message (types.Message): Сообщение с именем файла
        state (FSMContext): Контекст состояния FSM
    """
    user_id = message.from_user.id
    filename_input = message.text.strip()

    try:
        logger.info(f"Пользователь {user_id} ввел имя файла для экспорта: '{filename_input}'")

        # Валидация имени файла
        validation_result = validator.validate_filename(filename_input)

        if not validation_result.is_valid:
            # Ошибки валидации имени файла
            error_text = (
                f"❌ **Ошибка валидации имени файла**\n\n"
                f"Введенное имя: `{filename_input}`\n\n"
                f"**Ошибки:**\n"
            )

            for error in validation_result.errors:
                error_text += f"• {error}\n"

            error_text += f"\n💡 **Попробуйте еще раз** или используйте кнопку отмены."

            await message.answer(
                error_text,
                parse_mode='Markdown',
                reply_markup=cancel_operation_keyboard()
            )
            return

        # Получение данных экспорта из состояния
        data = await state.get_data()
        export_type = data.get('export_type', 'all')

        # Подготовка данных для экспорта
        if export_type == "all":
            # Экспорт всех стоп-слов
            words_to_export = stopwords_manager.stopwords
            export_description = "всех стоп-слов"
        elif export_type == "search":
            # Экспорт результатов поиска
            search_results = data.get('search_results', [])
            words_to_export = search_results
            export_description = f"результатов поиска ({len(search_results)} слов)"
        else:
            await message.answer(
                "❌ **Ошибка типа экспорта**\n\n"
                "Неизвестный тип экспорта.",
                parse_mode='Markdown',
                reply_markup=back_to_stopwords_keyboard()
            )
            await state.clear()
            return

        if not words_to_export:
            await message.answer(
                "❌ **Нет данных для экспорта**\n\n"
                "Список стоп-слов пуст.",
                parse_mode='Markdown',
                reply_markup=back_to_stopwords_keyboard()
            )
            await state.clear()
            return

        # Создание содержимого файла
        normalized_filename = validation_result.normalized_data
        export_content = create_export_content(words_to_export, export_type, data.get('search_query'))

        # Создание файла для отправки
        file_buffer = BytesIO(export_content.encode('utf-8'))
        file_buffer.name = f"{normalized_filename}.txt"

        # Отправка файла пользователю
        await message.answer_document(
            BufferedInputFile(file_buffer.getvalue(), filename=f"{normalized_filename}.txt"),
            caption=(
                f"📤 **Экспорт завершен успешно**\n\n"
                f"📄 **Файл:** `{normalized_filename}.txt`\n"
                f"📊 **Экспортировано:** {export_description}\n"
                f"📈 **Количество слов:** {len(words_to_export)}"
            ),
            parse_mode='Markdown',
            reply_markup=back_to_stopwords_keyboard()
        )

        logger.info(f"Экспорт выполнен для пользователя {user_id}: {len(words_to_export)} слов в файл {normalized_filename}.txt")

        # Очистка состояния
        await state.clear()

    except Exception as e:
        logger.error(f"Ошибка при обработке экспорта: {e}")
        await handle_errors(message, "Ошибка при экспорте файла")
        await state.clear()


# ============================================================================
# ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
# ============================================================================

def format_stopwords_page(page: int, per_page: int = 10) -> Dict[str, Any]:
    """
    Форматирование страницы списка стоп-слов для отображения

    Создает отформатированный текст и клавиатуру для отображения
    конкретной страницы списка стоп-слов с пагинацией.

    Args:
        page (int): Номер страницы (начиная с 1)
        per_page (int): Количество элементов на странице

    Returns:
        Dict[str, Any]: Словарь с текстом, клавиатурой и метаданными
    """
    try:
        # Получение пагинированных данных
        page_data = stopwords_manager.get_paginated_stopwords(page, per_page)

        if not page_data['words']:
            return {
                'text': "📋 **Список стоп-слов пуст**\n\nДобавьте первое стоп-слово для начала работы.",
                'keyboard': None,
                'has_words': False
            }

        # Формирование заголовка
        header_text = (
            f"📋 **Список стоп-слов**\n\n"
            f"📊 **Страница {page_data['current_page']} из {page_data['total_pages']}**\n"
            f"📈 **Всего стоп-слов: {page_data['total_words']}**\n\n"
        )

        # Формирование списка слов
        words_text = ""
        start_index = (page - 1) * per_page

        for i, word in enumerate(page_data['words'], start=start_index + 1):
            words_text += f"{i}. `{word}`\n"

        # Добавление инструкции для выбора стоп-слова
        words_text += f"\n" + "="*40 + "\n"
        words_text += f"💡 **КАК УДАЛИТЬ/РЕДАКТИРОВАТЬ СТОП-СЛОВО:**\n"
        words_text += f"📝 Введите номер стоп-слова в чат (например: {start_index + 1})\n"
        words_text += f"🎯 Затем выберите действие из меню\n"
        words_text += f"="*40 + "\n"

        # Объединение текста
        full_text = header_text + words_text

        # Создание клавиатуры пагинации
        keyboard = stopwords_pagination_keyboard(
            page_data['current_page'],
            page_data['total_pages']
        )

        return {
            'text': full_text,
            'keyboard': keyboard,
            'has_words': True,
            'page_data': page_data
        }

    except Exception as e:
        logger.error(f"Ошибка при форматировании страницы стоп-слов: {e}")
        return {
            'text': "❌ Ошибка при загрузке списка стоп-слов",
            'keyboard': None,
            'has_words': False
        }


def format_search_results(query: str, page: int, per_page: int = 10) -> Dict[str, Any]:
    """
    Форматирование результатов поиска стоп-слов для отображения

    Создает отформатированный текст и клавиатуру для отображения
    результатов поиска с пагинацией и дополнительными опциями.

    Args:
        query (str): Поисковый запрос
        page (int): Номер страницы результатов
        per_page (int): Количество результатов на странице

    Returns:
        Dict[str, Any]: Словарь с текстом, клавиатурой и метаданными
    """
    try:
        # Выполнение поиска
        found_words = stopwords_manager.search_stopwords(query)

        if not found_words:
            return {
                'text': f"🔍 **Результаты поиска**\n\nПо запросу **\"{query}\"** ничего не найдено.",
                'keyboard': None,
                'has_words': False
            }

        # Пагинация результатов
        total_results = len(found_words)
        total_pages = (total_results + per_page - 1) // per_page
        start_index = (page - 1) * per_page
        end_index = start_index + per_page
        page_results = found_words[start_index:end_index]

        # Формирование заголовка
        header_text = (
            f"🔍 **Результаты поиска**\n\n"
            f"📝 **Запрос:** `{query}`\n"
            f"📊 **Найдено:** {total_results} стоп-слов\n"
            f"📄 **Страница:** {page} из {total_pages}\n\n"
        )

        # Формирование списка найденных слов
        results_text = ""
        for i, word in enumerate(page_results, start=start_index + 1):
            # Выделение найденной подстроки
            highlighted_word = word.replace(query.lower(), f"**{query.lower()}**")
            results_text += f"{i}. `{highlighted_word}`\n"

        # Объединение текста
        full_text = header_text + results_text

        # Создание клавиатуры для результатов поиска
        keyboard = search_results_keyboard(total_results, page, total_pages, query)

        return {
            'text': full_text,
            'keyboard': keyboard,
            'has_words': True,
            'search_data': {
                'query': query,
                'total_results': total_results,
                'current_page': page,
                'total_pages': total_pages,
                'results': found_words
            }
        }

    except Exception as e:
        logger.error(f"Ошибка при форматировании результатов поиска: {e}")
        return {
            'text': f"❌ Ошибка при поиске по запросу: {query}",
            'keyboard': None,
            'has_words': False
        }


def create_export_content(words: List[str], export_type: str, search_query: Optional[str] = None) -> str:
    """
    Создание содержимого файла для экспорта стоп-слов

    Генерирует отформатированное содержимое файла с метаданными,
    комментариями и списком стоп-слов для экспорта.

    Args:
        words (List[str]): Список стоп-слов для экспорта
        export_type (str): Тип экспорта ('all' или 'search')
        search_query (Optional[str]): Поисковый запрос (для типа 'search')

    Returns:
        str: Отформатированное содержимое файла
    """
    try:
        from datetime import datetime

        # Заголовок файла
        content_lines = [
            "# Экспорт стоп-слов PM Searcher Bot",
            f"# Дата экспорта: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"# Количество слов: {len(words)}",
        ]

        # Дополнительная информация в зависимости от типа экспорта
        if export_type == "search" and search_query:
            content_lines.extend([
                f"# Тип экспорта: Результаты поиска",
                f"# Поисковый запрос: {search_query}",
            ])
        else:
            content_lines.append("# Тип экспорта: Все стоп-слова")

        content_lines.extend([
            "#",
            "# Формат файла:",
            "# - Одно стоп-слово на строку",
            "# - Строки начинающиеся с # являются комментариями",
            "# - Пустые строки игнорируются",
            "#",
            "# Этот файл может быть использован для импорта в PM Searcher Bot",
            "",
        ])

        # Добавление отсортированных стоп-слов
        sorted_words = sorted(words)
        content_lines.extend(sorted_words)

        # Добавление статистики в конец файла
        content_lines.extend([
            "",
            f"# Конец файла - экспортировано {len(words)} стоп-слов"
        ])

        return '\n'.join(content_lines)

    except Exception as e:
        logger.error(f"Ошибка при создании содержимого экспорта: {e}")
        # Возврат базового содержимого при ошибке
        return f"# Экспорт стоп-слов\n# Ошибка генерации: {str(e)}\n\n" + '\n'.join(sorted(words))


async def handle_errors(message: types.Message, error_message: str) -> None:
    """
    Универсальная обработка ошибок с отправкой сообщения пользователю

    Отправляет пользователю информативное сообщение об ошибке
    с возможностью возврата в главное меню стоп-слов.

    Args:
        message (types.Message): Сообщение для ответа
        error_message (str): Текст ошибки для отображения
    """
    try:
        error_text = (
            f"❌ **Произошла ошибка**\n\n"
            f"{error_message}\n\n"
            f"🔄 **Попробуйте:**\n"
            f"• Повторить операцию\n"
            f"• Вернуться в главное меню\n"
            f"• Обратиться к администратору"
        )

        await message.answer(
            error_text,
            parse_mode='Markdown',
            reply_markup=back_to_stopwords_keyboard()
        )

    except Exception as e:
        logger.error(f"Ошибка при обработке ошибки: {e}")
        # Попытка отправить базовое сообщение об ошибке
        try:
            await message.answer(
                "❌ Произошла критическая ошибка. Обратитесь к администратору.",
                reply_markup=back_to_stopwords_keyboard()
            )
        except:
            pass  # Если даже это не работает, просто логируем

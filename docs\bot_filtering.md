# Фильтрация алертов от ботов

## Обзор

Система мониторинга Telegram аккаунтов теперь поддерживает фильтрацию алертов от ботов. Эта функциональность позволяет избежать спама уведомлений от автоматических сообщений ботов, сохраняя при этом мониторинг обычных пользователей.

## Как это работает

### Определение ботов

Система определяет ботов по следующим критериям:

1. **Флаг bot**: Проверяется атрибут `bot` объекта пользователя из Telethon API
2. **Username паттерны**: 
   - Окончание на `_bot` (например, `PriemkaLitva_bot`)
   - Другие bot-паттерны в сочетании с флагом bot

### Примеры ботов, которые будут отфильтрованы

- `@PriemkaLitva_bot` (ID: 7625968101)
- `@support_bot`
- `@notification_bot`
- `@helper_bot`
- Любой пользователь с флагом `bot = True`

## Настройка

### По умолчанию

Фильтрация ботов **включена по умолчанию**. Это означает, что система автоматически пропускает фото-алерты от ботов.

### Управление настройкой

Настройка фильтрации ботов управляется через класс `MonitoringSettings`:

```python
from src.config.monitoring_settings import monitoring_settings

# Проверить текущее состояние
print(monitoring_settings.bot_filtering_enabled)  # True по умолчанию

# Отключить фильтрацию ботов
monitoring_settings.set_bot_filtering(False, updated_by=admin_user_id)

# Включить фильтрацию ботов
monitoring_settings.set_bot_filtering(True, updated_by=admin_user_id)

# Получить полный статус мониторинга
status = monitoring_settings.get_monitoring_status()
print(status['bot_filtering'])
```

### Конфигурационный файл

Настройки сохраняются в файле `data/monitoring_config.json`:

```json
{
  "group_chat_monitoring_enabled": true,
  "private_chat_monitoring_enabled": true,
  "bot_filtering_enabled": true,
  "last_updated": "2023-12-01T12:00:00",
  "updated_by": 123456789
}
```

## Логирование

### Когда фильтрация включена

```
[+79991234567] 🤖 ФИЛЬТРАЦИЯ БОТОВ: Пропускаем фото алерт от бота @PriemkaLitva_bot (ID: 7625968101) в чате Приемка Литвы
```

### Когда фильтрация отключена

```
[+79991234567] 🤖 Обнаружен бот @PriemkaLitva_bot (ID: 7625968101), но фильтрация отключена - отправляем алерт
```

## Безопасность

### Fallback поведение

В случае ошибок при определении типа отправителя, система использует безопасный fallback:
- При ошибке считается, что отправитель **не является ботом**
- Алерт отправляется как обычно
- Ошибка логируется для отладки

### Обработка исключений

```python
def _is_bot_sender(self, sender) -> bool:
    try:
        # Логика определения бота
        ...
    except Exception as e:
        logger.warning(f"Ошибка при проверке типа отправителя: {e}")
        return False  # Безопасный fallback
```

## Тестирование

### Юнит-тесты

```bash
# Тесты функции определения ботов
python -m pytest tests/unit/test_telethon_manager.py::TestBotFiltering -v

# Тесты настроек мониторинга
python -m pytest tests/unit/test_monitoring_settings.py -v
```

### Интеграционные тесты

```bash
# Полные интеграционные тесты
python -m pytest tests/integration/test_bot_filtering_integration.py -v
```

## Примеры использования

### Проверка конкретного отправителя

```python
from src.monitoring.telethon_manager import TelethonManager
from unittest.mock import Mock

manager = TelethonManager()

# Создаем mock объект бота
bot_sender = Mock()
bot_sender.bot = True
bot_sender.username = "test_bot"

is_bot = manager._is_bot_sender(bot_sender)
print(f"Является ботом: {is_bot}")  # True
```

### Изменение настроек

```python
from src.config.monitoring_settings import monitoring_settings

# Временно отключить фильтрацию для отладки
monitoring_settings.set_bot_filtering(False, updated_by=admin_id)

# Проверить статус
status = monitoring_settings.get_monitoring_status()
print(f"Фильтрация ботов: {status['bot_filtering']}")
print(f"Последнее обновление: {status['last_updated']}")
print(f"Обновлено пользователем: {status['updated_by']}")

# Включить обратно
monitoring_settings.set_bot_filtering(True, updated_by=admin_id)
```

## Влияние на производительность

- **Минимальное**: Проверка типа отправителя выполняется только для входящих медиа-сообщений
- **Кэширование**: Результаты проверки не кэшируются, но операция очень быстрая
- **Сетевые запросы**: Дополнительных сетевых запросов не выполняется

## Совместимость

- **Telethon**: Совместимо с текущей версией Telethon API
- **Обратная совместимость**: Полная - существующая функциональность не затронута
- **Конфигурация**: Автоматическое создание настроек по умолчанию для новых установок

## Устранение неполадок

### Боты все еще отправляют алерты

1. Проверьте настройку: `monitoring_settings.bot_filtering_enabled`
2. Проверьте логи на предмет ошибок определения типа отправителя
3. Убедитесь, что бот правильно определяется функцией `_is_bot_sender`

### Обычные пользователи фильтруются как боты

1. Проверьте username пользователя на наличие bot-паттернов
2. Проверьте флаг `bot` в объекте пользователя
3. Добавьте дополнительное логирование для отладки

### Настройки не сохраняются

1. Проверьте права доступа к файлу `data/monitoring_config.json`
2. Проверьте логи на предмет ошибок сохранения
3. Убедитесь, что директория `data/` существует

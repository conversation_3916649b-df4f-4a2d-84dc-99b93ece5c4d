"""
Интеграционные тесты полных сценариев PM Searcher Bot

Покрывает полные пользовательские сценарии от начала до конца
с реальным взаимодействием между компонентами.
"""

import pytest
import tempfile
import asyncio
import time
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from aiogram import Bot, Dispatcher
from aiogram.types import Message, CallbackQuery, User, Chat, Document
from aiogram.fsm.context import FSMContext
from aiogram.fsm.storage.memory import MemoryStorage

from src.bot.handlers.basic import register_basic_handlers
from src.bot.handlers.account import register_account_handlers
from src.bot.handlers.admin import register_admin_handlers
from src.bot.handlers.stopwords_management import register_stopwords_handlers
from src.bot.states import ConnectAccount, StopWordsManagement
from src.utils.stopwords import StopWordsManager
from src.monitoring.telethon_manager import TelethonManager
from src.config.environment import env_config
from tests.fixtures.test_constants import TEST_ADMIN_USER_ID, TEST_REGULAR_USER_ID
from tests.utils.mock_factories import MockFactory, TelethonMockFactory, StopWordsMockFactory


class TestFullUserScenarios:
    """Тесты полных пользовательских сценариев"""
    
    @pytest.mark.asyncio
    async def test_complete_account_connection_flow(self):
        """Тест полного сценария подключения аккаунта"""
        # Настройка
        bot = MockFactory.create_bot()
        storage = MemoryStorage()
        dp = Dispatcher(storage=storage)
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        mock_telethon_manager.start_auth.return_value = True
        mock_telethon_manager.complete_auth.return_value = True
        
        # Регистрируем обработчики
        register_basic_handlers(dp)
        register_account_handlers(dp, mock_telethon_manager)
        
        user_data = MockFactory.create_user_data(TEST_REGULAR_USER_ID)
        
        # Шаг 1: Пользователь нажимает "Подключить аккаунт"
        message1 = MockFactory.create_message(user_data, "📱 Подключить аккаунт")
        
        with patch('src.bot.handlers.account.telethon_manager', mock_telethon_manager):
            # Имитируем обработку сообщения
            state = MockFactory.create_fsm_context()
            
            # Проверяем что пользователь переведен в состояние ожидания телефона
            await state.set_state(ConnectAccount.waiting_for_phone)
            state.set_state.assert_called_with(ConnectAccount.waiting_for_phone)
            
            # Шаг 2: Пользователь вводит номер телефона
            message2 = MockFactory.create_message(user_data, "+***********")
            
            # Имитируем обработку номера телефона
            phone_result = mock_telethon_manager.start_auth(TEST_REGULAR_USER_ID, "+***********")
            assert phone_result is True
            
            # Переводим в состояние ожидания кода
            await state.set_state(ConnectAccount.waiting_for_code)
            
            # Шаг 3: Пользователь вводит код подтверждения
            message3 = MockFactory.create_message(user_data, "12345")
            
            # Имитируем обработку кода
            code_result = mock_telethon_manager.complete_auth(TEST_REGULAR_USER_ID, "12345")
            assert code_result is True
            
            # Очищаем состояние после успешного подключения
            await state.clear()
            state.clear.assert_called()
            
            # Проверяем что все методы были вызваны
            mock_telethon_manager.start_auth.assert_called_once_with(TEST_REGULAR_USER_ID, "+***********")
            mock_telethon_manager.complete_auth.assert_called_once_with(TEST_REGULAR_USER_ID, "12345")
    
    @pytest.mark.asyncio
    async def test_complete_stopword_management_flow(self):
        """Тест полного сценария управления стоп-словами"""
        # Настройка
        bot = MockFactory.create_bot()
        storage = MemoryStorage()
        dp = Dispatcher(storage=storage)
        
        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager(["спам", "реклама"])
        
        # Регистрируем обработчики
        register_stopwords_handlers(dp)
        
        admin_data = MockFactory.create_user_data(TEST_ADMIN_USER_ID)
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch('src.bot.handlers.stopwords_management.stopwords_manager', mock_stopwords_manager):
                # Шаг 1: Администратор открывает управление стоп-словами
                message1 = MockFactory.create_message(admin_data, "Управление стоп-словами")
                
                # Проверяем что статистика была получена
                mock_stopwords_manager.get_detailed_stats = Mock(return_value={
                    'total_count': 2,
                    'average_length': 5.0,
                    'file_info': {'size_formatted': '0.5 KB'}
                })
                
                # Шаг 2: Администратор добавляет новое стоп-слово
                state = MockFactory.create_fsm_context()
                await state.set_state(StopWordsManagement.waiting_for_new_stopword)
                
                message2 = MockFactory.create_message(admin_data, "мошенники")
                
                # Имитируем добавление стоп-слова
                mock_stopwords_manager.add_stopword.return_value = True
                result = mock_stopwords_manager.add_stopword("мошенники")
                assert result is True
                
                await state.clear()
                
                # Шаг 3: Администратор ищет стоп-слова
                await state.set_state(StopWordsManagement.waiting_for_search_query)
                
                message3 = MockFactory.create_message(admin_data, "спам")
                
                # Имитируем поиск
                mock_stopwords_manager.search_stopwords.return_value = ["спам"]
                search_results = mock_stopwords_manager.search_stopwords("спам")
                assert "спам" in search_results
                
                await state.clear()
                
                # Проверяем что все операции были выполнены
                mock_stopwords_manager.add_stopword.assert_called_once_with("мошенники")
                mock_stopwords_manager.search_stopwords.assert_called_once_with("спам")
    
    @pytest.mark.asyncio
    async def test_admin_statistics_and_management_flow(self):
        """Тест полного сценария административного управления"""
        # Настройка
        bot = MockFactory.create_bot()
        storage = MemoryStorage()
        dp = Dispatcher(storage=storage)
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager([
            {'user_id': 123, 'phone': '+***********', 'monitoring': True},
            {'user_id': 456, 'phone': '+***********', 'monitoring': False}
        ])
        
        # Регистрируем обработчики
        register_admin_handlers(dp, mock_telethon_manager)
        
        admin_data = MockFactory.create_user_data(TEST_ADMIN_USER_ID)
        
        with patch.object(env_config, 'is_admin', return_value=True):
            with patch.object(env_config, 'ADMIN_CHAT_IDS', [TEST_ADMIN_USER_ID]):
                # Шаг 1: Администратор просматривает статистику
                message1 = MockFactory.create_message(admin_data, "Статистика")
                
                accounts = mock_telethon_manager.get_connected_accounts()
                active_accounts = [acc for acc in accounts if acc['monitoring']]
                
                assert len(accounts) == 2
                assert len(active_accounts) == 1
                
                # Шаг 2: Администратор просматривает все аккаунты
                message2 = MockFactory.create_message(admin_data, "Все подключенные аккаунты")
                
                all_accounts = mock_telethon_manager.get_connected_accounts()
                assert len(all_accounts) == 2
                assert all_accounts[0]['phone'] == '+***********'
                assert all_accounts[1]['phone'] == '+***********'
                
                # Шаг 3: Администратор перезагружает стоп-слова
                message3 = MockFactory.create_message(admin_data, "/reload_stopwords")
                
                mock_telethon_manager.reload_stopwords.return_value = 15
                reloaded_count = mock_telethon_manager.reload_stopwords()
                assert reloaded_count == 15
                
                # Проверяем что все методы были вызваны
                assert mock_telethon_manager.get_connected_accounts.call_count >= 2
                mock_telethon_manager.reload_stopwords.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_recovery_scenarios(self):
        """Тест сценариев восстановления после ошибок"""
        # Настройка
        bot = MockFactory.create_bot()
        storage = MemoryStorage()
        dp = Dispatcher(storage=storage)
        
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        
        # Регистрируем обработчики
        register_account_handlers(dp, mock_telethon_manager)
        
        user_data = MockFactory.create_user_data(TEST_REGULAR_USER_ID)
        
        # Сценарий 1: Ошибка при подключении аккаунта
        mock_telethon_manager.start_auth.side_effect = Exception("Connection failed")
        
        message1 = MockFactory.create_message(user_data, "+***********")
        state = MockFactory.create_fsm_context()
        
        # Имитируем обработку ошибки
        try:
            await mock_telethon_manager.start_auth(TEST_REGULAR_USER_ID, "+***********")
        except Exception as e:
            assert str(e) == "Connection failed"
            # Состояние должно быть очищено при ошибке
            await state.clear()
        
        # Сценарий 2: Восстановление после ошибки
        mock_telethon_manager.start_auth.side_effect = None
        mock_telethon_manager.start_auth.return_value = True
        
        # Пользователь пробует снова
        message2 = MockFactory.create_message(user_data, "+***********")
        
        result = await mock_telethon_manager.start_auth(TEST_REGULAR_USER_ID, "+***********")
        assert result is True
        
        # Проверяем что методы были вызваны
        assert mock_telethon_manager.start_auth.call_count == 2
        state.clear.assert_called()


class TestComponentIntegration:
    """Тесты интеграции между компонентами"""
    
    @pytest.mark.asyncio
    async def test_stopwords_manager_telethon_integration(self):
        """Тест интеграции StopWordsManager с TelethonManager"""
        # Создаем реальные экземпляры для интеграционного теста
        with tempfile.TemporaryDirectory() as temp_dir:
            stopwords_file = Path(temp_dir) / "stopwords.txt"
            
            # Создаем тестовый файл стоп-слов
            with open(stopwords_file, 'w', encoding='utf-8') as f:
                f.write("# Тестовые стоп-слова\n")
                f.write("спам\n")
                f.write("реклама\n")
                f.write("мошенники\n")
            
            # Мокируем путь к файлу
            with patch('src.utils.stopwords.STOPWORDS_FILE', str(stopwords_file)):
                stopwords_manager = StopWordsManager()
                
                # Проверяем загрузку стоп-слов
                assert len(stopwords_manager.stopwords) == 3
                assert "спам" in stopwords_manager.stopwords
                assert "реклама" in stopwords_manager.stopwords
                assert "мошенники" in stopwords_manager.stopwords
                
                # Тестируем добавление нового стоп-слова
                result = stopwords_manager.add_stopword("продажа")
                assert result is True
                assert "продажа" in stopwords_manager.stopwords
                
                # Тестируем проверку текста на стоп-слова
                test_text = "Это спам сообщение с рекламой"
                contains_stopwords, found_words = stopwords_manager.contains_stopwords(test_text)
                
                assert contains_stopwords is True
                assert "спам" in found_words
                assert "реклама" in found_words
                
                # Тестируем удаление стоп-слова
                result = stopwords_manager.remove_stopword("спам")
                assert result is True
                assert "спам" not in stopwords_manager.stopwords
                
                # Проверяем что файл был обновлен
                reloaded_count = stopwords_manager.reload_stopwords()
                assert reloaded_count == 3  # реклама, мошенники, продажа
    
    @pytest.mark.asyncio
    async def test_fsm_state_persistence(self):
        """Тест сохранения состояний FSM между сессиями"""
        # Создаем хранилище состояний
        storage = MemoryStorage()
        
        # Создаем контекст для пользователя
        user_id = TEST_REGULAR_USER_ID
        chat_id = TEST_REGULAR_USER_ID
        
        # Имитируем создание FSM контекста
        context_data = {
            'user_id': user_id,
            'chat_id': chat_id,
            'state': ConnectAccount.waiting_for_phone.state,
            'data': {'phone': '+***********'}
        }
        
        # Проверяем что данные можно сохранить и восстановить
        assert context_data['state'] == "ConnectAccount:waiting_for_phone"
        assert context_data['data']['phone'] == '+***********'
        
        # Имитируем переход в следующее состояние
        context_data['state'] = ConnectAccount.waiting_for_code.state
        context_data['data']['code_sent'] = True
        
        assert context_data['state'] == "ConnectAccount:waiting_for_code"
        assert context_data['data']['code_sent'] is True
    
    @pytest.mark.asyncio
    async def test_file_operations_integration(self):
        """Тест интеграции файловых операций"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Тест импорта стоп-слов из файла
            import_file = Path(temp_dir) / "import_stopwords.txt"
            
            with open(import_file, 'w', encoding='utf-8') as f:
                f.write("# Импортируемые стоп-слова\n")
                f.write("новое_слово1\n")
                f.write("новое_слово2\n")
                f.write("дубликат\n")
                f.write("дубликат\n")  # Дубликат для тестирования
                f.write("\n")  # Пустая строка
                f.write("# Комментарий\n")
                f.write("новое_слово3\n")
            
            # Читаем и обрабатываем файл
            with open(import_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Парсим содержимое
            lines = content.split('\n')
            words = []
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    words.append(line.lower())
            
            # Удаляем дубликаты
            unique_words = list(set(words))
            
            assert len(unique_words) == 4  # новое_слово1, новое_слово2, дубликат, новое_слово3
            assert "новое_слово1" in unique_words
            assert "новое_слово2" in unique_words
            assert "дубликат" in unique_words
            assert "новое_слово3" in unique_words
            
            # Тест экспорта стоп-слов в файл
            export_file = Path(temp_dir) / "export_stopwords.txt"
            
            export_words = ["экспорт1", "экспорт2", "экспорт3"]
            
            with open(export_file, 'w', encoding='utf-8') as f:
                f.write("# Экспортированные стоп-слова\n")
                f.write("# Создано автоматически\n\n")
                
                for word in sorted(export_words):
                    f.write(f"{word}\n")
            
            # Проверяем что файл был создан корректно
            assert export_file.exists()
            
            with open(export_file, 'r', encoding='utf-8') as f:
                exported_content = f.read()
            
            assert "экспорт1" in exported_content
            assert "экспорт2" in exported_content
            assert "экспорт3" in exported_content
            assert "# Экспортированные стоп-слова" in exported_content
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self):
        """Тест одновременных операций"""
        # Создаем несколько задач для имитации одновременных операций
        tasks = []
        results = []
        
        async def mock_operation(operation_id: int):
            # Имитируем асинхронную операцию
            await asyncio.sleep(0.01)  # Небольшая задержка
            
            # Имитируем работу со стоп-словами
            mock_manager = StopWordsMockFactory.create_stopwords_manager()
            result = mock_manager.add_stopword(f"слово_{operation_id}")
            
            return {'operation_id': operation_id, 'result': result}
        
        # Создаем 10 одновременных операций
        for i in range(10):
            task = asyncio.create_task(mock_operation(i))
            tasks.append(task)
        
        # Ждем завершения всех операций
        results = await asyncio.gather(*tasks)
        
        # Проверяем результаты
        assert len(results) == 10
        
        for i, result in enumerate(results):
            assert result['operation_id'] == i
            assert result['result'] is True
    
    @pytest.mark.asyncio
    async def test_error_propagation_between_components(self):
        """Тест распространения ошибок между компонентами"""
        # Создаем цепочку компонентов с ошибкой
        mock_stopwords_manager = StopWordsMockFactory.create_stopwords_manager()
        mock_telethon_manager = TelethonMockFactory.create_telethon_manager()
        
        # Настраиваем ошибку в StopWordsManager
        mock_stopwords_manager.add_stopword.side_effect = Exception("StopWords error")
        
        # Имитируем обработчик, который использует оба компонента
        async def mock_handler():
            try:
                # Сначала пытаемся добавить стоп-слово
                mock_stopwords_manager.add_stopword("тест")
            except Exception as e:
                # При ошибке пытаемся уведомить через TelethonManager
                mock_telethon_manager.send_error_notification(str(e))
                raise
        
        # Проверяем что ошибка правильно распространяется
        with pytest.raises(Exception, match="StopWords error"):
            await mock_handler()
        
        # Проверяем что уведомление было отправлено
        mock_telethon_manager.send_error_notification.assert_called_once_with("StopWords error")


class TestRealWorldScenarios:
    """Тесты реальных сценариев использования"""
    
    @pytest.mark.asyncio
    async def test_high_load_scenario(self):
        """Тест сценария высокой нагрузки"""
        # Имитируем множество пользователей одновременно
        user_count = 50
        operations_per_user = 5
        
        async def simulate_user_activity(user_id: int):
            operations = []
            
            for i in range(operations_per_user):
                # Имитируем различные операции пользователя
                if i % 3 == 0:
                    # Подключение аккаунта
                    mock_telethon = TelethonMockFactory.create_telethon_manager()
                    result = await mock_telethon.start_auth(user_id, f"+7999{user_id:07d}")
                    operations.append(('auth', result))
                elif i % 3 == 1:
                    # Работа со стоп-словами
                    mock_stopwords = StopWordsMockFactory.create_stopwords_manager()
                    result = mock_stopwords.add_stopword(f"слово_{user_id}_{i}")
                    operations.append(('stopword', result))
                else:
                    # Поиск стоп-слов
                    mock_stopwords = StopWordsMockFactory.create_stopwords_manager()
                    result = mock_stopwords.search_stopwords(f"поиск_{user_id}")
                    operations.append(('search', result))
            
            return {'user_id': user_id, 'operations': operations}
        
        # Запускаем всех пользователей одновременно
        tasks = [simulate_user_activity(i) for i in range(user_count)]
        results = await asyncio.gather(*tasks)
        
        # Проверяем результаты
        assert len(results) == user_count
        
        total_operations = sum(len(result['operations']) for result in results)
        assert total_operations == user_count * operations_per_user
        
        # Проверяем что все операции завершились успешно
        for result in results:
            for operation_type, operation_result in result['operations']:
                if operation_type in ['stopword', 'auth']:
                    assert operation_result is True
                elif operation_type == 'search':
                    assert isinstance(operation_result, list)
    
    @pytest.mark.asyncio
    async def test_data_consistency_scenario(self):
        """Тест сценария консистентности данных"""
        with tempfile.TemporaryDirectory() as temp_dir:
            stopwords_file = Path(temp_dir) / "consistency_test.txt"
            
            # Создаем начальный файл
            initial_words = ["слово1", "слово2", "слово3"]
            
            with open(stopwords_file, 'w', encoding='utf-8') as f:
                f.write("# Тест консистентности\n")
                for word in initial_words:
                    f.write(f"{word}\n")
            
            with patch('src.utils.stopwords.STOPWORDS_FILE', str(stopwords_file)):
                # Создаем несколько экземпляров менеджера
                manager1 = StopWordsManager()
                manager2 = StopWordsManager()
                
                # Проверяем что оба менеджера видят одинаковые данные
                assert len(manager1.stopwords) == len(manager2.stopwords) == 3
                assert set(manager1.stopwords) == set(manager2.stopwords)
                
                # Изменяем данные через первый менеджер
                manager1.add_stopword("новое_слово")
                
                # Перезагружаем данные во втором менеджере
                manager2.reload_stopwords()
                
                # Проверяем что изменения видны в обоих менеджерах
                assert "новое_слово" in manager1.stopwords
                assert "новое_слово" in manager2.stopwords
                assert len(manager1.stopwords) == len(manager2.stopwords) == 4
    
    @pytest.mark.asyncio
    async def test_recovery_after_system_restart(self):
        """Тест восстановления после перезапуска системы"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Имитируем состояние системы до перезапуска
            stopwords_file = Path(temp_dir) / "restart_test.txt"
            state_file = Path(temp_dir) / "state_backup.json"
            
            # Создаем файлы состояния
            pre_restart_words = ["спам", "реклама", "мошенники"]
            
            with open(stopwords_file, 'w', encoding='utf-8') as f:
                f.write("# Состояние до перезапуска\n")
                for word in pre_restart_words:
                    f.write(f"{word}\n")
            
            # Имитируем сохранение состояния FSM
            import json
            fsm_state = {
                'user_123': {
                    'state': 'StopWordsManagement:waiting_for_new_stopword',
                    'data': {'temp_word': 'временное_слово'}
                }
            }
            
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(fsm_state, f)
            
            # Имитируем перезапуск системы
            with patch('src.utils.stopwords.STOPWORDS_FILE', str(stopwords_file)):
                # Создаем новый экземпляр менеджера (как после перезапуска)
                manager = StopWordsManager()
                
                # Проверяем что данные восстановились
                assert len(manager.stopwords) == 3
                assert set(manager.stopwords) == set(pre_restart_words)
                
                # Проверяем что можем восстановить состояние FSM
                with open(state_file, 'r', encoding='utf-8') as f:
                    restored_state = json.load(f)
                
                assert 'user_123' in restored_state
                assert restored_state['user_123']['state'] == 'StopWordsManagement:waiting_for_new_stopword'
                assert restored_state['user_123']['data']['temp_word'] == 'временное_слово'


class TestFileOperationsIntegration:
    """Тесты интеграции файловых операций"""

    @pytest.mark.asyncio
    async def test_complete_import_export_cycle(self):
        """Тест полного цикла импорта-экспорта стоп-слов"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Создаем исходный файл для импорта
            import_file = Path(temp_dir) / "import_stopwords.txt"
            export_file = Path(temp_dir) / "export_stopwords.txt"
            main_file = Path(temp_dir) / "main_stopwords.txt"

            # Исходные данные для импорта
            import_words = [
                "# Импортируемые стоп-слова",
                "импорт_слово1",
                "импорт_слово2",
                "дубликат",
                "дубликат",  # Тестируем дедупликацию
                "",  # Пустая строка
                "# Комментарий",
                "импорт_слово3"
            ]

            with open(import_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(import_words))

            # Создаем основной файл стоп-слов
            with open(main_file, 'w', encoding='utf-8') as f:
                f.write("# Основные стоп-слова\n")
                f.write("основное_слово1\n")
                f.write("основное_слово2\n")

            with patch('src.utils.stopwords.STOPWORDS_FILE', str(main_file)):
                # Шаг 1: Загружаем основные стоп-слова
                manager = StopWordsManager()
                initial_count = len(manager.stopwords)
                assert initial_count == 2

                # Шаг 2: Имитируем импорт из файла
                with open(import_file, 'r', encoding='utf-8') as f:
                    import_content = f.read()

                # Парсим импортируемые слова
                imported_words = []
                for line in import_content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        imported_words.append(line.lower())

                # Удаляем дубликаты
                unique_imported = list(set(imported_words))

                # Добавляем импортированные слова
                for word in unique_imported:
                    manager.add_stopword(word)

                # Проверяем что слова добавились
                final_count = len(manager.stopwords)
                assert final_count == initial_count + len(unique_imported)

                # Шаг 3: Экспортируем все стоп-слова
                all_words = sorted(manager.stopwords)

                with open(export_file, 'w', encoding='utf-8') as f:
                    f.write("# Экспортированные стоп-слова\n")
                    f.write(f"# Всего слов: {len(all_words)}\n")
                    f.write(f"# Дата экспорта: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                    for word in all_words:
                        f.write(f"{word}\n")

                # Шаг 4: Проверяем экспортированный файл
                with open(export_file, 'r', encoding='utf-8') as f:
                    export_content = f.read()

                # Проверяем что все слова присутствуют
                assert "основное_слово1" in export_content
                assert "основное_слово2" in export_content
                assert "импорт_слово1" in export_content
                assert "импорт_слово2" in export_content
                assert "импорт_слово3" in export_content
                assert "дубликат" in export_content

                # Проверяем что дубликат встречается только один раз
                assert export_content.count("дубликат") == 1

                # Шаг 5: Тестируем реимпорт экспортированного файла
                reimport_manager = StopWordsManager()

                # Парсим экспортированный файл
                reimported_words = []
                for line in export_content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        reimported_words.append(line)

                # Проверяем консистентность
                assert len(reimported_words) == len(all_words)
                assert set(reimported_words) == set(all_words)

    @pytest.mark.asyncio
    async def test_backup_and_restore_operations(self):
        """Тест операций резервного копирования и восстановления"""
        with tempfile.TemporaryDirectory() as temp_dir:
            main_file = Path(temp_dir) / "main_stopwords.txt"
            backup_file = Path(temp_dir) / "backup_stopwords.txt"

            # Создаем основной файл
            original_words = ["слово1", "слово2", "слово3"]

            with open(main_file, 'w', encoding='utf-8') as f:
                f.write("# Основные стоп-слова\n")
                for word in original_words:
                    f.write(f"{word}\n")

            with patch('src.utils.stopwords.STOPWORDS_FILE', str(main_file)):
                manager = StopWordsManager()

                # Создаем резервную копию
                import shutil
                shutil.copy2(main_file, backup_file)

                # Проверяем что резервная копия создана
                assert backup_file.exists()

                # Изменяем основной файл
                manager.add_stopword("новое_слово")
                manager.remove_stopword("слово2")

                # Проверяем изменения
                modified_words = set(manager.stopwords)
                expected_modified = {"слово1", "слово3", "новое_слово"}
                assert modified_words == expected_modified

                # Имитируем критическую ошибку - восстанавливаем из резервной копии
                shutil.copy2(backup_file, main_file)

                # Перезагружаем менеджер
                restored_manager = StopWordsManager()
                restored_words = set(restored_manager.stopwords)

                # Проверяем что данные восстановились
                assert restored_words == set(original_words)
                assert "новое_слово" not in restored_words
                assert "слово2" in restored_words

    @pytest.mark.asyncio
    async def test_concurrent_file_access(self):
        """Тест одновременного доступа к файлам"""
        with tempfile.TemporaryDirectory() as temp_dir:
            shared_file = Path(temp_dir) / "shared_stopwords.txt"

            # Создаем общий файл
            with open(shared_file, 'w', encoding='utf-8') as f:
                f.write("# Общий файл стоп-слов\n")
                f.write("общее_слово\n")

            with patch('src.utils.stopwords.STOPWORDS_FILE', str(shared_file)):
                # Создаем несколько менеджеров для имитации разных процессов
                managers = [StopWordsManager() for _ in range(3)]

                # Функция для работы с файлом в отдельном потоке
                def file_worker(manager: StopWordsManager, worker_id: int):
                    results = []

                    for i in range(5):
                        try:
                            # Добавляем слово
                            word = f"слово_{worker_id}_{i}"
                            add_result = manager.add_stopword(word)
                            results.append(('add', word, add_result))

                            # Небольшая задержка
                            time.sleep(0.01)

                            # Перезагружаем для синхронизации
                            reload_count = manager.reload_stopwords()
                            results.append(('reload', reload_count, True))

                            # Проверяем наличие слова
                            contains, found = manager.contains_stopwords(f"Текст с {word}")
                            results.append(('check', word, contains))

                        except Exception as e:
                            results.append(('error', str(e), False))

                    return results

                # Запускаем потоки
                import threading
                threads = []
                all_results = []

                for i, manager in enumerate(managers):
                    def worker_wrapper(mgr=manager, wid=i):
                        result = file_worker(mgr, wid)
                        all_results.append(result)

                    thread = threading.Thread(target=worker_wrapper)
                    threads.append(thread)
                    thread.start()

                # Ждем завершения всех потоков
                for thread in threads:
                    thread.join()

                # Проверяем результаты
                assert len(all_results) == 3

                # Проверяем финальное состояние файла
                final_manager = StopWordsManager()
                final_words = set(final_manager.stopwords)

                # Должно быть исходное слово плюс добавленные
                assert "общее_слово" in final_words
                assert len(final_words) > 1

                # Проверяем что файл не поврежден
                with open(shared_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                assert "# Общий файл стоп-слов" in content
                lines = [line.strip() for line in content.split('\n') if line.strip() and not line.startswith('#')]
                assert len(lines) == len(final_words)

    @pytest.mark.asyncio
    async def test_file_corruption_recovery(self):
        """Тест восстановления после повреждения файла"""
        with tempfile.TemporaryDirectory() as temp_dir:
            main_file = Path(temp_dir) / "corruptible_stopwords.txt"

            # Создаем нормальный файл
            with open(main_file, 'w', encoding='utf-8') as f:
                f.write("# Нормальный файл\n")
                f.write("слово1\n")
                f.write("слово2\n")

            with patch('src.utils.stopwords.STOPWORDS_FILE', str(main_file)):
                # Загружаем нормальный файл
                manager = StopWordsManager()
                assert len(manager.stopwords) == 2

                # Имитируем повреждение файла
                with open(main_file, 'w', encoding='utf-8') as f:
                    f.write("Поврежденные данные\x00\xFF\xFE")

                # Пытаемся перезагрузить
                try:
                    corrupted_manager = StopWordsManager()
                    # Если загрузка прошла, проверяем что получили пустой список
                    assert len(corrupted_manager.stopwords) == 0
                except Exception:
                    # Или получили исключение - это тоже нормально
                    pass

                # Восстанавливаем файл
                with open(main_file, 'w', encoding='utf-8') as f:
                    f.write("# Восстановленный файл\n")
                    f.write("восстановленное_слово\n")

                # Проверяем восстановление
                recovered_manager = StopWordsManager()
                assert len(recovered_manager.stopwords) == 1
                assert "восстановленное_слово" in recovered_manager.stopwords

    @pytest.mark.asyncio
    async def test_large_file_operations(self):
        """Тест операций с большими файлами"""
        with tempfile.TemporaryDirectory() as temp_dir:
            large_file = Path(temp_dir) / "large_stopwords.txt"

            # Создаем большой файл (10,000 стоп-слов)
            large_word_count = 10000

            with open(large_file, 'w', encoding='utf-8') as f:
                f.write("# Большой файл стоп-слов\n")
                f.write(f"# Содержит {large_word_count} слов\n\n")

                for i in range(large_word_count):
                    f.write(f"большое_слово_{i:05d}\n")

            with patch('src.utils.stopwords.STOPWORDS_FILE', str(large_file)):
                # Тест загрузки большого файла
                start_time = time.time()
                manager = StopWordsManager()
                load_time = time.time() - start_time

                assert len(manager.stopwords) == large_word_count
                assert load_time < 10.0  # Загрузка за разумное время

                # Тест поиска в большом файле
                start_time = time.time()
                contains, found = manager.contains_stopwords("Текст с большое_слово_05000 для поиска")
                search_time = time.time() - start_time

                assert contains is True
                assert "большое_слово_05000" in found
                assert search_time < 2.0  # Поиск за разумное время

                # Тест добавления в большой файл
                start_time = time.time()
                add_result = manager.add_stopword("дополнительное_слово")
                add_time = time.time() - start_time

                assert add_result is True
                assert add_time < 5.0  # Добавление за разумное время

                # Проверяем что файл остался корректным
                final_count = len(manager.stopwords)
                assert final_count == large_word_count + 1

[tool:pytest]
# Конфигурация pytest для PM Searcher Bot

# Директории с тестами
testpaths = tests

# Паттерны для поиска тестов
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Маркеры для группировки тестов
markers =
    unit: Юнит-тесты отдельных компонентов
    integration: Интеграционные тесты взаимодействия компонентов
    handlers: Тесты обработчиков aiogram
    security: Тесты безопасности
    performance: Тесты производительности
    slow: Медленные тесты
    network: Тесты требующие сетевого подключения
    file_operations: Тесты файловых операций

# Настройки asyncio
asyncio_mode = auto

# Минимальный уровень логирования для тестов
log_level = INFO

# Показывать локальные переменные при ошибках
showlocals = true

# Подробный вывод при ошибках
tb = short

# Настройки покрытия кода
addopts = 
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=90
    -v

# Игнорировать предупреждения от внешних библиотек
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

#!/usr/bin/env python3
"""
Тест авторизации сессии
"""

import asyncio
import sys
from pathlib import Path

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.config.settings import SESSIONS_DIR
from src.config.environment import env_config
from telethon import TelegramClient

async def test_session_auth():
    """Тест авторизации сессии"""
    print("🔍 Тест авторизации сессии...")
    print("=" * 50)
    
    # Получаем список файлов сессий
    if not SESSIONS_DIR.exists():
        print("❌ Директория сессий не найдена")
        return
    
    session_files = list(SESSIONS_DIR.glob('*.session'))
    print(f"📁 Найдено {len(session_files)} файлов сессий")
    
    for session_file in session_files:
        try:
            user_id = int(session_file.stem)
            print(f"\n🔍 Проверка сессии для пользователя {user_id}...")
            
            # Создаем клиент
            client = TelegramClient(str(session_file), env_config.API_ID, env_config.API_HASH)
            
            # Подключаемся
            print("🔌 Подключение к Telegram API...")
            await client.connect()
            
            # Проверяем авторизацию
            is_authorized = await client.is_user_authorized()
            print(f"🔐 Авторизован: {is_authorized}")
            
            if is_authorized:
                # Получаем информацию о пользователе
                me = await client.get_me()
                print(f"👤 Пользователь: {me.first_name} {me.last_name or ''}")
                print(f"📞 Телефон: {me.phone}")
                print(f"🆔 ID: {me.id}")
            else:
                print("❌ Сессия не авторизована")
            
            await client.disconnect()
            
        except ValueError:
            print(f"❌ Некорректное имя файла сессии: {session_file.name}")
        except Exception as e:
            print(f"❌ Ошибка проверки {session_file.name}: {e}")

if __name__ == "__main__":
    asyncio.run(test_session_auth())

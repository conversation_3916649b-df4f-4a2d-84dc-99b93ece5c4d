#!/usr/bin/env python3
"""
Интеграционные тесты для системы отслеживания времени ответа
"""

import asyncio
import pytest
import tempfile
import json
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
import sys

# Добавляем src в путь для импорта
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.config.response_time_manager import ResponseTimeManager
from src.monitoring.response_tracker import ResponseTracker
from src.config.monitoring_settings import MonitoringSettings


class TestResponseTimeTracking:
    """Тесты системы отслеживания времени ответа"""
    
    @pytest.fixture
    def temp_config_file(self):
        """Временный файл конфигурации"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config = {
                "global_settings": {
                    "enabled": True,
                    "default_response_time_minutes": 10,
                    "check_interval_seconds": 1,  # Быстрая проверка для тестов
                    "alert_before_deadline_minutes": 2
                },
                "account_specific_settings": {},
                "statistics": {
                    "total_messages_tracked": 0,
                    "total_responses_on_time": 0,
                    "total_responses_late": 0,
                    "total_missed_responses": 0
                },
                "last_updated": None,
                "updated_by": None
            }
            json.dump(config, f)
            f.flush()
            yield Path(f.name)
        Path(f.name).unlink()
    
    @pytest.fixture
    def temp_monitoring_config(self):
        """Временный файл настроек мониторинга"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config = {
                "group_chat_monitoring_enabled": False,  # Важно: групповые чаты отключены
                "private_chat_monitoring_enabled": True,  # Личные чаты включены
                "bot_filtering_enabled": True,
                "last_updated": None,
                "updated_by": None
            }
            json.dump(config, f)
            f.flush()
            yield Path(f.name)
        Path(f.name).unlink()
    
    @pytest.fixture
    def response_manager(self, temp_config_file):
        """Менеджер времени ответа с временным конфигом"""
        with patch('src.config.response_time_manager.RESPONSE_TIME_CONFIG_FILE', temp_config_file):
            manager = ResponseTimeManager()
            yield manager
    
    @pytest.fixture
    def monitoring_settings(self, temp_monitoring_config):
        """Настройки мониторинга с временным конфигом"""
        with patch('src.config.monitoring_settings.MONITORING_CONFIG_FILE', temp_monitoring_config):
            settings = MonitoringSettings()
            yield settings
    
    @pytest.fixture
    def mock_alert_callback(self):
        """Mock callback для алертов"""
        alerts = []
        
        async def callback(phone: str, alert_text: str):
            alerts.append({
                'phone': phone,
                'alert': alert_text,
                'timestamp': datetime.now()
            })
        
        callback.alerts = alerts
        return callback
    
    @pytest.fixture
    def response_tracker(self, mock_alert_callback):
        """Трекер времени ответа"""
        tracker = ResponseTracker(mock_alert_callback)
        yield tracker
        # Cleanup
        if tracker._running:
            asyncio.create_task(tracker.stop_monitoring())

    @pytest.mark.asyncio
    async def test_private_chat_tracking_enabled(self, response_tracker, response_manager, monitoring_settings):
        """Тест: отслеживание работает для личных чатов"""
        # Запускаем трекер
        await response_tracker.start_monitoring()
        
        # Сообщение из личного чата
        private_message = {
            'account_phone': '+************',
            'message_id': 12345,
            'sender_id': *********,
            'sender_name': 'Тестовый Лид',
            'chat_id': *********,
            'chat_title': 'Личный чат',
            'text': 'Привет! У меня есть вопрос.',
            'is_from_external': True,
            'chat_type': 'private'  # Важно: личный чат
        }
        
        # Проверяем, что мониторинг личных чатов включен
        assert monitoring_settings.should_monitor_chat('private') is True
        
        # Отслеживаем сообщение
        success = response_tracker.track_incoming_message(private_message)
        assert success is True
        
        # Проверяем, что сообщение добавлено в отслеживание
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 1
        
        await response_tracker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_group_chat_tracking_disabled(self, response_tracker, response_manager, monitoring_settings):
        """Тест: отслеживание НЕ работает для групповых чатов"""
        # Запускаем трекер
        await response_tracker.start_monitoring()
        
        # Сообщение из группового чата
        group_message = {
            'account_phone': '+************',
            'message_id': 12346,
            'sender_id': *********,
            'sender_name': 'Участник группы',
            'chat_id': *********,
            'chat_title': 'Тестовая группа',
            'text': 'Сообщение в группе',
            'is_from_external': True,
            'chat_type': 'group'  # Важно: групповой чат
        }
        
        # Проверяем, что мониторинг групповых чатов отключен
        assert monitoring_settings.should_monitor_chat('group') is False
        
        # Пытаемся отследить сообщение - должно быть отклонено
        success = response_tracker.track_incoming_message(group_message)
        assert success is False  # Система должна отклонить групповые чаты
        
        # Проверяем, что сообщение НЕ добавлено в отслеживание
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 0
        
        await response_tracker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_supergroup_chat_tracking_disabled(self, response_tracker, response_manager, monitoring_settings):
        """Тест: отслеживание НЕ работает для супергрупп"""
        await response_tracker.start_monitoring()
        
        supergroup_message = {
            'account_phone': '+************',
            'message_id': 12347,
            'sender_id': *********,
            'sender_name': 'Участник супергруппы',
            'chat_id': *********,
            'chat_title': 'Тестовая супергруппа',
            'text': 'Сообщение в супергруппе',
            'is_from_external': True,
            'chat_type': 'supergroup'  # Важно: супергруппа
        }
        
        # Проверяем, что мониторинг супергрупп отключен
        assert monitoring_settings.should_monitor_chat('supergroup') is False
        
        # Пытаемся отследить сообщение
        success = response_tracker.track_incoming_message(supergroup_message)
        assert success is False
        
        # Проверяем, что сообщение НЕ добавлено
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 0
        
        await response_tracker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_channel_tracking_disabled(self, response_tracker, response_manager, monitoring_settings):
        """Тест: отслеживание НЕ работает для каналов"""
        await response_tracker.start_monitoring()
        
        channel_message = {
            'account_phone': '+************',
            'message_id': 12348,
            'sender_id': *********,
            'sender_name': 'Подписчик канала',
            'chat_id': *********,
            'chat_title': 'Тестовый канал',
            'text': 'Комментарий в канале',
            'is_from_external': True,
            'chat_type': 'channel'  # Важно: канал
        }
        
        # Проверяем, что мониторинг каналов отключен
        assert monitoring_settings.should_monitor_chat('channel') is False
        
        # Пытаемся отследить сообщение
        success = response_tracker.track_incoming_message(supergroup_message)
        assert success is False
        
        await response_tracker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_bot_filtering_in_private_chats(self, response_tracker, response_manager, monitoring_settings):
        """Тест: фильтрация ботов в личных чатах"""
        await response_tracker.start_monitoring()
        
        # Сообщение от бота в личном чате
        bot_message = {
            'account_phone': '+************',
            'message_id': 12349,
            'sender_id': *********,  # ID бота
            'sender_name': 'TestBot',
            'chat_id': *********,
            'chat_title': 'Бот чат',
            'text': 'Автоматическое сообщение от бота',
            'is_from_external': True,
            'chat_type': 'private',
            'is_bot': True  # Важно: отправитель - бот
        }
        
        # Проверяем, что фильтрация ботов включена
        assert monitoring_settings.bot_filtering_enabled is True
        
        # Пытаемся отследить сообщение от бота
        success = response_tracker.track_incoming_message(bot_message)
        assert success is False  # Должно быть отклонено из-за фильтрации ботов
        
        # Проверяем, что сообщение НЕ добавлено
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 0
        
        await response_tracker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_self_message_filtering(self, response_tracker, response_manager):
        """Тест: фильтрация собственных сообщений менеджера"""
        await response_tracker.start_monitoring()
        
        # Сообщение от самого менеджера (исходящее)
        self_message = {
            'account_phone': '+************',
            'message_id': 12350,
            'sender_id': ************,  # ID самого аккаунта
            'sender_name': 'Менеджер',
            'chat_id': *********,
            'chat_title': 'Личный чат',
            'text': 'Ответ менеджера',
            'is_from_external': False,  # Важно: не внешнее сообщение
            'chat_type': 'private'
        }
        
        # Пытаемся отследить собственное сообщение
        success = response_tracker.track_incoming_message(self_message)
        assert success is False  # Должно быть отклонено
        
        # Проверяем, что сообщение НЕ добавлено
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 0
        
        await response_tracker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_response_tracking_workflow(self, response_tracker, response_manager, mock_alert_callback):
        """Тест: полный цикл отслеживания ответа"""
        await response_tracker.start_monitoring()
        
        # 1. Входящее сообщение от лида
        incoming_message = {
            'account_phone': '+************',
            'message_id': 12351,
            'sender_id': *********,
            'sender_name': 'Важный Лид',
            'chat_id': *********,
            'chat_title': 'VIP клиент',
            'text': 'Срочный вопрос по продукту!',
            'is_from_external': True,
            'chat_type': 'private'
        }
        
        # Отслеживаем входящее сообщение
        success = response_tracker.track_incoming_message(incoming_message)
        assert success is True
        
        # Проверяем, что сообщение в отслеживании
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 1
        
        # Ждем немного
        await asyncio.sleep(0.1)
        
        # 2. Исходящий ответ менеджера
        outgoing_response = {
            'account_phone': '+************',
            'chat_id': *********,
            'message_id': 12352,
            'text': 'Здравствуйте! Отвечаю на ваш вопрос...'
        }
        
        # Отслеживаем ответ
        response_found = response_tracker.track_outgoing_message(outgoing_response)
        assert response_found is True
        
        # Проверяем, что сообщение удалено из ожидающих
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 0
        
        # Проверяем статистику
        stats = response_manager.get_statistics()
        assert stats['total_responses_on_time'] >= 1
        
        await response_tracker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_deadline_alert_system(self, response_tracker, response_manager, mock_alert_callback):
        """Тест: система алертов о дедлайнах"""
        # Устанавливаем очень короткое время ответа для теста
        response_manager.set_default_response_time(1, *********)  # 1 минута
        response_manager.set_enabled(True, *********)
        
        await response_tracker.start_monitoring()
        
        # Входящее сообщение
        incoming_message = {
            'account_phone': '+************',
            'message_id': 12353,
            'sender_id': *********,
            'sender_name': 'Нетерпеливый Лид',
            'chat_id': *********,
            'chat_title': 'Срочный клиент',
            'text': 'Мне нужен ответ прямо сейчас!',
            'is_from_external': True,
            'chat_type': 'private'
        }
        
        # Отслеживаем сообщение
        success = response_tracker.track_incoming_message(incoming_message)
        assert success is True
        
        # Ждем достаточно долго для срабатывания алерта
        await asyncio.sleep(2)  # Ждем больше времени ответа
        
        # Проверяем, что алерт был отправлен
        assert len(mock_alert_callback.alerts) > 0
        
        # Проверяем содержимое алерта
        alert = mock_alert_callback.alerts[0]
        assert alert['phone'] == '+************'
        assert 'ПРОСРОЧЕН ОТВЕТ' in alert['alert']
        
        await response_tracker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_account_specific_response_time(self, response_tracker, response_manager):
        """Тест: индивидуальное время ответа для аккаунтов"""
        # Устанавливаем индивидуальное время для аккаунта
        test_phone = '+************'
        response_manager.set_account_response_time(test_phone, 5, *********)  # 5 минут
        response_manager.set_default_response_time(10, *********)  # 10 минут по умолчанию
        
        # Проверяем, что индивидуальное время применяется
        account_time = response_manager.get_response_time_for_account(test_phone)
        assert account_time == 5
        
        # Проверяем время для другого аккаунта (должно быть по умолчанию)
        other_time = response_manager.get_response_time_for_account('+***********')
        assert other_time == 10

    def test_chat_type_validation(self, monitoring_settings):
        """Тест: валидация типов чатов"""
        # Проверяем все поддерживаемые типы чатов
        assert monitoring_settings.should_monitor_chat('private') is True
        assert monitoring_settings.should_monitor_chat('group') is False
        assert monitoring_settings.should_monitor_chat('supergroup') is False
        assert monitoring_settings.should_monitor_chat('channel') is False
        
        # Проверяем неизвестный тип (должен использовать настройку групповых чатов)
        assert monitoring_settings.should_monitor_chat('unknown') is False

    @pytest.mark.asyncio
    async def test_multiple_pending_messages(self, response_tracker, response_manager):
        """Тест: обработка нескольких ожидающих сообщений"""
        await response_tracker.start_monitoring()
        
        # Добавляем несколько сообщений от разных лидов
        for i in range(3):
            message = {
                'account_phone': '+************',
                'message_id': 12354 + i,
                'sender_id': ********* + i,
                'sender_name': f'Лид {i+1}',
                'chat_id': ********* + i,
                'chat_title': f'Чат {i+1}',
                'text': f'Сообщение {i+1}',
                'is_from_external': True,
                'chat_type': 'private'
            }
            
            success = response_tracker.track_incoming_message(message)
            assert success is True
        
        # Проверяем, что все сообщения в отслеживании
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 3
        
        # Отвечаем на одно сообщение
        response = {
            'account_phone': '+************',
            'chat_id': *********,  # Первый чат
            'message_id': 12357,
            'text': 'Ответ на первое сообщение'
        }
        
        response_found = response_tracker.track_outgoing_message(response)
        assert response_found is True
        
        # Проверяем, что осталось 2 ожидающих сообщения
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 2
        
        await response_tracker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_system_disabled(self, response_tracker, response_manager):
        """Тест: система не работает когда отключена"""
        # Отключаем систему
        response_manager.set_enabled(False, *********)
        
        await response_tracker.start_monitoring()
        
        # Пытаемся отследить сообщение
        message = {
            'account_phone': '+************',
            'message_id': 12358,
            'sender_id': *********,
            'sender_name': 'Тестовый Лид',
            'chat_id': *********,
            'chat_title': 'Тестовый чат',
            'text': 'Сообщение при отключенной системе',
            'is_from_external': True,
            'chat_type': 'private'
        }
        
        # Система должна отклонить сообщение
        success = response_tracker.track_incoming_message(message)
        assert success is False
        
        # Проверяем, что сообщение НЕ добавлено
        status = response_tracker.get_status()
        assert status['pending_messages_count'] == 0
        
        await response_tracker.stop_monitoring()


if __name__ == "__main__":
    # Запуск тестов
    pytest.main([__file__, "-v"])

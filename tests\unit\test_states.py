"""
Тесты FSM состояний PM Searcher Bot

Покрывает все состояния FSM и переходы между ними
с проверкой корректности состояний.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from aiogram.fsm.state import State, StatesGroup
from aiogram.fsm.context import FSMContext

from src.bot.states import ConnectAccount, ManageStopwords, StopWordsManagement
from tests.utils.mock_factories import MockFactory


class TestConnectAccountStates:
    """Тесты состояний ConnectAccount"""
    
    def test_connect_account_states_exist(self):
        """Тест существования состояний подключения аккаунта"""
        assert hasattr(ConnectAccount, 'waiting_for_phone')
        assert hasattr(ConnectAccount, 'waiting_for_code')
        
        assert isinstance(ConnectAccount.waiting_for_phone, State)
        assert isinstance(ConnectAccount.waiting_for_code, State)
    
    def test_connect_account_states_names(self):
        """Тест имен состояний подключения аккаунта"""
        assert ConnectAccount.waiting_for_phone.state == "ConnectAccount:waiting_for_phone"
        assert ConnectAccount.waiting_for_code.state == "ConnectAccount:waiting_for_code"
    
    def test_connect_account_states_group(self):
        """Тест принадлежности состояний к группе"""
        assert ConnectAccount.waiting_for_phone.group == ConnectAccount
        assert ConnectAccount.waiting_for_code.group == ConnectAccount
    
    def test_connect_account_states_uniqueness(self):
        """Тест уникальности состояний"""
        states = [ConnectAccount.waiting_for_phone, ConnectAccount.waiting_for_code]
        state_names = [state.state for state in states]
        
        assert len(state_names) == len(set(state_names))  # Все имена уникальны
    
    def test_connect_account_inheritance(self):
        """Тест наследования от StatesGroup"""
        assert issubclass(ConnectAccount, StatesGroup)
        assert isinstance(ConnectAccount(), StatesGroup)


class TestManageStopwordsStates:
    """Тесты состояний ManageStopwords"""
    
    def test_manage_stopwords_states_exist(self):
        """Тест существования состояний управления стоп-словами"""
        assert hasattr(ManageStopwords, 'waiting_for_word_to_add')
        assert hasattr(ManageStopwords, 'waiting_for_word_to_remove')
        
        assert isinstance(ManageStopwords.waiting_for_word_to_add, State)
        assert isinstance(ManageStopwords.waiting_for_word_to_remove, State)
    
    def test_manage_stopwords_states_names(self):
        """Тест имен состояний управления стоп-словами"""
        assert ManageStopwords.waiting_for_word_to_add.state == "ManageStopwords:waiting_for_word_to_add"
        assert ManageStopwords.waiting_for_word_to_remove.state == "ManageStopwords:waiting_for_word_to_remove"
    
    def test_manage_stopwords_states_group(self):
        """Тест принадлежности состояний к группе"""
        assert ManageStopwords.waiting_for_word_to_add.group == ManageStopwords
        assert ManageStopwords.waiting_for_word_to_remove.group == ManageStopwords
    
    def test_manage_stopwords_inheritance(self):
        """Тест наследования от StatesGroup"""
        assert issubclass(ManageStopwords, StatesGroup)
        assert isinstance(ManageStopwords(), StatesGroup)


class TestStopWordsManagementStates:
    """Тесты расширенных состояний StopWordsManagement"""
    
    def test_stopwords_management_basic_states_exist(self):
        """Тест существования основных состояний ввода данных"""
        basic_states = [
            'waiting_for_new_stopword',
            'waiting_for_delete_confirmation',
            'waiting_for_search_query',
            'waiting_for_import_file'
        ]
        
        for state_name in basic_states:
            assert hasattr(StopWordsManagement, state_name)
            state = getattr(StopWordsManagement, state_name)
            assert isinstance(state, State)
    
    def test_stopwords_management_navigation_states_exist(self):
        """Тест существования состояний просмотра и навигации"""
        navigation_states = [
            'browsing_stopwords',
            'viewing_search_results'
        ]
        
        for state_name in navigation_states:
            assert hasattr(StopWordsManagement, state_name)
            state = getattr(StopWordsManagement, state_name)
            assert isinstance(state, State)
    
    def test_stopwords_management_advanced_states_exist(self):
        """Тест существования продвинутых состояний"""
        advanced_states = [
            'waiting_for_export_filename',
            'confirming_import'
        ]
        
        for state_name in advanced_states:
            assert hasattr(StopWordsManagement, state_name)
            state = getattr(StopWordsManagement, state_name)
            assert isinstance(state, State)
    
    def test_stopwords_management_states_names(self):
        """Тест имен состояний StopWordsManagement"""
        expected_states = {
            'waiting_for_new_stopword': "StopWordsManagement:waiting_for_new_stopword",
            'waiting_for_delete_confirmation': "StopWordsManagement:waiting_for_delete_confirmation",
            'waiting_for_search_query': "StopWordsManagement:waiting_for_search_query",
            'waiting_for_import_file': "StopWordsManagement:waiting_for_import_file",
            'browsing_stopwords': "StopWordsManagement:browsing_stopwords",
            'viewing_search_results': "StopWordsManagement:viewing_search_results",
            'waiting_for_export_filename': "StopWordsManagement:waiting_for_export_filename",
            'confirming_import': "StopWordsManagement:confirming_import"
        }
        
        for attr_name, expected_state_name in expected_states.items():
            state = getattr(StopWordsManagement, attr_name)
            assert state.state == expected_state_name
    
    def test_stopwords_management_states_group(self):
        """Тест принадлежности всех состояний к группе StopWordsManagement"""
        state_attributes = [
            'waiting_for_new_stopword',
            'waiting_for_delete_confirmation',
            'waiting_for_search_query',
            'waiting_for_import_file',
            'browsing_stopwords',
            'viewing_search_results',
            'waiting_for_export_filename',
            'confirming_import'
        ]
        
        for attr_name in state_attributes:
            state = getattr(StopWordsManagement, attr_name)
            assert state.group == StopWordsManagement
    
    def test_stopwords_management_states_count(self):
        """Тест количества состояний в StopWordsManagement"""
        # Получаем все атрибуты, которые являются состояниями
        states = [
            attr for attr in dir(StopWordsManagement)
            if isinstance(getattr(StopWordsManagement, attr), State)
        ]
        
        # Должно быть 8 состояний согласно документации
        assert len(states) == 8
    
    def test_stopwords_management_inheritance(self):
        """Тест наследования от StatesGroup"""
        assert issubclass(StopWordsManagement, StatesGroup)
        assert isinstance(StopWordsManagement(), StatesGroup)


class TestFSMContextIntegration:
    """Тесты интеграции состояний с FSMContext"""
    
    @pytest.mark.asyncio
    async def test_fsm_context_set_state_connect_account(self):
        """Тест установки состояний ConnectAccount через FSMContext"""
        context = MockFactory.create_fsm_context()
        
        # Тестируем установку состояния ожидания телефона
        await context.set_state(ConnectAccount.waiting_for_phone)
        context.set_state.assert_called_with(ConnectAccount.waiting_for_phone)
        
        # Тестируем установку состояния ожидания кода
        await context.set_state(ConnectAccount.waiting_for_code)
        context.set_state.assert_called_with(ConnectAccount.waiting_for_code)
    
    @pytest.mark.asyncio
    async def test_fsm_context_set_state_manage_stopwords(self):
        """Тест установки состояний ManageStopwords через FSMContext"""
        context = MockFactory.create_fsm_context()
        
        # Тестируем установку состояния добавления слова
        await context.set_state(ManageStopwords.waiting_for_word_to_add)
        context.set_state.assert_called_with(ManageStopwords.waiting_for_word_to_add)
        
        # Тестируем установку состояния удаления слова
        await context.set_state(ManageStopwords.waiting_for_word_to_remove)
        context.set_state.assert_called_with(ManageStopwords.waiting_for_word_to_remove)
    
    @pytest.mark.asyncio
    async def test_fsm_context_set_state_stopwords_management(self):
        """Тест установки состояний StopWordsManagement через FSMContext"""
        context = MockFactory.create_fsm_context()
        
        states_to_test = [
            StopWordsManagement.waiting_for_new_stopword,
            StopWordsManagement.waiting_for_search_query,
            StopWordsManagement.waiting_for_import_file,
            StopWordsManagement.browsing_stopwords,
            StopWordsManagement.confirming_import
        ]
        
        for state in states_to_test:
            await context.set_state(state)
            context.set_state.assert_called_with(state)
    
    @pytest.mark.asyncio
    async def test_fsm_context_clear_state(self):
        """Тест очистки состояния через FSMContext"""
        context = MockFactory.create_fsm_context()
        
        # Устанавливаем состояние
        await context.set_state(StopWordsManagement.waiting_for_new_stopword)
        
        # Очищаем состояние
        await context.clear()
        context.clear.assert_called()
    
    @pytest.mark.asyncio
    async def test_fsm_context_get_state(self):
        """Тест получения текущего состояния через FSMContext"""
        context = MockFactory.create_fsm_context()
        
        # Мокируем возврат состояния
        context.get_state.return_value = StopWordsManagement.waiting_for_new_stopword
        
        current_state = await context.get_state()
        assert current_state == StopWordsManagement.waiting_for_new_stopword
        context.get_state.assert_called()
    
    @pytest.mark.asyncio
    async def test_fsm_context_update_data(self):
        """Тест обновления данных в FSMContext"""
        context = MockFactory.create_fsm_context()
        
        test_data = {
            'stopword': 'тестовое_слово',
            'search_query': 'поиск',
            'import_filename': 'stopwords.txt'
        }
        
        await context.update_data(**test_data)
        context.update_data.assert_called_with(**test_data)
    
    @pytest.mark.asyncio
    async def test_fsm_context_get_data(self):
        """Тест получения данных из FSMContext"""
        context = MockFactory.create_fsm_context()
        
        # Мокируем возврат данных
        expected_data = {'stopword': 'тестовое_слово'}
        context.get_data.return_value = expected_data
        
        data = await context.get_data()
        assert data == expected_data
        context.get_data.assert_called()


class TestStatesValidation:
    """Тесты валидации состояний"""
    
    def test_all_states_have_unique_names(self):
        """Тест уникальности имен всех состояний"""
        all_states = []
        
        # Собираем все состояния из всех групп
        state_groups = [ConnectAccount, ManageStopwords, StopWordsManagement]
        
        for group in state_groups:
            for attr_name in dir(group):
                attr = getattr(group, attr_name)
                if isinstance(attr, State):
                    all_states.append(attr.state)
        
        # Проверяем уникальность
        assert len(all_states) == len(set(all_states))
    
    def test_states_naming_convention(self):
        """Тест соблюдения соглашений об именовании состояний"""
        state_groups = [ConnectAccount, ManageStopwords, StopWordsManagement]
        
        for group in state_groups:
            group_name = group.__name__
            
            for attr_name in dir(group):
                attr = getattr(group, attr_name)
                if isinstance(attr, State):
                    # Проверяем что имя состояния начинается с имени группы
                    assert attr.state.startswith(f"{group_name}:")
                    
                    # Проверяем что имя атрибута соответствует части после двоеточия
                    expected_suffix = attr_name
                    actual_suffix = attr.state.split(':', 1)[1]
                    assert actual_suffix == expected_suffix
    
    def test_states_documentation_consistency(self):
        """Тест соответствия состояний документации"""
        # Проверяем что все документированные состояния существуют
        documented_stopwords_states = [
            'waiting_for_new_stopword',
            'waiting_for_delete_confirmation',
            'waiting_for_search_query',
            'waiting_for_import_file',
            'browsing_stopwords',
            'viewing_search_results',
            'waiting_for_export_filename',
            'confirming_import'
        ]
        
        for state_name in documented_stopwords_states:
            assert hasattr(StopWordsManagement, state_name)
            state = getattr(StopWordsManagement, state_name)
            assert isinstance(state, State)
    
    def test_states_groups_isolation(self):
        """Тест изоляции групп состояний"""
        # Проверяем что состояния из разных групп не пересекаются
        connect_states = [
            attr for attr in dir(ConnectAccount)
            if isinstance(getattr(ConnectAccount, attr), State)
        ]
        
        manage_states = [
            attr for attr in dir(ManageStopwords)
            if isinstance(getattr(ManageStopwords, attr), State)
        ]
        
        stopwords_mgmt_states = [
            attr for attr in dir(StopWordsManagement)
            if isinstance(getattr(StopWordsManagement, attr), State)
        ]
        
        # Проверяем что нет пересечений в именах атрибутов
        all_state_names = connect_states + manage_states + stopwords_mgmt_states
        assert len(all_state_names) == len(set(all_state_names))


class TestStatesEdgeCases:
    """Тесты граничных случаев для состояний"""
    
    def test_states_memory_usage(self):
        """Тест использования памяти состояниями"""
        # Создаем много экземпляров групп состояний
        groups = []
        for i in range(100):
            groups.append(ConnectAccount())
            groups.append(ManageStopwords())
            groups.append(StopWordsManagement())
        
        # Проверяем что все экземпляры созданы корректно
        assert len(groups) == 300
        
        for group in groups:
            assert isinstance(group, StatesGroup)
    
    def test_states_serialization(self):
        """Тест сериализации состояний"""
        states_to_test = [
            ConnectAccount.waiting_for_phone,
            ManageStopwords.waiting_for_word_to_add,
            StopWordsManagement.waiting_for_new_stopword
        ]
        
        for state in states_to_test:
            # Проверяем что состояние можно преобразовать в строку
            state_str = str(state)
            assert isinstance(state_str, str)
            assert len(state_str) > 0
            
            # Проверяем что состояние имеет корректное строковое представление
            assert state.state in state_str
    
    def test_states_comparison(self):
        """Тест сравнения состояний"""
        # Проверяем что одинаковые состояния равны
        assert ConnectAccount.waiting_for_phone == ConnectAccount.waiting_for_phone
        assert StopWordsManagement.waiting_for_new_stopword == StopWordsManagement.waiting_for_new_stopword
        
        # Проверяем что разные состояния не равны
        assert ConnectAccount.waiting_for_phone != ConnectAccount.waiting_for_code
        assert ManageStopwords.waiting_for_word_to_add != ManageStopwords.waiting_for_word_to_remove
        
        # Проверяем что состояния из разных групп не равны
        assert ConnectAccount.waiting_for_phone != StopWordsManagement.waiting_for_new_stopword
    
    def test_states_hash_consistency(self):
        """Тест консистентности хеширования состояний"""
        states_to_test = [
            ConnectAccount.waiting_for_phone,
            ConnectAccount.waiting_for_code,
            ManageStopwords.waiting_for_word_to_add,
            ManageStopwords.waiting_for_word_to_remove,
            StopWordsManagement.waiting_for_new_stopword,
            StopWordsManagement.waiting_for_search_query
        ]
        
        # Проверяем что состояния можно хешировать
        state_hashes = set()
        for state in states_to_test:
            state_hash = hash(state)
            assert isinstance(state_hash, int)
            state_hashes.add(state_hash)
        
        # Проверяем что хеши уникальны
        assert len(state_hashes) == len(states_to_test)

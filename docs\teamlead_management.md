# Управление тимлидами

Система управления тимлидами позволяет главным администраторам назначать конкретных пользователей (тимлидов) для получения уведомлений от определенных номеров телефонов.

## Основные возможности

### 1. Добавление тимлидов
- Главные администраторы могут добавлять новых тимлидов
- Для добавления нужен никнейм (без @) и Telegram ID пользователя
- Тимлиды сохраняются в файле `data/teamleads_config.json`

### 2. Назначение номеров телефонов
- Каждому тимлиду можно назначить один или несколько номеров телефонов
- Алерты от назначенных номеров будут приходить только этому тимлиду
- Если номер не назначен никому, алерты идут всем главным администраторам

### 3. Система маршрутизации алертов
- **Текстовые алерты**: отправляются через `send_alert_with_phone_routing()`
- **Фото алерты**: отправляются через `send_photo_alert_with_phone_routing()`
- Fallback: если тимлиды не назначены, алерты идут всем админам

## Команды для управления

### Основные команды
- `/add_teamlead` - Добавить нового тимлида
- `/assign_phone` - Назначить номера тимлиду
- `/list_teamleads` - Список всех тимлидов
- `/view_assignments` - Просмотр всех назначений
- `/remove_teamlead` - Удалить тимлида

### Дополнительные команды
- `/unassign_phone` - Отменить назначение номера
- `/teamlead_stats` - Детальная статистика
- `/clear_all_assignments` - Очистить все назначения

### Интерфейс
- Кнопка "👥 Управление тимлидами" в админской клавиатуре
- Интерактивные inline-кнопки для удобного управления
- Подтверждения для критических операций

## Структура данных

### Файл конфигурации: `data/teamleads_config.json`
```json
{
  "teamleads": {
    "тимлид1": {
      "user_id": 123456789,
      "assigned_phones": ["+79991234567", "+79997654321"],
      "added_by": 7738438019,
      "added_at": "2025-07-29T20:00:00"
    }
  },
  "phone_assignments": {
    "+79991234567": ["тимлид1"],
    "+79997654321": ["тимлид1"]
  },
  "last_updated": "2025-07-29T20:00:00",
  "updated_by": 7738438019
}
```

## Примеры использования

### 1. Добавление тимлида
1. Выполните `/add_teamlead`
2. Введите никнейм: `тимлид1`
3. Введите Telegram ID: `123456789`
4. Тимлид добавлен в систему

### 2. Назначение номеров
1. Выполните `/assign_phone`
2. Выберите тимлида из списка
3. Выберите номера для назначения
4. Подтвердите выбор

### 3. Работа алертов
- Алерт от номера `+79991234567` → отправляется тимлиду1
- Алерт от номера `+79995555555` (не назначен) → отправляется всем админам

## Права доступа

### Главные администраторы (ADMIN_CHAT_ID)
- Могут добавлять/удалять тимлидов
- Могут назначать/отменять назначения номеров
- Получают алерты от неназначенных номеров

### Тимлиды
- Получают алерты только от назначенных им номеров
- Не могут управлять другими тимлидами
- Не имеют доступа к админским функциям

## Логирование

Все операции с тимлидами логируются:
- Добавление/удаление тимлидов
- Назначение/отмена назначений
- Отправка алертов с маршрутизацией

Логи сохраняются в:
- `data/logs/teamlead_management.log`
- `data/logs/teamlead_removal.log`
- `data/logs/teamlead_callbacks.log`
- `data/logs/alerts.log`

## Безопасность

### Проверки доступа
- Все команды управления доступны только главным администраторам
- Проверка прав на каждом этапе операции
- Подтверждения для критических действий (удаление, очистка)

### Валидация данных
- Проверка существования тимлидов
- Валидация Telegram ID
- Проверка доступности номеров телефонов

## Интеграция с существующей системой

### AlertManager
- Добавлены новые методы маршрутизации
- Сохранена обратная совместимость
- Fallback на старую систему при ошибках

### TelethonManager
- Модифицирован `_create_alert_callback()`
- Обновлены обработчики фото алертов
- Сохранена совместимость с существующим кодом

### Клавиатуры
- Добавлена кнопка в админскую клавиатуру
- Новые inline-клавиатуры для управления
- Интуитивный интерфейс с подтверждениями

## Миграция и обновление

При первом запуске:
1. Создается файл `data/teamleads_config.json`
2. Все существующие алерты продолжают работать как раньше
3. Новые назначения начинают действовать сразу

Обратная совместимость:
- Старые методы AlertManager продолжают работать
- Существующие обработчики не затронуты
- Плавный переход на новую систему

#!/usr/bin/env python3
"""
Тест для проверки исправления ошибки обновления списка аккаунтов
"""

import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.bot.handlers.account import _generate_accounts_list_text_and_keyboard
from src.bot.keyboards import accounts_management_keyboard


def test_generate_accounts_list_text_and_keyboard():
    """Тест вспомогательной функции генерации текста и клавиатуры"""
    print("🧪 Тестирование функции _generate_accounts_list_text_and_keyboard...")
    
    # Тест с пустым списком аккаунтов
    print("\n1. Тест с пустым списком аккаунтов:")
    accounts = []
    user_id = *********
    
    text, keyboard = _generate_accounts_list_text_and_keyboard(accounts, user_id)
    print(f"   Текст: {text[:50]}...")
    print(f"   Клавиатура создана: {keyboard is not None}")
    
    # Тест с пустым списком аккаунтов и временной меткой
    print("\n2. Тест с пустым списком аккаунтов и временной меткой:")
    text_with_time, keyboard_with_time = _generate_accounts_list_text_and_keyboard(
        accounts, user_id, include_timestamp=True
    )
    print(f"   Текст: {text_with_time[:50]}...")
    print(f"   Содержит временную метку: {'🕐 Обновлено:' in text_with_time}")
    print(f"   Клавиатура создана: {keyboard_with_time is not None}")
    
    # Тест с аккаунтами
    print("\n3. Тест с аккаунтами:")
    accounts_with_data = [
        {
            'phone': '+***********',
            'monitoring': True
        },
        {
            'phone': '+***********',
            'monitoring': False
        }
    ]
    
    text_accounts, keyboard_accounts = _generate_accounts_list_text_and_keyboard(
        accounts_with_data, user_id
    )
    print(f"   Текст: {text_accounts[:80]}...")
    print(f"   Содержит количество аккаунтов: {'Всего аккаунтов: 2' in text_accounts}")
    print(f"   Клавиатура создана: {keyboard_accounts is not None}")
    
    # Тест с аккаунтами и временной меткой
    print("\n4. Тест с аккаунтами и временной меткой:")
    text_accounts_time, keyboard_accounts_time = _generate_accounts_list_text_and_keyboard(
        accounts_with_data, user_id, include_timestamp=True
    )
    print(f"   Текст: {text_accounts_time[:80]}...")
    print(f"   Содержит временную метку: {'🕐 Обновлено:' in text_accounts_time}")
    print(f"   Клавиатура создана: {keyboard_accounts_time is not None}")
    
    print("\n✅ Все тесты пройдены успешно!")


def test_timestamp_uniqueness():
    """Тест уникальности временных меток"""
    print("\n🧪 Тестирование уникальности временных меток...")
    
    accounts = []
    user_id = *********
    
    # Генерируем несколько текстов подряд
    texts = []
    for i in range(3):
        text, _ = _generate_accounts_list_text_and_keyboard(
            accounts, user_id, include_timestamp=True
        )
        texts.append(text)
        # Небольшая задержка для изменения времени
        import time
        time.sleep(1)
    
    # Проверяем, что тексты разные (из-за разных временных меток)
    unique_texts = set(texts)
    print(f"   Сгенерировано текстов: {len(texts)}")
    print(f"   Уникальных текстов: {len(unique_texts)}")
    
    if len(unique_texts) == len(texts):
        print("   ✅ Все тексты уникальны благодаря временным меткам")
    else:
        print("   ⚠️ Некоторые тексты одинаковые")
    
    return len(unique_texts) == len(texts)


def main():
    """Основная функция тестирования"""
    print("🚀 Запуск тестов исправления ошибки обновления списка аккаунтов")
    print("=" * 70)
    
    try:
        # Тест основной функции
        test_generate_accounts_list_text_and_keyboard()
        
        # Тест уникальности временных меток
        timestamp_test_passed = test_timestamp_uniqueness()
        
        print("\n" + "=" * 70)
        print("📊 Результаты тестирования:")
        print("✅ Функция генерации текста и клавиатуры работает корректно")
        print(f"{'✅' if timestamp_test_passed else '❌'} Временные метки обеспечивают уникальность")
        
        if timestamp_test_passed:
            print("\n🎉 Все тесты пройдены! Исправление должно работать корректно.")
            print("\n💡 Теперь при нажатии кнопки 'Обновить список' ошибка")
            print("   'message is not modified' больше не должна возникать.")
        else:
            print("\n⚠️ Некоторые тесты не прошли. Требуется дополнительная проверка.")
            
    except Exception as e:
        print(f"\n❌ Ошибка при выполнении тестов: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

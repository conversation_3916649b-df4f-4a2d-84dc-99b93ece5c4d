"""
Менеджер для управления тимлидами и их назначениями на номера телефонов
"""

import json
import logging
from typing import Dict, List, Optional, Set
from pathlib import Path
from datetime import datetime
from .settings import TEAMLEADS_CONFIG_FILE

logger = logging.getLogger(__name__)

class TeamleadsManager:
    """Класс для управления тимлидами и их назначениями"""
    
    def __init__(self):
        self.config_file = TEAMLEADS_CONFIG_FILE
        self._config = self._load_config()
    
    def _load_config(self) -> Dict:
        """Загрузка конфигурации тимлидов из файла"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"Конфигурация тимлидов загружена из {self.config_file}")
                return config
            else:
                # Конфигурация по умолчанию
                default_config = {
                    "teamleads": {},  # username: {"user_id": int, "assigned_phones": []}
                    "phone_assignments": {},  # phone: [usernames]
                    "last_updated": None,
                    "updated_by": None
                }
                self._save_config(default_config)
                logger.info("Создана конфигурация тимлидов по умолчанию")
                return default_config
        except Exception as e:
            logger.error(f"Ошибка загрузки конфигурации тимлидов: {e}")
            # Возвращаем конфигурацию по умолчанию при ошибке
            return {
                "teamleads": {},
                "phone_assignments": {},
                "last_updated": None,
                "updated_by": None
            }
    
    def _save_config(self, config: Dict = None) -> bool:
        """Сохранение конфигурации в файл"""
        try:
            config_to_save = config or self._config
            config_to_save["last_updated"] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            
            if config:
                self._config = config
            
            logger.info(f"Конфигурация тимлидов сохранена в {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"Ошибка сохранения конфигурации тимлидов: {e}")
            return False
    
    def add_teamlead(self, username: str, user_id: int, updated_by: int = None) -> bool:
        """
        Добавление нового тимлида
        
        Args:
            username (str): Никнейм тимлида (без @)
            user_id (int): Telegram ID тимлида
            updated_by (int): ID администратора, который добавил тимлида
            
        Returns:
            bool: True если тимлид успешно добавлен
        """
        try:
            # Убираем @ если есть
            username = username.lstrip('@').lower()
            
            if username in self._config["teamleads"]:
                logger.warning(f"Тимлид {username} уже существует")
                return False
            
            self._config["teamleads"][username] = {
                "user_id": user_id,
                "assigned_phones": [],
                "added_by": updated_by,
                "added_at": datetime.now().isoformat()
            }
            
            self._config["updated_by"] = updated_by
            
            success = self._save_config()
            if success:
                logger.info(f"Тимлид {username} (ID: {user_id}) добавлен администратором {updated_by}")
            
            return success
        except Exception as e:
            logger.error(f"Ошибка добавления тимлида {username}: {e}")
            return False
    
    def remove_teamlead(self, username: str, updated_by: int = None) -> bool:
        """
        Удаление тимлида
        
        Args:
            username (str): Никнейм тимлида
            updated_by (int): ID администратора, который удалил тимлида
            
        Returns:
            bool: True если тимлид успешно удален
        """
        try:
            username = username.lstrip('@').lower()
            
            if username not in self._config["teamleads"]:
                logger.warning(f"Тимлид {username} не найден")
                return False
            
            # Удаляем все назначения этого тимлида
            assigned_phones = self._config["teamleads"][username]["assigned_phones"].copy()
            for phone in assigned_phones:
                self.unassign_phone_from_teamlead(phone, username, updated_by)
            
            # Удаляем тимлида
            del self._config["teamleads"][username]
            self._config["updated_by"] = updated_by
            
            success = self._save_config()
            if success:
                logger.info(f"Тимлид {username} удален администратором {updated_by}")
            
            return success
        except Exception as e:
            logger.error(f"Ошибка удаления тимлида {username}: {e}")
            return False
    
    def assign_phone_to_teamlead(self, phone: str, username: str, updated_by: int = None) -> bool:
        """
        Назначение номера телефона тимлиду
        
        Args:
            phone (str): Номер телефона
            username (str): Никнейм тимлида
            updated_by (int): ID администратора, который сделал назначение
            
        Returns:
            bool: True если назначение успешно
        """
        try:
            username = username.lstrip('@').lower()
            
            if username not in self._config["teamleads"]:
                logger.warning(f"Тимлид {username} не найден")
                return False
            
            # Добавляем номер к тимлиду
            if phone not in self._config["teamleads"][username]["assigned_phones"]:
                self._config["teamleads"][username]["assigned_phones"].append(phone)
            
            # Добавляем тимлида к номеру
            if phone not in self._config["phone_assignments"]:
                self._config["phone_assignments"][phone] = []
            
            if username not in self._config["phone_assignments"][phone]:
                self._config["phone_assignments"][phone].append(username)
            
            self._config["updated_by"] = updated_by
            
            success = self._save_config()
            if success:
                logger.info(f"Номер {phone} назначен тимлиду {username} администратором {updated_by}")
            
            return success
        except Exception as e:
            logger.error(f"Ошибка назначения номера {phone} тимлиду {username}: {e}")
            return False
    
    def unassign_phone_from_teamlead(self, phone: str, username: str, updated_by: int = None) -> bool:
        """
        Отмена назначения номера телефона тимлиду
        
        Args:
            phone (str): Номер телефона
            username (str): Никнейм тимлида
            updated_by (int): ID администратора, который отменил назначение
            
        Returns:
            bool: True если отмена назначения успешна
        """
        try:
            username = username.lstrip('@').lower()
            
            # Удаляем номер у тимлида
            if username in self._config["teamleads"]:
                if phone in self._config["teamleads"][username]["assigned_phones"]:
                    self._config["teamleads"][username]["assigned_phones"].remove(phone)
            
            # Удаляем тимлида у номера
            if phone in self._config["phone_assignments"]:
                if username in self._config["phone_assignments"][phone]:
                    self._config["phone_assignments"][phone].remove(username)
                
                # Если у номера не осталось тимлидов, удаляем запись
                if not self._config["phone_assignments"][phone]:
                    del self._config["phone_assignments"][phone]
            
            self._config["updated_by"] = updated_by
            
            success = self._save_config()
            if success:
                logger.info(f"Назначение номера {phone} тимлиду {username} отменено администратором {updated_by}")
            
            return success
        except Exception as e:
            logger.error(f"Ошибка отмены назначения номера {phone} тимлиду {username}: {e}")
            return False
    
    def get_teamleads_for_phone(self, phone: str) -> List[Dict]:
        """
        Получение списка тимлидов, назначенных на номер
        
        Args:
            phone (str): Номер телефона
            
        Returns:
            List[Dict]: Список тимлидов с их данными
        """
        try:
            if phone not in self._config["phone_assignments"]:
                return []
            
            teamleads = []
            for username in self._config["phone_assignments"][phone]:
                if username in self._config["teamleads"]:
                    teamlead_data = self._config["teamleads"][username].copy()
                    teamlead_data["username"] = username
                    teamleads.append(teamlead_data)
            
            return teamleads
        except Exception as e:
            logger.error(f"Ошибка получения тимлидов для номера {phone}: {e}")
            return []
    
    def get_phones_for_teamlead(self, username: str) -> List[str]:
        """
        Получение списка номеров, назначенных тимлиду
        
        Args:
            username (str): Никнейм тимлида
            
        Returns:
            List[str]: Список номеров телефонов
        """
        try:
            username = username.lstrip('@').lower()
            
            if username not in self._config["teamleads"]:
                return []
            
            return self._config["teamleads"][username]["assigned_phones"].copy()
        except Exception as e:
            logger.error(f"Ошибка получения номеров для тимлида {username}: {e}")
            return []
    
    def get_all_teamleads(self) -> Dict[str, Dict]:
        """
        Получение всех тимлидов
        
        Returns:
            Dict[str, Dict]: Словарь всех тимлидов
        """
        return self._config["teamleads"].copy()
    
    def get_all_assignments(self) -> Dict[str, List[str]]:
        """
        Получение всех назначений номеров
        
        Returns:
            Dict[str, List[str]]: Словарь назначений (номер -> список тимлидов)
        """
        return self._config["phone_assignments"].copy()
    
    def teamlead_exists(self, username: str) -> bool:
        """
        Проверка существования тимлида
        
        Args:
            username (str): Никнейм тимлида
            
        Returns:
            bool: True если тимлид существует
        """
        username = username.lstrip('@').lower()
        return username in self._config["teamleads"]
    
    def get_teamlead_user_id(self, username: str) -> Optional[int]:
        """
        Получение Telegram ID тимлида по никнейму
        
        Args:
            username (str): Никнейм тимлида
            
        Returns:
            Optional[int]: Telegram ID или None если тимлид не найден
        """
        username = username.lstrip('@').lower()
        if username in self._config["teamleads"]:
            return self._config["teamleads"][username]["user_id"]
        return None

# Глобальный экземпляр менеджера
teamleads_manager = TeamleadsManager()
